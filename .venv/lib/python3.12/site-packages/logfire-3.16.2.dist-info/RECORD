../../../bin/logfire,sha256=D3Rzld8Ak_92FkdxGBFClJfdU9NA16UnrDDRj_xCKlQ,242
logfire-3.16.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
logfire-3.16.2.dist-info/METADATA,sha256=5xjCELLn-2QQeySjBV_vlJ39s5WfT2uqEUsV1xpBh4U,9295
logfire-3.16.2.dist-info/RECORD,,
logfire-3.16.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
logfire-3.16.2.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
logfire-3.16.2.dist-info/entry_points.txt,sha256=QNJyTjDTD2CmK-GewKe7uu3MzwKxdfD7iz8E0zG70xQ,149
logfire-3.16.2.dist-info/licenses/LICENSE,sha256=A1UHuKHnh6FsfxOPCSVXowqO6cyc726E0RVG7juaRc0,1099
logfire/__init__.py,sha256=W9qgwyyTra24MMI2AREVL1v23QcN5qi84Ti_pt6wYvw,5826
logfire/__main__.py,sha256=9pQVkauM3ZyhuWMpJTqQMeTKTdRzem8ZGxINZOIhOhw,89
logfire/__pycache__/__init__.cpython-312.pyc,,
logfire/__pycache__/__main__.cpython-312.pyc,,
logfire/__pycache__/cli.cpython-312.pyc,,
logfire/__pycache__/exceptions.cpython-312.pyc,,
logfire/__pycache__/propagate.cpython-312.pyc,,
logfire/__pycache__/testing.cpython-312.pyc,,
logfire/__pycache__/version.cpython-312.pyc,,
logfire/_internal/__init__.py,sha256=v0hLWJ_cy1BAvsygpKL0XJsc0Z5qrE7iJ33cr_XRA10,183
logfire/_internal/__pycache__/__init__.cpython-312.pyc,,
logfire/_internal/__pycache__/ast_utils.cpython-312.pyc,,
logfire/_internal/__pycache__/async_.cpython-312.pyc,,
logfire/_internal/__pycache__/auth.cpython-312.pyc,,
logfire/_internal/__pycache__/cli.cpython-312.pyc,,
logfire/_internal/__pycache__/collect_system_info.cpython-312.pyc,,
logfire/_internal/__pycache__/config.cpython-312.pyc,,
logfire/_internal/__pycache__/config_params.cpython-312.pyc,,
logfire/_internal/__pycache__/constants.cpython-312.pyc,,
logfire/_internal/__pycache__/db_statement_summary.cpython-312.pyc,,
logfire/_internal/__pycache__/formatter.cpython-312.pyc,,
logfire/_internal/__pycache__/instrument.cpython-312.pyc,,
logfire/_internal/__pycache__/json_encoder.cpython-312.pyc,,
logfire/_internal/__pycache__/json_formatter.cpython-312.pyc,,
logfire/_internal/__pycache__/json_schema.cpython-312.pyc,,
logfire/_internal/__pycache__/json_types.cpython-312.pyc,,
logfire/_internal/__pycache__/logs.cpython-312.pyc,,
logfire/_internal/__pycache__/main.cpython-312.pyc,,
logfire/_internal/__pycache__/metrics.cpython-312.pyc,,
logfire/_internal/__pycache__/scrubbing.cpython-312.pyc,,
logfire/_internal/__pycache__/stack_info.cpython-312.pyc,,
logfire/_internal/__pycache__/tracer.cpython-312.pyc,,
logfire/_internal/__pycache__/ulid.cpython-312.pyc,,
logfire/_internal/__pycache__/utils.cpython-312.pyc,,
logfire/_internal/ast_utils.py,sha256=4N8nNORaNYkbDwO3wYpIT182i4aSXm0M9hLwcKOOW7U,5319
logfire/_internal/async_.py,sha256=Jw7miWMfDUb2DjoBRMpGokvnbyDjPE7VD5qQl7VUzDQ,4795
logfire/_internal/auth.py,sha256=T5DGygQbsWD_cFTuNoeDYiVtFGL328d1C4LQxKcb1mU,3534
logfire/_internal/auto_trace/__init__.py,sha256=VgH83rhBjb3V_lWh7bEgxFrN69CI29XLIGVKc5TPT5o,2688
logfire/_internal/auto_trace/__pycache__/__init__.cpython-312.pyc,,
logfire/_internal/auto_trace/__pycache__/import_hook.cpython-312.pyc,,
logfire/_internal/auto_trace/__pycache__/rewrite_ast.cpython-312.pyc,,
logfire/_internal/auto_trace/__pycache__/types.cpython-312.pyc,,
logfire/_internal/auto_trace/import_hook.py,sha256=hsmLEOQfsV4n8FYm-q4cUdYe6BDaEZa-vP88mcnrLGg,5920
logfire/_internal/auto_trace/rewrite_ast.py,sha256=ai6abT9Tb0DfMSbyyO496RRYCtnnTz885D5yHHF2DRw,7215
logfire/_internal/auto_trace/types.py,sha256=nGZ-OnoDSOOpXtN6CSS1Izoa8JGNgS-P7pZYRxnWEEg,1789
logfire/_internal/cli.py,sha256=BaOsk37XwIXHi4dtjO5EDK9dgFSk9_qQcq2UwnkXEB0,19956
logfire/_internal/collect_system_info.py,sha256=6mwYm8jMdme9HRwq5VWeJFyIdyBYbViwENXo1m1WMw0,1109
logfire/_internal/config.py,sha256=qpijmiy7SeP2ktXSNt_sFbCWH5l81CAAwuGaVWwPr4Y,80418
logfire/_internal/config_params.py,sha256=aKt2pI44-05InTx64mk5q6qeb4tDonAU-ZB2L0jogNA,11640
logfire/_internal/constants.py,sha256=RhmdUWv6allVUJENF1iYwTq4FIwczR87o8yQdLLQY10,5887
logfire/_internal/db_statement_summary.py,sha256=iGTDymWiaGpEJrt0POOJhCnKftlGJ6znC1wAo-EKkdM,5242
logfire/_internal/exporters/__init__.py,sha256=zhJAlDqQ4tA9-hMk5WNIATDU3g8Wyu1bNMHndq6Lutg,38
logfire/_internal/exporters/__pycache__/__init__.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/console.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/dynamic_batch.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/logs.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/otlp.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/processor_wrapper.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/quiet_metrics.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/remove_pending.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/test.cpython-312.pyc,,
logfire/_internal/exporters/__pycache__/wrapper.cpython-312.pyc,,
logfire/_internal/exporters/console.py,sha256=57IGZKUXzE4cSfdYA2PDOVzwN_-naJ6Beb9ud8j4d-k,20801
logfire/_internal/exporters/dynamic_batch.py,sha256=bTnqOoUcNfrcv6h8ebICLL0R8yQoaW9_BeMcFvnwFhY,1376
logfire/_internal/exporters/logs.py,sha256=UMJcZFIBX2ZJDXf35yoBM3hwLy9FsPl4Ov9vO4oVLLk,946
logfire/_internal/exporters/otlp.py,sha256=rLvtaFamXz6sip8hdYyEIjPEupDuVUZHLOaLWSBasxo,10537
logfire/_internal/exporters/processor_wrapper.py,sha256=9uHrOkjbgbKpHQHz2OfEUZPYhSMu49dxau-QepNn8mc,12283
logfire/_internal/exporters/quiet_metrics.py,sha256=kHnyxtw26Mgn6rVVk1yfeQDBnLJTkCifAp_S7JmNYW0,710
logfire/_internal/exporters/remove_pending.py,sha256=h_WRa6boMRZgpwUTStGlL_WNHt-SPDBZjqAl_ms2qjk,1787
logfire/_internal/exporters/test.py,sha256=WVLh2wOxjrnJ9VKjhP0ty1R-Xr3rzU9GcZ7FwfpdxY0,9641
logfire/_internal/exporters/wrapper.py,sha256=NEQQ5wmcL6l1HprW46DA8tqpgXeUkkLZE5eLOQ8KrsI,3667
logfire/_internal/formatter.py,sha256=JlY9DnUfojWwVOynPdBylRxJN_xALfopgc271cuy9nw,20446
logfire/_internal/instrument.py,sha256=-6ZcoADO7-uOFdWoHjATHcZyKqJXVT1qXXig56PiAyA,8066
logfire/_internal/integrations/__init__.py,sha256=3v3p9yRZQoB_lfcO-nEjpcUMUhqa97QobbUNdj3dz2Q,46
logfire/_internal/integrations/__pycache__/__init__.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/aiohttp_client.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/asgi.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/asyncpg.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/aws_lambda.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/celery.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/django.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/executors.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/fastapi.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/flask.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/httpx.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/mcp.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/mysql.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/openai_agents.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/psycopg.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/pydantic_ai.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/pymongo.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/redis.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/requests.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/sqlalchemy.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/sqlite3.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/starlette.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/system_metrics.cpython-312.pyc,,
logfire/_internal/integrations/__pycache__/wsgi.cpython-312.pyc,,
logfire/_internal/integrations/aiohttp_client.py,sha256=mv1xJnibGZzXV5ZEjt1EYEY2skHRqo3tohqnqVSuKJA,902
logfire/_internal/integrations/asgi.py,sha256=zdd71AwUIqBvvmYcHjKi2lXn-tMm2vrS_8MY92OE7Dg,3733
logfire/_internal/integrations/asyncpg.py,sha256=NpA_gjgMmfzsaguy5q_beMXvaUPZ-YDxalJNjmvK498,639
logfire/_internal/integrations/aws_lambda.py,sha256=_LPf-HhBfNtJ_63RoqSEWDWTlS509sF31xU_MR60mzM,1277
logfire/_internal/integrations/celery.py,sha256=PWX-jS4DecaBcOfLvEGl4bkDCXmfavbthnaj1CwDBFE,636
logfire/_internal/integrations/django.py,sha256=ov03tAky1Eu1iu6mMalQvjmah-sRbcUKlmu_vi5ubXU,1287
logfire/_internal/integrations/executors.py,sha256=aQok3F6l_Bn4JLqT7GNk3Z9h5WNbuVQqwawC7EYKT6I,2750
logfire/_internal/integrations/fastapi.py,sha256=_pYmAcd7bNM2ZobgXpL6YZQ0CiHYehKRQD4GKa7hgk4,12168
logfire/_internal/integrations/flask.py,sha256=4Ix8QmmEO3G3hssCXw4qy99d493Vl4zZBRaZFpyn7G4,1752
logfire/_internal/integrations/httpx.py,sha256=qGiRU1NOhJMaSyxb6vZT7MD8GlX4fYi98t_dI1Y3c7U,16886
logfire/_internal/integrations/llm_providers/__pycache__/anthropic.cpython-312.pyc,,
logfire/_internal/integrations/llm_providers/__pycache__/llm_provider.cpython-312.pyc,,
logfire/_internal/integrations/llm_providers/__pycache__/openai.cpython-312.pyc,,
logfire/_internal/integrations/llm_providers/__pycache__/types.cpython-312.pyc,,
logfire/_internal/integrations/llm_providers/anthropic.py,sha256=bx1Ixg_J6Ed2We_OBTTsdQkSTKnr0BdcHELoaz3GllM,3612
logfire/_internal/integrations/llm_providers/llm_provider.py,sha256=Kx_yB6aj268PJxSQuF9szora8NkRNYwYiSPmPpH9ai0,7732
logfire/_internal/integrations/llm_providers/openai.py,sha256=ga2TObljRP1eXxqb4Fcdd8hHoHw8VsYCqLiV1Ioshyo,12096
logfire/_internal/integrations/llm_providers/types.py,sha256=yehWidw_hkNsF_6P6RPvBBkpPN8vgIB1dunFPxJtzfg,747
logfire/_internal/integrations/mcp.py,sha256=ua_z3pLTKOjGvjG7Bnhw3EDXLytJwZrqiD4wjrM2PB4,2741
logfire/_internal/integrations/mysql.py,sha256=brOEp0ApfwBU3bYfAUAEVZC3_rBVvHUpfu4iIhQjMuo,1276
logfire/_internal/integrations/openai_agents.py,sha256=i_pTTHNu3KFzndc8Rr7ayNgNe72zL-ubHb5wbL3WKj4,14931
logfire/_internal/integrations/psycopg.py,sha256=52zu7fGHAe4X9GjdvopTGHr8G1cNrFcnVL6U9uWpNTc,5392
logfire/_internal/integrations/pydantic_ai.py,sha256=X54LtwDjIPW51W-Yy0DcTG31hcb-QSZgMn_A-BVQRzc,1099
logfire/_internal/integrations/pymongo.py,sha256=l6m3vFicR73R0QN613AQay-c3Efup-Usk9UWmYCr3U8,1324
logfire/_internal/integrations/redis.py,sha256=-V-yS_h1qBzvF4eb_xrePfYCYl_bQTu3A5DdPTH6z9g,1897
logfire/_internal/integrations/requests.py,sha256=iPvmXyK2zjGV2ICGxsrUwCAMU8w61n-XQ4mt5ehZod8,1071
logfire/_internal/integrations/sqlalchemy.py,sha256=OqazM9jl8hAQeN1Dea6qbvfXfTOUXabIr3ND8J_1CcI,1191
logfire/_internal/integrations/sqlite3.py,sha256=1pYKLPcm-Kb6RL1f6gfUoG6FmCMUrvDNXbWPqUpiP5U,1055
logfire/_internal/integrations/starlette.py,sha256=SjRk70OzCTTkW7uuc8lAjn_os9Tq-htrPCihpsQ3B84,1738
logfire/_internal/integrations/system_metrics.py,sha256=DhrbZM1SSNa8dJ0Xd72rBzyIAOISgoqLRsh9V__rWzM,9304
logfire/_internal/integrations/wsgi.py,sha256=nRQC5hPyzw--VIv8mUOaaqdbQlMpblrAYamQPPR1auE,1109
logfire/_internal/json_encoder.py,sha256=AlPauGU2XT1eavTHCnpsqOyqtZ87S1mNSnCJCDBoQAc,9965
logfire/_internal/json_formatter.py,sha256=WLt-iNJvD1GJ3UrCoIj_ojZkh3ZRWoLg18EUdFmSIdg,12448
logfire/_internal/json_schema.py,sha256=N5Q1SOY68GHDexusVWDxBi_l5Iw7bA3zSVD9H4sYeds,14087
logfire/_internal/json_types.py,sha256=rNGwvSwY0vxRwOhXqQTWvyqnAYhGLTPPaFm1Z1ZrjzE,2753
logfire/_internal/logs.py,sha256=GlzfiVY9fBln6_PhR0paOThRvJkUCttRgWE9f7I6UpE,3209
logfire/_internal/main.py,sha256=uXgi3CDgeoGILfY2sb2-ahUOSM4RlU5HMP9Ez_0c8To,100823
logfire/_internal/metrics.py,sha256=fhPoDbcqIDh0IX0bK7h8i8WKK372Er9Mf4o55rJA6Fw,11730
logfire/_internal/scrubbing.py,sha256=Aj-CUHDtZjam0DCg2oWtuzlEvuYw4yoTV115817FHpk,11871
logfire/_internal/stack_info.py,sha256=gLxBsM3UZXdl4-cs7LHiIaUEzX5Ea-CSmZjg2fWa0ZM,4915
logfire/_internal/tracer.py,sha256=D_Fkl5IIVJgpXyNAlQt7wvCIKya9bO-3ud3BGY27CZ8,15560
logfire/_internal/ulid.py,sha256=dDHSl8nMo6OVApPORaUVihy3dl0lEBalkEaL6iNgAEA,2038
logfire/_internal/utils.py,sha256=2JivRb5jEY-eoHOG0itzM0PZeu3fQpdP0gVb1c3RWjI,15067
logfire/cli.py,sha256=C2X0XBrYuGx6iBhfXCrlDMZIL72FzLxagtUgHxiyhVA,53
logfire/exceptions.py,sha256=4x0UOjeTuc4VlQW2dg3VigRJU3M08y6DFtc44vY0UGE,145
logfire/experimental/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
logfire/experimental/__pycache__/__init__.cpython-312.pyc,,
logfire/experimental/__pycache__/annotations.cpython-312.pyc,,
logfire/experimental/__pycache__/query_client.cpython-312.pyc,,
logfire/experimental/annotations.py,sha256=9XoPOUkT0n4sHIlz1YsSjUUuEjg2wBUESPbAdhDMx9U,2696
logfire/experimental/query_client.py,sha256=9IgtS8aG8FDdBET_XbjA7J-Py_n1m8q0D00TWgPqjFM,11492
logfire/integrations/__init__.py,sha256=frQk2NnpMMxe0-w5c0jjtWCAxcY4Jt_BVFLg38oDsU8,99
logfire/integrations/__pycache__/__init__.cpython-312.pyc,,
logfire/integrations/__pycache__/flask.cpython-312.pyc,,
logfire/integrations/__pycache__/httpx.cpython-312.pyc,,
logfire/integrations/__pycache__/logging.cpython-312.pyc,,
logfire/integrations/__pycache__/loguru.cpython-312.pyc,,
logfire/integrations/__pycache__/psycopg.cpython-312.pyc,,
logfire/integrations/__pycache__/pydantic.cpython-312.pyc,,
logfire/integrations/__pycache__/redis.cpython-312.pyc,,
logfire/integrations/__pycache__/sqlalchemy.cpython-312.pyc,,
logfire/integrations/__pycache__/structlog.cpython-312.pyc,,
logfire/integrations/__pycache__/wsgi.cpython-312.pyc,,
logfire/integrations/flask.py,sha256=ev0duZirVQ0pC-sCHuXTU_vSwhtFwkjN-aYL_8LEjKg,781
logfire/integrations/httpx.py,sha256=hZowU-26KpvtEjTLIScLadM0Hikr8VOmEfiPrixvBG0,1564
logfire/integrations/logging.py,sha256=KXGYgwYgXmHTUqecGc0A4WdapiTpzhFU6ILXOsDiKb4,4544
logfire/integrations/loguru.py,sha256=G4_E8hjEpD4-T4JH_7iAHSfFs0K4D9RdvkAamEnT-58,3769
logfire/integrations/psycopg.py,sha256=xdv8BtrGWm4IKYNNqsBWwGLkrpAkQniCXRiOBnO68j4,713
logfire/integrations/pydantic.py,sha256=10aSBHiEOgq0W1DxhP8_xauDLvJnN5uxI6Cs4aVlIEk,19411
logfire/integrations/redis.py,sha256=n4Ug7SYSPiaO8eJ6U4pCi9DXUhFY7ANweRS46gf90o8,957
logfire/integrations/sqlalchemy.py,sha256=Wi4VGP4biTEe5RVCaCYU2oJJuw5b947wVqrOU8RpLLM,493
logfire/integrations/structlog.py,sha256=06zASq9w9CNERTrrfjsladEzJYiVrUQSWjV4mUI9TQ4,1963
logfire/integrations/wsgi.py,sha256=Ulc273RsthY6QF1Oi1joVNoxHzx-jI0ieS36aKLXDto,456
logfire/propagate.py,sha256=KhRD1iQm1NGrKoAou4Fw2j2HbadTqWpJABHoRGUXOHg,5677
logfire/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
logfire/sampling/__init__.py,sha256=xgduo7YolbCb_b0pc5qzje4jDO0l1Lye4qMqepV_4VA,286
logfire/sampling/__pycache__/__init__.cpython-312.pyc,,
logfire/sampling/__pycache__/_tail_sampling.cpython-312.pyc,,
logfire/sampling/_tail_sampling.py,sha256=EOsM7iUJvz-Yft_EmkrBFfYs_ahheXkSzSU77PVHAp4,10471
logfire/testing.py,sha256=xlA4QXGkDWfjblx3waOmWpbWa7ZFi5Z8VT4z3K_DYc0,3678
logfire/version.py,sha256=Ttxz-NYMF14MATyYcRGJnDpwojpcNSIdGKgOZo9IKlg,123
