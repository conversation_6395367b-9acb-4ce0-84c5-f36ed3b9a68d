{"info": {"name": "GenTekai API Tests", "description": "Test collection for GenTekai Multi-Agent Platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Checks", "item": [{"name": "Backend Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9876/-/health", "protocol": "http", "host": ["localhost"], "port": "9876", "path": ["-", "health"]}}}, {"name": "API Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9876/api/health", "protocol": "http", "host": ["localhost"], "port": "9876", "path": ["api", "health"]}}}, {"name": "MCP Server Health", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9877/-/health", "protocol": "http", "host": ["localhost"], "port": "9877", "path": ["-", "health"]}}}]}, {"name": "API Documentation", "item": [{"name": "Backend OpenAPI Docs", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9876/docs", "protocol": "http", "host": ["localhost"], "port": "9876", "path": ["docs"]}}}, {"name": "MCP Server OpenAPI", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9877/swagger.json", "protocol": "http", "host": ["localhost"], "port": "9877", "path": ["swagger.json"]}}}]}]}