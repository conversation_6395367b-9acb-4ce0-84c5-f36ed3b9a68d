import {
  __assign,
  init_tslib_es6
} from "./chunk-DJCHRV5B.js";
import {
  require_authorization,
  require_deprecated,
  require_error,
  require_errors,
  require_keys,
  require_telemetry,
  require_utils,
  require_utils2
} from "./chunk-XACFPJBO.js";
import {
  esm_exports,
  init_esm,
  init_router,
  require_cookie,
  router_exports
} from "./chunk-XID4VVAK.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-QGSYD46Z.js";

// node_modules/@clerk/shared/dist/buildAccountsBaseUrl.js
var require_buildAccountsBaseUrl = __commonJS({
  "node_modules/@clerk/shared/dist/buildAccountsBaseUrl.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var buildAccountsBaseUrl_exports = {};
    __export2(buildAccountsBaseUrl_exports, {
      buildAccountsBaseUrl: () => buildAccountsBaseUrl
    });
    module.exports = __toCommonJS2(buildAccountsBaseUrl_exports);
    function buildAccountsBaseUrl(frontendApi) {
      if (!frontendApi) {
        return "";
      }
      const accountsBaseUrl = frontendApi.replace(/clerk\.accountsstage\./, "accountsstage.").replace(/clerk\.accounts\.|clerk\./, "accounts.");
      return `https://${accountsBaseUrl}`;
    }
  }
});

// node_modules/@clerk/shared/dist/url.js
var require_url = __commonJS({
  "node_modules/@clerk/shared/dist/url.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var url_exports = {};
    __export2(url_exports, {
      addClerkPrefix: () => addClerkPrefix,
      cleanDoubleSlashes: () => cleanDoubleSlashes,
      getClerkJsMajorVersionOrTag: () => getClerkJsMajorVersionOrTag,
      getScriptUrl: () => getScriptUrl,
      hasLeadingSlash: () => hasLeadingSlash,
      hasTrailingSlash: () => hasTrailingSlash,
      isAbsoluteUrl: () => isAbsoluteUrl,
      isCurrentDevAccountPortalOrigin: () => isCurrentDevAccountPortalOrigin,
      isLegacyDevAccountPortalOrigin: () => isLegacyDevAccountPortalOrigin,
      isNonEmptyURL: () => isNonEmptyURL,
      joinURL: () => joinURL,
      parseSearchParams: () => parseSearchParams,
      stripScheme: () => stripScheme,
      withLeadingSlash: () => withLeadingSlash,
      withTrailingSlash: () => withTrailingSlash,
      withoutLeadingSlash: () => withoutLeadingSlash,
      withoutTrailingSlash: () => withoutTrailingSlash
    });
    module.exports = __toCommonJS2(url_exports);
    var LEGACY_DEV_INSTANCE_SUFFIXES = [".lcl.dev", ".lclstage.dev", ".lclclerk.com"];
    var CURRENT_DEV_INSTANCE_SUFFIXES = [".accounts.dev", ".accountsstage.dev", ".accounts.lclclerk.com"];
    function isStaging(frontendApi) {
      return frontendApi.endsWith(".lclstage.dev") || frontendApi.endsWith(".stgstage.dev") || frontendApi.endsWith(".clerkstage.dev") || frontendApi.endsWith(".accountsstage.dev");
    }
    function parseSearchParams(queryString = "") {
      if (queryString.startsWith("?")) {
        queryString = queryString.slice(1);
      }
      return new URLSearchParams(queryString);
    }
    function stripScheme(url = "") {
      return (url || "").replace(/^.+:\/\//, "");
    }
    function addClerkPrefix(str) {
      if (!str) {
        return "";
      }
      let regex;
      if (str.match(/^(clerk\.)+\w*$/)) {
        regex = /(clerk\.)*(?=clerk\.)/;
      } else if (str.match(/\.clerk.accounts/)) {
        return str;
      } else {
        regex = /^(clerk\.)*/gi;
      }
      const stripped = str.replace(regex, "");
      return `clerk.${stripped}`;
    }
    var getClerkJsMajorVersionOrTag = (frontendApi, version) => {
      if (!version && isStaging(frontendApi)) {
        return "canary";
      }
      if (!version) {
        return "latest";
      }
      return version.split(".")[0] || "latest";
    };
    var getScriptUrl = (frontendApi, { clerkJSVersion }) => {
      const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\/\//, "");
      const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);
      return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;
    };
    function isLegacyDevAccountPortalOrigin(host) {
      return LEGACY_DEV_INSTANCE_SUFFIXES.some((legacyDevSuffix) => {
        return host.startsWith("accounts.") && host.endsWith(legacyDevSuffix);
      });
    }
    function isCurrentDevAccountPortalOrigin(host) {
      return CURRENT_DEV_INSTANCE_SUFFIXES.some((currentDevSuffix) => {
        return host.endsWith(currentDevSuffix) && !host.endsWith(".clerk" + currentDevSuffix);
      });
    }
    var TRAILING_SLASH_RE = /\/$|\/\?|\/#/;
    function hasTrailingSlash(input = "", respectQueryAndFragment) {
      if (!respectQueryAndFragment) {
        return input.endsWith("/");
      }
      return TRAILING_SLASH_RE.test(input);
    }
    function withTrailingSlash(input = "", respectQueryAndFragment) {
      if (!respectQueryAndFragment) {
        return input.endsWith("/") ? input : input + "/";
      }
      if (hasTrailingSlash(input, true)) {
        return input || "/";
      }
      let path = input;
      let fragment = "";
      const fragmentIndex = input.indexOf("#");
      if (fragmentIndex >= 0) {
        path = input.slice(0, fragmentIndex);
        fragment = input.slice(fragmentIndex);
        if (!path) {
          return fragment;
        }
      }
      const [s0, ...s] = path.split("?");
      return s0 + "/" + (s.length > 0 ? `?${s.join("?")}` : "") + fragment;
    }
    function withoutTrailingSlash(input = "", respectQueryAndFragment) {
      if (!respectQueryAndFragment) {
        return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || "/";
      }
      if (!hasTrailingSlash(input, true)) {
        return input || "/";
      }
      let path = input;
      let fragment = "";
      const fragmentIndex = input.indexOf("#");
      if (fragmentIndex >= 0) {
        path = input.slice(0, fragmentIndex);
        fragment = input.slice(fragmentIndex);
      }
      const [s0, ...s] = path.split("?");
      return (s0.slice(0, -1) || "/") + (s.length > 0 ? `?${s.join("?")}` : "") + fragment;
    }
    function hasLeadingSlash(input = "") {
      return input.startsWith("/");
    }
    function withoutLeadingSlash(input = "") {
      return (hasLeadingSlash(input) ? input.slice(1) : input) || "/";
    }
    function withLeadingSlash(input = "") {
      return hasLeadingSlash(input) ? input : "/" + input;
    }
    function cleanDoubleSlashes(input = "") {
      return input.split("://").map((string_) => string_.replace(/\/{2,}/g, "/")).join("://");
    }
    function isNonEmptyURL(url) {
      return url && url !== "/";
    }
    var JOIN_LEADING_SLASH_RE = /^\.?\//;
    function joinURL(base, ...input) {
      let url = base || "";
      for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {
        if (url) {
          const _segment = segment.replace(JOIN_LEADING_SLASH_RE, "");
          url = withTrailingSlash(url) + _segment;
        } else {
          url = segment;
        }
      }
      return url;
    }
    var ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\d+\-.]*?:/;
    var isAbsoluteUrl = (url) => ABSOLUTE_URL_REGEX.test(url);
  }
});

// node_modules/@clerk/shared/dist/retry.js
var require_retry = __commonJS({
  "node_modules/@clerk/shared/dist/retry.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var retry_exports = {};
    __export2(retry_exports, {
      retry: () => retry
    });
    module.exports = __toCommonJS2(retry_exports);
    var defaultOptions = {
      initialDelay: 125,
      maxDelayBetweenRetries: 0,
      factor: 2,
      shouldRetry: (_, iteration) => iteration < 5,
      retryImmediately: false,
      jitter: true
    };
    var RETRY_IMMEDIATELY_DELAY = 100;
    var sleep = async (ms) => new Promise((s) => setTimeout(s, ms));
    var applyJitter = (delay, jitter) => {
      return jitter ? delay * (1 + Math.random()) : delay;
    };
    var createExponentialDelayAsyncFn = (opts) => {
      let timesCalled = 0;
      const calculateDelayInMs = () => {
        const constant = opts.initialDelay;
        const base = opts.factor;
        let delay = constant * Math.pow(base, timesCalled);
        delay = applyJitter(delay, opts.jitter);
        return Math.min(opts.maxDelayBetweenRetries || delay, delay);
      };
      return async () => {
        await sleep(calculateDelayInMs());
        timesCalled++;
      };
    };
    var retry = async (callback, options = {}) => {
      let iterations = 0;
      const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {
        ...defaultOptions,
        ...options
      };
      const delay = createExponentialDelayAsyncFn({
        initialDelay,
        maxDelayBetweenRetries,
        factor,
        jitter
      });
      while (true) {
        try {
          return await callback();
        } catch (e) {
          iterations++;
          if (!shouldRetry(e, iterations)) {
            throw e;
          }
          if (retryImmediately && iterations === 1) {
            await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));
          } else {
            await delay();
          }
        }
      }
    };
  }
});

// node_modules/@clerk/backend/dist/runtime/browser/crypto.mjs
var crypto_exports = {};
__export(crypto_exports, {
  webcrypto: () => webcrypto
});
var webcrypto;
var init_crypto = __esm({
  "node_modules/@clerk/backend/dist/runtime/browser/crypto.mjs"() {
    webcrypto = crypto;
  }
});

// node_modules/@clerk/shared/dist/isomorphicAtob.js
var require_isomorphicAtob = __commonJS({
  "node_modules/@clerk/shared/dist/isomorphicAtob.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var isomorphicAtob_exports = {};
    __export2(isomorphicAtob_exports, {
      isomorphicAtob: () => isomorphicAtob
    });
    module.exports = __toCommonJS2(isomorphicAtob_exports);
    var isomorphicAtob = (data) => {
      if (typeof atob !== "undefined" && typeof atob === "function") {
        return atob(data);
      } else if (typeof global !== "undefined" && global.Buffer) {
        return new global.Buffer(data, "base64").toString();
      }
      return data;
    };
  }
});

// node_modules/@clerk/shared/dist/jwtPayloadParser.js
var require_jwtPayloadParser = __commonJS({
  "node_modules/@clerk/shared/dist/jwtPayloadParser.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var jwtPayloadParser_exports = {};
    __export2(jwtPayloadParser_exports, {
      __experimental_JWTPayloadToAuthObjectProperties: () => __experimental_JWTPayloadToAuthObjectProperties,
      parsePermissions: () => parsePermissions
    });
    module.exports = __toCommonJS2(jwtPayloadParser_exports);
    var splitByScope = (fea) => {
      const features = fea ? fea.split(",").map((f) => f.trim()) : [];
      return {
        org: features.filter((f) => f.split(":")[0].includes("o")).map((f) => f.split(":")[1]),
        user: features.filter((f) => f.split(":")[0].includes("u")).map((f) => f.split(":")[1])
      };
    };
    var parsePermissions = ({ per, fpm }) => {
      if (!per || !fpm) {
        return { permissions: [], featurePermissionMap: [] };
      }
      const permissions = per.split(",").map((p) => p.trim());
      const featurePermissionMap = fpm.split(",").map((permission) => Number.parseInt(permission.trim(), 10)).map(
        (permission) => permission.toString(2).padStart(permissions.length, "0").split("").map((bit) => Number.parseInt(bit, 10)).reverse()
      ).filter(Boolean);
      return { permissions, featurePermissionMap };
    };
    function buildOrgPermissions({
      features,
      permissions,
      featurePermissionMap
    }) {
      if (!features || !permissions || !featurePermissionMap) {
        return [];
      }
      const orgPermissions = [];
      for (let featureIndex = 0; featureIndex < features.length; featureIndex++) {
        const feature = features[featureIndex];
        if (featureIndex >= featurePermissionMap.length) {
          continue;
        }
        const permissionBits = featurePermissionMap[featureIndex];
        if (!permissionBits)
          continue;
        for (let permIndex = 0; permIndex < permissionBits.length; permIndex++) {
          if (permissionBits[permIndex] === 1) {
            orgPermissions.push(`org:${feature}:${permissions[permIndex]}`);
          }
        }
      }
      return orgPermissions;
    }
    var __experimental_JWTPayloadToAuthObjectProperties = (claims) => {
      var _a, _b, _c, _d, _e, _f;
      let orgId;
      let orgRole;
      let orgSlug;
      let orgPermissions;
      const factorVerificationAge = claims.fva ?? null;
      const sessionStatus = claims.sts ?? null;
      switch (claims.v) {
        case 2: {
          if (claims.o) {
            orgId = (_a = claims.o) == null ? void 0 : _a.id;
            orgSlug = (_b = claims.o) == null ? void 0 : _b.slg;
            if ((_c = claims.o) == null ? void 0 : _c.rol) {
              orgRole = `org:${(_d = claims.o) == null ? void 0 : _d.rol}`;
            }
            const { org } = splitByScope(claims.fea);
            const { permissions, featurePermissionMap } = parsePermissions({
              per: (_e = claims.o) == null ? void 0 : _e.per,
              fpm: (_f = claims.o) == null ? void 0 : _f.fpm
            });
            orgPermissions = buildOrgPermissions({
              features: org,
              featurePermissionMap,
              permissions
            });
          }
          break;
        }
        default:
          orgId = claims.org_id;
          orgRole = claims.org_role;
          orgSlug = claims.org_slug;
          orgPermissions = claims.org_permissions;
          break;
      }
      return {
        sessionClaims: claims,
        sessionId: claims.sid,
        sessionStatus,
        actor: claims.act,
        userId: claims.sub,
        orgId,
        orgRole,
        orgSlug,
        orgPermissions,
        factorVerificationAge
      };
    };
  }
});

// node_modules/map-obj/index.js
var require_map_obj = __commonJS({
  "node_modules/map-obj/index.js"(exports, module) {
    "use strict";
    var isObject = (value) => typeof value === "object" && value !== null;
    var mapObjectSkip = Symbol("skip");
    var isObjectCustom = (value) => isObject(value) && !(value instanceof RegExp) && !(value instanceof Error) && !(value instanceof Date);
    var mapObject = (object, mapper, options, isSeen = /* @__PURE__ */ new WeakMap()) => {
      options = {
        deep: false,
        target: {},
        ...options
      };
      if (isSeen.has(object)) {
        return isSeen.get(object);
      }
      isSeen.set(object, options.target);
      const { target } = options;
      delete options.target;
      const mapArray = (array) => array.map((element) => isObjectCustom(element) ? mapObject(element, mapper, options, isSeen) : element);
      if (Array.isArray(object)) {
        return mapArray(object);
      }
      for (const [key, value] of Object.entries(object)) {
        const mapResult = mapper(key, value, object);
        if (mapResult === mapObjectSkip) {
          continue;
        }
        let [newKey, newValue, { shouldRecurse = true } = {}] = mapResult;
        if (newKey === "__proto__") {
          continue;
        }
        if (options.deep && shouldRecurse && isObjectCustom(newValue)) {
          newValue = Array.isArray(newValue) ? mapArray(newValue) : mapObject(newValue, mapper, options, isSeen);
        }
        target[newKey] = newValue;
      }
      return target;
    };
    module.exports = (object, mapper, options) => {
      if (!isObject(object)) {
        throw new TypeError(`Expected an object, got \`${object}\` (${typeof object})`);
      }
      return mapObject(object, mapper, options);
    };
    module.exports.mapObjectSkip = mapObjectSkip;
  }
});

// node_modules/lower-case/dist.es2015/index.js
function lowerCase(str) {
  return str.toLowerCase();
}
var init_dist = __esm({
  "node_modules/lower-case/dist.es2015/index.js"() {
  }
});

// node_modules/no-case/dist.es2015/index.js
function noCase(input, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.splitRegexp, splitRegexp = _a === void 0 ? DEFAULT_SPLIT_REGEXP : _a, _b = options.stripRegexp, stripRegexp = _b === void 0 ? DEFAULT_STRIP_REGEXP : _b, _c = options.transform, transform = _c === void 0 ? lowerCase : _c, _d = options.delimiter, delimiter = _d === void 0 ? " " : _d;
  var result = replace(replace(input, splitRegexp, "$1\0$2"), stripRegexp, "\0");
  var start = 0;
  var end = result.length;
  while (result.charAt(start) === "\0")
    start++;
  while (result.charAt(end - 1) === "\0")
    end--;
  return result.slice(start, end).split("\0").map(transform).join(delimiter);
}
function replace(input, re, value) {
  if (re instanceof RegExp)
    return input.replace(re, value);
  return re.reduce(function(input2, re2) {
    return input2.replace(re2, value);
  }, input);
}
var DEFAULT_SPLIT_REGEXP, DEFAULT_STRIP_REGEXP;
var init_dist2 = __esm({
  "node_modules/no-case/dist.es2015/index.js"() {
    init_dist();
    DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];
    DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;
  }
});

// node_modules/dot-case/dist.es2015/index.js
function dotCase(input, options) {
  if (options === void 0) {
    options = {};
  }
  return noCase(input, __assign({ delimiter: "." }, options));
}
var init_dist3 = __esm({
  "node_modules/dot-case/dist.es2015/index.js"() {
    init_tslib_es6();
    init_dist2();
  }
});

// node_modules/snake-case/dist.es2015/index.js
var dist_exports = {};
__export(dist_exports, {
  snakeCase: () => snakeCase
});
function snakeCase(input, options) {
  if (options === void 0) {
    options = {};
  }
  return dotCase(input, __assign({ delimiter: "_" }, options));
}
var init_dist4 = __esm({
  "node_modules/snake-case/dist.es2015/index.js"() {
    init_tslib_es6();
    init_dist3();
  }
});

// node_modules/snakecase-keys/index.js
var require_snakecase_keys = __commonJS({
  "node_modules/snakecase-keys/index.js"(exports, module) {
    "use strict";
    var map = require_map_obj();
    var { snakeCase: snakeCase2 } = (init_dist4(), __toCommonJS(dist_exports));
    var PlainObjectConstructor = {}.constructor;
    module.exports = function(obj, options) {
      if (Array.isArray(obj)) {
        if (obj.some((item) => item.constructor !== PlainObjectConstructor)) {
          throw new Error("obj must be array of plain objects");
        }
      } else {
        if (obj.constructor !== PlainObjectConstructor) {
          throw new Error("obj must be an plain object");
        }
      }
      options = Object.assign({ deep: true, exclude: [], parsingOptions: {} }, options);
      return map(obj, function(key, val) {
        return [
          matches(options.exclude, key) ? key : snakeCase2(key, options.parsingOptions),
          val,
          mapperOptions(key, val, options)
        ];
      }, options);
    };
    function matches(patterns, value) {
      return patterns.some(function(pattern) {
        return typeof pattern === "string" ? pattern === value : pattern.test(value);
      });
    }
    function mapperOptions(key, val, options) {
      return options.shouldRecurse ? { shouldRecurse: options.shouldRecurse(key, val) } : void 0;
    }
  }
});

// node_modules/@clerk/backend/node_modules/cookie/dist/index.js
var require_dist = __commonJS({
  "node_modules/@clerk/backend/node_modules/cookie/dist/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.parse = parse;
    exports.serialize = serialize;
    var cookieNameRegExp = /^[\u0021-\u003A\u003C\u003E-\u007E]+$/;
    var cookieValueRegExp = /^[\u0021-\u003A\u003C-\u007E]*$/;
    var domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;
    var pathValueRegExp = /^[\u0020-\u003A\u003D-\u007E]*$/;
    var __toString = Object.prototype.toString;
    var NullObject = (() => {
      const C = function() {
      };
      C.prototype = /* @__PURE__ */ Object.create(null);
      return C;
    })();
    function parse(str, options) {
      const obj = new NullObject();
      const len = str.length;
      if (len < 2)
        return obj;
      const dec = (options == null ? void 0 : options.decode) || decode;
      let index = 0;
      do {
        const eqIdx = str.indexOf("=", index);
        if (eqIdx === -1)
          break;
        const colonIdx = str.indexOf(";", index);
        const endIdx = colonIdx === -1 ? len : colonIdx;
        if (eqIdx > endIdx) {
          index = str.lastIndexOf(";", eqIdx - 1) + 1;
          continue;
        }
        const keyStartIdx = startIndex(str, index, eqIdx);
        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);
        const key = str.slice(keyStartIdx, keyEndIdx);
        if (obj[key] === void 0) {
          let valStartIdx = startIndex(str, eqIdx + 1, endIdx);
          let valEndIdx = endIndex(str, endIdx, valStartIdx);
          const value = dec(str.slice(valStartIdx, valEndIdx));
          obj[key] = value;
        }
        index = endIdx + 1;
      } while (index < len);
      return obj;
    }
    function startIndex(str, index, max) {
      do {
        const code = str.charCodeAt(index);
        if (code !== 32 && code !== 9)
          return index;
      } while (++index < max);
      return max;
    }
    function endIndex(str, index, min) {
      while (index > min) {
        const code = str.charCodeAt(--index);
        if (code !== 32 && code !== 9)
          return index + 1;
      }
      return min;
    }
    function serialize(name, val, options) {
      const enc = (options == null ? void 0 : options.encode) || encodeURIComponent;
      if (!cookieNameRegExp.test(name)) {
        throw new TypeError(`argument name is invalid: ${name}`);
      }
      const value = enc(val);
      if (!cookieValueRegExp.test(value)) {
        throw new TypeError(`argument val is invalid: ${val}`);
      }
      let str = name + "=" + value;
      if (!options)
        return str;
      if (options.maxAge !== void 0) {
        if (!Number.isInteger(options.maxAge)) {
          throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);
        }
        str += "; Max-Age=" + options.maxAge;
      }
      if (options.domain) {
        if (!domainValueRegExp.test(options.domain)) {
          throw new TypeError(`option domain is invalid: ${options.domain}`);
        }
        str += "; Domain=" + options.domain;
      }
      if (options.path) {
        if (!pathValueRegExp.test(options.path)) {
          throw new TypeError(`option path is invalid: ${options.path}`);
        }
        str += "; Path=" + options.path;
      }
      if (options.expires) {
        if (!isDate(options.expires) || !Number.isFinite(options.expires.valueOf())) {
          throw new TypeError(`option expires is invalid: ${options.expires}`);
        }
        str += "; Expires=" + options.expires.toUTCString();
      }
      if (options.httpOnly) {
        str += "; HttpOnly";
      }
      if (options.secure) {
        str += "; Secure";
      }
      if (options.partitioned) {
        str += "; Partitioned";
      }
      if (options.priority) {
        const priority = typeof options.priority === "string" ? options.priority.toLowerCase() : void 0;
        switch (priority) {
          case "low":
            str += "; Priority=Low";
            break;
          case "medium":
            str += "; Priority=Medium";
            break;
          case "high":
            str += "; Priority=High";
            break;
          default:
            throw new TypeError(`option priority is invalid: ${options.priority}`);
        }
      }
      if (options.sameSite) {
        const sameSite = typeof options.sameSite === "string" ? options.sameSite.toLowerCase() : options.sameSite;
        switch (sameSite) {
          case true:
          case "strict":
            str += "; SameSite=Strict";
            break;
          case "lax":
            str += "; SameSite=Lax";
            break;
          case "none":
            str += "; SameSite=None";
            break;
          default:
            throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);
        }
      }
      return str;
    }
    function decode(str) {
      if (str.indexOf("%") === -1)
        return str;
      try {
        return decodeURIComponent(str);
      } catch (e) {
        return str;
      }
    }
    function isDate(val) {
      return __toString.call(val) === "[object Date]";
    }
  }
});

// node_modules/@clerk/shared/dist/pathToRegexp.js
var require_pathToRegexp = __commonJS({
  "node_modules/@clerk/shared/dist/pathToRegexp.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var pathToRegexp_exports = {};
    __export2(pathToRegexp_exports, {
      match: () => match,
      pathToRegexp: () => pathToRegexp
    });
    module.exports = __toCommonJS2(pathToRegexp_exports);
    function _(r) {
      for (var n = [], e = 0; e < r.length; ) {
        var a = r[e];
        if (a === "*" || a === "+" || a === "?") {
          n.push({
            type: "MODIFIER",
            index: e,
            value: r[e++]
          });
          continue;
        }
        if (a === "\\") {
          n.push({
            type: "ESCAPED_CHAR",
            index: e++,
            value: r[e++]
          });
          continue;
        }
        if (a === "{") {
          n.push({
            type: "OPEN",
            index: e,
            value: r[e++]
          });
          continue;
        }
        if (a === "}") {
          n.push({
            type: "CLOSE",
            index: e,
            value: r[e++]
          });
          continue;
        }
        if (a === ":") {
          for (var u = "", t = e + 1; t < r.length; ) {
            var c = r.charCodeAt(t);
            if (c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || c === 95) {
              u += r[t++];
              continue;
            }
            break;
          }
          if (!u)
            throw new TypeError("Missing parameter name at ".concat(e));
          n.push({
            type: "NAME",
            index: e,
            value: u
          }), e = t;
          continue;
        }
        if (a === "(") {
          var o = 1, m = "", t = e + 1;
          if (r[t] === "?")
            throw new TypeError('Pattern cannot start with "?" at '.concat(t));
          for (; t < r.length; ) {
            if (r[t] === "\\") {
              m += r[t++] + r[t++];
              continue;
            }
            if (r[t] === ")") {
              if (o--, o === 0) {
                t++;
                break;
              }
            } else if (r[t] === "(" && (o++, r[t + 1] !== "?"))
              throw new TypeError("Capturing groups are not allowed at ".concat(t));
            m += r[t++];
          }
          if (o)
            throw new TypeError("Unbalanced pattern at ".concat(e));
          if (!m)
            throw new TypeError("Missing pattern at ".concat(e));
          n.push({
            type: "PATTERN",
            index: e,
            value: m
          }), e = t;
          continue;
        }
        n.push({
          type: "CHAR",
          index: e,
          value: r[e++]
        });
      }
      return n.push({
        type: "END",
        index: e,
        value: ""
      }), n;
    }
    function F(r, n) {
      n === void 0 && (n = {});
      for (var e = _(r), a = n.prefixes, u = a === void 0 ? "./" : a, t = n.delimiter, c = t === void 0 ? "/#?" : t, o = [], m = 0, h = 0, p = "", f = function(l) {
        if (h < e.length && e[h].type === l)
          return e[h++].value;
      }, w = function(l) {
        var v = f(l);
        if (v !== void 0)
          return v;
        var E = e[h], N = E.type, S = E.index;
        throw new TypeError("Unexpected ".concat(N, " at ").concat(S, ", expected ").concat(l));
      }, d = function() {
        for (var l = "", v; v = f("CHAR") || f("ESCAPED_CHAR"); )
          l += v;
        return l;
      }, M = function(l) {
        for (var v = 0, E = c; v < E.length; v++) {
          var N = E[v];
          if (l.indexOf(N) > -1)
            return true;
        }
        return false;
      }, A = function(l) {
        var v = o[o.length - 1], E = l || (v && typeof v == "string" ? v : "");
        if (v && !E)
          throw new TypeError('Must have text between two parameters, missing text after "'.concat(v.name, '"'));
        return !E || M(E) ? "[^".concat(s(c), "]+?") : "(?:(?!".concat(s(E), ")[^").concat(s(c), "])+?");
      }; h < e.length; ) {
        var T = f("CHAR"), x = f("NAME"), C = f("PATTERN");
        if (x || C) {
          var g = T || "";
          u.indexOf(g) === -1 && (p += g, g = ""), p && (o.push(p), p = ""), o.push({
            name: x || m++,
            prefix: g,
            suffix: "",
            pattern: C || A(g),
            modifier: f("MODIFIER") || ""
          });
          continue;
        }
        var i = T || f("ESCAPED_CHAR");
        if (i) {
          p += i;
          continue;
        }
        p && (o.push(p), p = "");
        var R = f("OPEN");
        if (R) {
          var g = d(), y = f("NAME") || "", O = f("PATTERN") || "", b = d();
          w("CLOSE"), o.push({
            name: y || (O ? m++ : ""),
            pattern: y && !O ? A(g) : O,
            prefix: g,
            suffix: b,
            modifier: f("MODIFIER") || ""
          });
          continue;
        }
        w("END");
      }
      return o;
    }
    function H(r, n) {
      var e = [], a = P(r, e, n);
      return I(a, e, n);
    }
    function I(r, n, e) {
      e === void 0 && (e = {});
      var a = e.decode, u = a === void 0 ? function(t) {
        return t;
      } : a;
      return function(t) {
        var c = r.exec(t);
        if (!c)
          return false;
        for (var o = c[0], m = c.index, h = /* @__PURE__ */ Object.create(null), p = function(w) {
          if (c[w] === void 0)
            return "continue";
          var d = n[w - 1];
          d.modifier === "*" || d.modifier === "+" ? h[d.name] = c[w].split(d.prefix + d.suffix).map(function(M) {
            return u(M, d);
          }) : h[d.name] = u(c[w], d);
        }, f = 1; f < c.length; f++)
          p(f);
        return {
          path: o,
          index: m,
          params: h
        };
      };
    }
    function s(r) {
      return r.replace(/([.+*?=^!:${}()[\]|/\\])/g, "\\$1");
    }
    function D(r) {
      return r && r.sensitive ? "" : "i";
    }
    function $(r, n) {
      if (!n)
        return r;
      for (var e = /\((?:\?<(.*?)>)?(?!\?)/g, a = 0, u = e.exec(r.source); u; )
        n.push({
          name: u[1] || a++,
          prefix: "",
          suffix: "",
          modifier: "",
          pattern: ""
        }), u = e.exec(r.source);
      return r;
    }
    function W(r, n, e) {
      var a = r.map(function(u) {
        return P(u, n, e).source;
      });
      return new RegExp("(?:".concat(a.join("|"), ")"), D(e));
    }
    function L(r, n, e) {
      return U(F(r, e), n, e);
    }
    function U(r, n, e) {
      e === void 0 && (e = {});
      for (var a = e.strict, u = a === void 0 ? false : a, t = e.start, c = t === void 0 ? true : t, o = e.end, m = o === void 0 ? true : o, h = e.encode, p = h === void 0 ? function(v) {
        return v;
      } : h, f = e.delimiter, w = f === void 0 ? "/#?" : f, d = e.endsWith, M = d === void 0 ? "" : d, A = "[".concat(s(M), "]|$"), T = "[".concat(s(w), "]"), x = c ? "^" : "", C = 0, g = r; C < g.length; C++) {
        var i = g[C];
        if (typeof i == "string")
          x += s(p(i));
        else {
          var R = s(p(i.prefix)), y = s(p(i.suffix));
          if (i.pattern)
            if (n && n.push(i), R || y)
              if (i.modifier === "+" || i.modifier === "*") {
                var O = i.modifier === "*" ? "?" : "";
                x += "(?:".concat(R, "((?:").concat(i.pattern, ")(?:").concat(y).concat(R, "(?:").concat(i.pattern, "))*)").concat(y, ")").concat(O);
              } else
                x += "(?:".concat(R, "(").concat(i.pattern, ")").concat(y, ")").concat(i.modifier);
            else {
              if (i.modifier === "+" || i.modifier === "*")
                throw new TypeError('Can not repeat "'.concat(i.name, '" without a prefix and suffix'));
              x += "(".concat(i.pattern, ")").concat(i.modifier);
            }
          else
            x += "(?:".concat(R).concat(y, ")").concat(i.modifier);
        }
      }
      if (m)
        u || (x += "".concat(T, "?")), x += e.endsWith ? "(?=".concat(A, ")") : "$";
      else {
        var b = r[r.length - 1], l = typeof b == "string" ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;
        u || (x += "(?:".concat(T, "(?=").concat(A, "))?")), l || (x += "(?=".concat(T, "|").concat(A, ")"));
      }
      return new RegExp(x, D(e));
    }
    function P(r, n, e) {
      return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);
    }
    var pathToRegexp = (path) => {
      try {
        return P(path);
      } catch (e) {
        throw new Error(
          `Invalid path: ${path}.
Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`
        );
      }
    };
    function match(str, options) {
      try {
        return H(str, options);
      } catch (e) {
        throw new Error(
          `Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`
        );
      }
    }
  }
});

// node_modules/@clerk/shared/dist/authorization-errors.js
var require_authorization_errors = __commonJS({
  "node_modules/@clerk/shared/dist/authorization-errors.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var authorization_errors_exports = {};
    __export2(authorization_errors_exports, {
      isReverificationHint: () => isReverificationHint,
      reverificationError: () => reverificationError2,
      reverificationErrorResponse: () => reverificationErrorResponse2
    });
    module.exports = __toCommonJS2(authorization_errors_exports);
    var REVERIFICATION_REASON = "reverification-error";
    var reverificationError2 = (missingConfig) => ({
      clerk_error: {
        type: "forbidden",
        reason: REVERIFICATION_REASON,
        metadata: {
          reverification: missingConfig
        }
      }
    });
    var reverificationErrorResponse2 = (...args) => new Response(JSON.stringify(reverificationError2(...args)), {
      status: 403
    });
    var isReverificationHint = (result) => {
      var _a, _b;
      return result && typeof result === "object" && "clerk_error" in result && ((_a = result.clerk_error) == null ? void 0 : _a.type) === "forbidden" && ((_b = result.clerk_error) == null ? void 0 : _b.reason) === REVERIFICATION_REASON;
    };
  }
});

// node_modules/@clerk/backend/dist/internal.js
var require_internal = __commonJS({
  "node_modules/@clerk/backend/dist/internal.js"(exports, module) {
    "use strict";
    var __create = Object.create;
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __getProtoOf = Object.getPrototypeOf;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
      // If the importer is in node compatibility mode or this is not an ESM
      // file that has been converted to a CommonJS file using a Babel-
      // compatible transform (i.e. "__esModule" has not been set), then set
      // "default" to the CommonJS "module.exports" for node compatibility.
      isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
      mod
    ));
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var internal_exports = {};
    __export2(internal_exports, {
      AuthStatus: () => AuthStatus,
      TokenType: () => TokenType,
      authenticatedMachineObject: () => authenticatedMachineObject,
      constants: () => constants,
      createAuthenticateRequest: () => createAuthenticateRequest,
      createClerkRequest: () => createClerkRequest,
      createRedirect: () => createRedirect,
      debugRequestState: () => debugRequestState,
      decorateObjectWithResources: () => decorateObjectWithResources,
      getAuthObjectForAcceptedToken: () => getAuthObjectForAcceptedToken,
      getAuthObjectFromJwt: () => getAuthObjectFromJwt,
      getMachineTokenType: () => getMachineTokenType,
      invalidTokenAuthObject: () => invalidTokenAuthObject,
      isMachineTokenByPrefix: () => isMachineTokenByPrefix,
      isMachineTokenType: () => isMachineTokenType,
      isTokenTypeAccepted: () => isTokenTypeAccepted,
      makeAuthObjectSerializable: () => makeAuthObjectSerializable,
      reverificationError: () => import_authorization_errors.reverificationError,
      reverificationErrorResponse: () => import_authorization_errors.reverificationErrorResponse,
      signedInAuthObject: () => signedInAuthObject,
      signedOutAuthObject: () => signedOutAuthObject,
      stripPrivateDataFromObject: () => stripPrivateDataFromObject,
      unauthenticatedMachineObject: () => unauthenticatedMachineObject,
      verifyMachineAuthToken: () => verifyMachineAuthToken
    });
    module.exports = __toCommonJS2(internal_exports);
    var API_URL = "https://api.clerk.com";
    var API_VERSION = "v1";
    var USER_AGENT = `${"@clerk/backend"}@${"2.3.1"}`;
    var MAX_CACHE_LAST_UPDATED_AT_SECONDS = 5 * 60;
    var SUPPORTED_BAPI_VERSION = "2025-04-10";
    var Attributes = {
      AuthToken: "__clerkAuthToken",
      AuthSignature: "__clerkAuthSignature",
      AuthStatus: "__clerkAuthStatus",
      AuthReason: "__clerkAuthReason",
      AuthMessage: "__clerkAuthMessage",
      ClerkUrl: "__clerkUrl"
    };
    var Cookies = {
      Session: "__session",
      Refresh: "__refresh",
      ClientUat: "__client_uat",
      Handshake: "__clerk_handshake",
      DevBrowser: "__clerk_db_jwt",
      RedirectCount: "__clerk_redirect_count",
      HandshakeNonce: "__clerk_handshake_nonce"
    };
    var QueryParameters = {
      ClerkSynced: "__clerk_synced",
      SuffixedCookies: "suffixed_cookies",
      ClerkRedirectUrl: "__clerk_redirect_url",
      // use the reference to Cookies to indicate that it's the same value
      DevBrowser: Cookies.DevBrowser,
      Handshake: Cookies.Handshake,
      HandshakeHelp: "__clerk_help",
      LegacyDevBrowser: "__dev_session",
      HandshakeReason: "__clerk_hs_reason",
      HandshakeNonce: Cookies.HandshakeNonce,
      HandshakeFormat: "format"
    };
    var Headers2 = {
      Accept: "accept",
      AuthMessage: "x-clerk-auth-message",
      Authorization: "authorization",
      AuthReason: "x-clerk-auth-reason",
      AuthSignature: "x-clerk-auth-signature",
      AuthStatus: "x-clerk-auth-status",
      AuthToken: "x-clerk-auth-token",
      CacheControl: "cache-control",
      ClerkRedirectTo: "x-clerk-redirect-to",
      ClerkRequestData: "x-clerk-request-data",
      ClerkUrl: "x-clerk-clerk-url",
      CloudFrontForwardedProto: "cloudfront-forwarded-proto",
      ContentType: "content-type",
      ContentSecurityPolicy: "content-security-policy",
      ContentSecurityPolicyReportOnly: "content-security-policy-report-only",
      EnableDebug: "x-clerk-debug",
      ForwardedHost: "x-forwarded-host",
      ForwardedPort: "x-forwarded-port",
      ForwardedProto: "x-forwarded-proto",
      Host: "host",
      Location: "location",
      Nonce: "x-nonce",
      Origin: "origin",
      Referrer: "referer",
      SecFetchDest: "sec-fetch-dest",
      UserAgent: "user-agent",
      ReportingEndpoints: "reporting-endpoints"
    };
    var ContentTypes = {
      Json: "application/json"
    };
    var constants = {
      Attributes,
      Cookies,
      Headers: Headers2,
      ContentTypes,
      QueryParameters
    };
    var import_buildAccountsBaseUrl = require_buildAccountsBaseUrl();
    var import_url = require_url();
    var import_retry = require_retry();
    var import_keys = require_keys();
    var import_deprecated = require_deprecated();
    var import_error = require_error();
    var import_keys2 = require_keys();
    var errorThrower = (0, import_error.buildErrorThrower)({ packageName: "@clerk/backend" });
    var { isDevOrStagingUrl } = (0, import_keys2.createDevOrStagingUrlCache)();
    var buildUrl = (_baseUrl, _targetUrl, _returnBackUrl, _devBrowserToken) => {
      if (_baseUrl === "") {
        return legacyBuildUrl(_targetUrl.toString(), _returnBackUrl == null ? void 0 : _returnBackUrl.toString());
      }
      const baseUrl = new URL(_baseUrl);
      const returnBackUrl = _returnBackUrl ? new URL(_returnBackUrl, baseUrl) : void 0;
      const res = new URL(_targetUrl, baseUrl);
      const isCrossOriginRedirect = `${baseUrl.hostname}:${baseUrl.port}` !== `${res.hostname}:${res.port}`;
      if (returnBackUrl) {
        if (isCrossOriginRedirect) {
          returnBackUrl.searchParams.delete(constants.QueryParameters.ClerkSynced);
        }
        res.searchParams.set("redirect_url", returnBackUrl.toString());
      }
      if (isCrossOriginRedirect && _devBrowserToken) {
        res.searchParams.set(constants.QueryParameters.DevBrowser, _devBrowserToken);
      }
      return res.toString();
    };
    var legacyBuildUrl = (targetUrl, redirectUrl) => {
      let url;
      if (!targetUrl.startsWith("http")) {
        if (!redirectUrl || !redirectUrl.startsWith("http")) {
          throw new Error("destination url or return back url should be an absolute path url!");
        }
        const baseURL = new URL(redirectUrl);
        url = new URL(targetUrl, baseURL.origin);
      } else {
        url = new URL(targetUrl);
      }
      if (redirectUrl) {
        url.searchParams.set("redirect_url", redirectUrl);
      }
      return url.toString();
    };
    var createRedirect = (params) => {
      const { publishableKey, redirectAdapter, signInUrl, signUpUrl, baseUrl, sessionStatus } = params;
      const parsedPublishableKey = (0, import_keys.parsePublishableKey)(publishableKey);
      const frontendApi = parsedPublishableKey == null ? void 0 : parsedPublishableKey.frontendApi;
      const isDevelopment = (parsedPublishableKey == null ? void 0 : parsedPublishableKey.instanceType) === "development";
      const accountsBaseUrl = (0, import_buildAccountsBaseUrl.buildAccountsBaseUrl)(frontendApi);
      const hasPendingStatus = sessionStatus === "pending";
      const redirectToTasks = (url, { returnBackUrl }) => {
        return redirectAdapter(
          buildUrl(baseUrl, `${url}/tasks`, returnBackUrl, isDevelopment ? params.devBrowserToken : null)
        );
      };
      const redirectToSignUp = ({ returnBackUrl } = {}) => {
        if (!signUpUrl && !accountsBaseUrl) {
          errorThrower.throwMissingPublishableKeyError();
        }
        const accountsSignUpUrl = `${accountsBaseUrl}/sign-up`;
        function buildSignUpUrl(signIn) {
          if (!signIn) {
            return;
          }
          const url = new URL(signIn, baseUrl);
          url.pathname = `${url.pathname}/create`;
          return url.toString();
        }
        const targetUrl = signUpUrl || buildSignUpUrl(signInUrl) || accountsSignUpUrl;
        if (hasPendingStatus) {
          return redirectToTasks(targetUrl, { returnBackUrl });
        }
        return redirectAdapter(buildUrl(baseUrl, targetUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null));
      };
      const redirectToSignIn = ({ returnBackUrl } = {}) => {
        if (!signInUrl && !accountsBaseUrl) {
          errorThrower.throwMissingPublishableKeyError();
        }
        const accountsSignInUrl = `${accountsBaseUrl}/sign-in`;
        const targetUrl = signInUrl || accountsSignInUrl;
        if (hasPendingStatus) {
          return redirectToTasks(targetUrl, { returnBackUrl });
        }
        return redirectAdapter(buildUrl(baseUrl, targetUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null));
      };
      return { redirectToSignUp, redirectToSignIn };
    };
    function mergePreDefinedOptions(preDefinedOptions, options) {
      return Object.keys(preDefinedOptions).reduce(
        (obj, key) => {
          return { ...obj, [key]: options[key] || obj[key] };
        },
        { ...preDefinedOptions }
      );
    }
    var TokenVerificationErrorCode = {
      InvalidSecretKey: "clerk_key_invalid"
    };
    var TokenVerificationErrorReason = {
      TokenExpired: "token-expired",
      TokenInvalid: "token-invalid",
      TokenInvalidAlgorithm: "token-invalid-algorithm",
      TokenInvalidAuthorizedParties: "token-invalid-authorized-parties",
      TokenInvalidSignature: "token-invalid-signature",
      TokenNotActiveYet: "token-not-active-yet",
      TokenIatInTheFuture: "token-iat-in-the-future",
      TokenVerificationFailed: "token-verification-failed",
      InvalidSecretKey: "secret-key-invalid",
      LocalJWKMissing: "jwk-local-missing",
      RemoteJWKFailedToLoad: "jwk-remote-failed-to-load",
      RemoteJWKInvalid: "jwk-remote-invalid",
      RemoteJWKMissing: "jwk-remote-missing",
      JWKFailedToResolve: "jwk-failed-to-resolve",
      JWKKidMismatch: "jwk-kid-mismatch"
    };
    var TokenVerificationErrorAction = {
      ContactSupport: "Contact <EMAIL>",
      EnsureClerkJWT: "Make sure that this is a valid Clerk generate JWT.",
      SetClerkJWTKey: "Set the CLERK_JWT_KEY environment variable.",
      SetClerkSecretKey: "Set the CLERK_SECRET_KEY environment variable.",
      EnsureClockSync: "Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."
    };
    var TokenVerificationError = class _TokenVerificationError extends Error {
      constructor({
        action,
        message,
        reason
      }) {
        super(message);
        Object.setPrototypeOf(this, _TokenVerificationError.prototype);
        this.reason = reason;
        this.message = message;
        this.action = action;
      }
      getFullMessage() {
        return `${[this.message, this.action].filter((m) => m).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`;
      }
    };
    var MachineTokenVerificationErrorCode = {
      TokenInvalid: "token-invalid",
      InvalidSecretKey: "secret-key-invalid",
      UnexpectedError: "unexpected-error"
    };
    var MachineTokenVerificationError = class _MachineTokenVerificationError extends Error {
      constructor({ message, code, status }) {
        super(message);
        Object.setPrototypeOf(this, _MachineTokenVerificationError.prototype);
        this.code = code;
        this.status = status;
      }
      getFullMessage() {
        return `${this.message} (code=${this.code}, status=${this.status})`;
      }
    };
    var import_crypto = (init_crypto(), __toCommonJS(crypto_exports));
    var globalFetch = fetch.bind(globalThis);
    var runtime = {
      crypto: import_crypto.webcrypto,
      get fetch() {
        return false ? fetch : globalFetch;
      },
      AbortController: globalThis.AbortController,
      Blob: globalThis.Blob,
      FormData: globalThis.FormData,
      Headers: globalThis.Headers,
      Request: globalThis.Request,
      Response: globalThis.Response
    };
    var base64url = {
      parse(string, opts) {
        return parse(string, base64UrlEncoding, opts);
      },
      stringify(data, opts) {
        return stringify(data, base64UrlEncoding, opts);
      }
    };
    var base64UrlEncoding = {
      chars: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",
      bits: 6
    };
    function parse(string, encoding, opts = {}) {
      if (!encoding.codes) {
        encoding.codes = {};
        for (let i = 0; i < encoding.chars.length; ++i) {
          encoding.codes[encoding.chars[i]] = i;
        }
      }
      if (!opts.loose && string.length * encoding.bits & 7) {
        throw new SyntaxError("Invalid padding");
      }
      let end = string.length;
      while (string[end - 1] === "=") {
        --end;
        if (!opts.loose && !((string.length - end) * encoding.bits & 7)) {
          throw new SyntaxError("Invalid padding");
        }
      }
      const out = new (opts.out ?? Uint8Array)(end * encoding.bits / 8 | 0);
      let bits = 0;
      let buffer = 0;
      let written = 0;
      for (let i = 0; i < end; ++i) {
        const value = encoding.codes[string[i]];
        if (value === void 0) {
          throw new SyntaxError("Invalid character " + string[i]);
        }
        buffer = buffer << encoding.bits | value;
        bits += encoding.bits;
        if (bits >= 8) {
          bits -= 8;
          out[written++] = 255 & buffer >> bits;
        }
      }
      if (bits >= encoding.bits || 255 & buffer << 8 - bits) {
        throw new SyntaxError("Unexpected end of data");
      }
      return out;
    }
    function stringify(data, encoding, opts = {}) {
      const { pad = true } = opts;
      const mask = (1 << encoding.bits) - 1;
      let out = "";
      let bits = 0;
      let buffer = 0;
      for (let i = 0; i < data.length; ++i) {
        buffer = buffer << 8 | 255 & data[i];
        bits += 8;
        while (bits > encoding.bits) {
          bits -= encoding.bits;
          out += encoding.chars[mask & buffer >> bits];
        }
      }
      if (bits) {
        out += encoding.chars[mask & buffer << encoding.bits - bits];
      }
      if (pad) {
        while (out.length * encoding.bits & 7) {
          out += "=";
        }
      }
      return out;
    }
    var algToHash = {
      RS256: "SHA-256",
      RS384: "SHA-384",
      RS512: "SHA-512"
    };
    var RSA_ALGORITHM_NAME = "RSASSA-PKCS1-v1_5";
    var jwksAlgToCryptoAlg = {
      RS256: RSA_ALGORITHM_NAME,
      RS384: RSA_ALGORITHM_NAME,
      RS512: RSA_ALGORITHM_NAME
    };
    var algs = Object.keys(algToHash);
    function getCryptoAlgorithm(algorithmName) {
      const hash = algToHash[algorithmName];
      const name = jwksAlgToCryptoAlg[algorithmName];
      if (!hash || !name) {
        throw new Error(`Unsupported algorithm ${algorithmName}, expected one of ${algs.join(",")}.`);
      }
      return {
        hash: { name: algToHash[algorithmName] },
        name: jwksAlgToCryptoAlg[algorithmName]
      };
    }
    var isArrayString = (s) => {
      return Array.isArray(s) && s.length > 0 && s.every((a) => typeof a === "string");
    };
    var assertAudienceClaim = (aud, audience) => {
      const audienceList = [audience].flat().filter((a) => !!a);
      const audList = [aud].flat().filter((a) => !!a);
      const shouldVerifyAudience = audienceList.length > 0 && audList.length > 0;
      if (!shouldVerifyAudience) {
        return;
      }
      if (typeof aud === "string") {
        if (!audienceList.includes(aud)) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.EnsureClerkJWT,
            reason: TokenVerificationErrorReason.TokenVerificationFailed,
            message: `Invalid JWT audience claim (aud) ${JSON.stringify(aud)}. Is not included in "${JSON.stringify(
              audienceList
            )}".`
          });
        }
      } else if (isArrayString(aud)) {
        if (!aud.some((a) => audienceList.includes(a))) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.EnsureClerkJWT,
            reason: TokenVerificationErrorReason.TokenVerificationFailed,
            message: `Invalid JWT audience claim array (aud) ${JSON.stringify(aud)}. Is not included in "${JSON.stringify(
              audienceList
            )}".`
          });
        }
      }
    };
    var assertHeaderType = (typ) => {
      if (typeof typ === "undefined") {
        return;
      }
      if (typ !== "JWT") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenInvalid,
          message: `Invalid JWT type ${JSON.stringify(typ)}. Expected "JWT".`
        });
      }
    };
    var assertHeaderAlgorithm = (alg) => {
      if (!algs.includes(alg)) {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenInvalidAlgorithm,
          message: `Invalid JWT algorithm ${JSON.stringify(alg)}. Supported: ${algs}.`
        });
      }
    };
    var assertSubClaim = (sub) => {
      if (typeof sub !== "string") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Subject claim (sub) is required and must be a string. Received ${JSON.stringify(sub)}.`
        });
      }
    };
    var assertAuthorizedPartiesClaim = (azp, authorizedParties) => {
      if (!azp || !authorizedParties || authorizedParties.length === 0) {
        return;
      }
      if (!authorizedParties.includes(azp)) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenInvalidAuthorizedParties,
          message: `Invalid JWT Authorized party claim (azp) ${JSON.stringify(azp)}. Expected "${authorizedParties}".`
        });
      }
    };
    var assertExpirationClaim = (exp, clockSkewInMs) => {
      if (typeof exp !== "number") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Invalid JWT expiry date claim (exp) ${JSON.stringify(exp)}. Expected number.`
        });
      }
      const currentDate = new Date(Date.now());
      const expiryDate = /* @__PURE__ */ new Date(0);
      expiryDate.setUTCSeconds(exp);
      const expired = expiryDate.getTime() <= currentDate.getTime() - clockSkewInMs;
      if (expired) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenExpired,
          message: `JWT is expired. Expiry date: ${expiryDate.toUTCString()}, Current date: ${currentDate.toUTCString()}.`
        });
      }
    };
    var assertActivationClaim = (nbf, clockSkewInMs) => {
      if (typeof nbf === "undefined") {
        return;
      }
      if (typeof nbf !== "number") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Invalid JWT not before date claim (nbf) ${JSON.stringify(nbf)}. Expected number.`
        });
      }
      const currentDate = new Date(Date.now());
      const notBeforeDate = /* @__PURE__ */ new Date(0);
      notBeforeDate.setUTCSeconds(nbf);
      const early = notBeforeDate.getTime() > currentDate.getTime() + clockSkewInMs;
      if (early) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenNotActiveYet,
          message: `JWT cannot be used prior to not before date claim (nbf). Not before date: ${notBeforeDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`
        });
      }
    };
    var assertIssuedAtClaim = (iat, clockSkewInMs) => {
      if (typeof iat === "undefined") {
        return;
      }
      if (typeof iat !== "number") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Invalid JWT issued at date claim (iat) ${JSON.stringify(iat)}. Expected number.`
        });
      }
      const currentDate = new Date(Date.now());
      const issuedAtDate = /* @__PURE__ */ new Date(0);
      issuedAtDate.setUTCSeconds(iat);
      const postIssued = issuedAtDate.getTime() > currentDate.getTime() + clockSkewInMs;
      if (postIssued) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenIatInTheFuture,
          message: `JWT issued at date claim (iat) is in the future. Issued at date: ${issuedAtDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`
        });
      }
    };
    var import_isomorphicAtob = require_isomorphicAtob();
    function pemToBuffer(secret) {
      const trimmed = secret.replace(/-----BEGIN.*?-----/g, "").replace(/-----END.*?-----/g, "").replace(/\s/g, "");
      const decoded = (0, import_isomorphicAtob.isomorphicAtob)(trimmed);
      const buffer = new ArrayBuffer(decoded.length);
      const bufView = new Uint8Array(buffer);
      for (let i = 0, strLen = decoded.length; i < strLen; i++) {
        bufView[i] = decoded.charCodeAt(i);
      }
      return bufView;
    }
    function importKey(key, algorithm, keyUsage) {
      if (typeof key === "object") {
        return runtime.crypto.subtle.importKey("jwk", key, algorithm, false, [keyUsage]);
      }
      const keyData = pemToBuffer(key);
      const format = keyUsage === "sign" ? "pkcs8" : "spki";
      return runtime.crypto.subtle.importKey(format, keyData, algorithm, false, [keyUsage]);
    }
    var DEFAULT_CLOCK_SKEW_IN_MS = 5 * 1e3;
    async function hasValidSignature(jwt, key) {
      const { header, signature, raw } = jwt;
      const encoder = new TextEncoder();
      const data = encoder.encode([raw.header, raw.payload].join("."));
      const algorithm = getCryptoAlgorithm(header.alg);
      try {
        const cryptoKey = await importKey(key, algorithm, "verify");
        const verified = await runtime.crypto.subtle.verify(algorithm.name, cryptoKey, signature, data);
        return { data: verified };
      } catch (error) {
        return {
          errors: [
            new TokenVerificationError({
              reason: TokenVerificationErrorReason.TokenInvalidSignature,
              message: error == null ? void 0 : error.message
            })
          ]
        };
      }
    }
    function decodeJwt(token) {
      const tokenParts = (token || "").toString().split(".");
      if (tokenParts.length !== 3) {
        return {
          errors: [
            new TokenVerificationError({
              reason: TokenVerificationErrorReason.TokenInvalid,
              message: `Invalid JWT form. A JWT consists of three parts separated by dots.`
            })
          ]
        };
      }
      const [rawHeader, rawPayload, rawSignature] = tokenParts;
      const decoder = new TextDecoder();
      const header = JSON.parse(decoder.decode(base64url.parse(rawHeader, { loose: true })));
      const payload = JSON.parse(decoder.decode(base64url.parse(rawPayload, { loose: true })));
      const signature = base64url.parse(rawSignature, { loose: true });
      const data = {
        header,
        payload,
        signature,
        raw: {
          header: rawHeader,
          payload: rawPayload,
          signature: rawSignature,
          text: token
        }
      };
      return { data };
    }
    async function verifyJwt(token, options) {
      const { audience, authorizedParties, clockSkewInMs, key } = options;
      const clockSkew = clockSkewInMs || DEFAULT_CLOCK_SKEW_IN_MS;
      const { data: decoded, errors } = decodeJwt(token);
      if (errors) {
        return { errors };
      }
      const { header, payload } = decoded;
      try {
        const { typ, alg } = header;
        assertHeaderType(typ);
        assertHeaderAlgorithm(alg);
        const { azp, sub, aud, iat, exp, nbf } = payload;
        assertSubClaim(sub);
        assertAudienceClaim([aud], [audience]);
        assertAuthorizedPartiesClaim(azp, authorizedParties);
        assertExpirationClaim(exp, clockSkew);
        assertActivationClaim(nbf, clockSkew);
        assertIssuedAtClaim(iat, clockSkew);
      } catch (err) {
        return { errors: [err] };
      }
      const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);
      if (signatureErrors) {
        return {
          errors: [
            new TokenVerificationError({
              action: TokenVerificationErrorAction.EnsureClerkJWT,
              reason: TokenVerificationErrorReason.TokenVerificationFailed,
              message: `Error verifying JWT signature. ${signatureErrors[0]}`
            })
          ]
        };
      }
      if (!signatureValid) {
        return {
          errors: [
            new TokenVerificationError({
              reason: TokenVerificationErrorReason.TokenInvalidSignature,
              message: "JWT signature is invalid."
            })
          ]
        };
      }
      return { data: payload };
    }
    function assertValidSecretKey(val) {
      if (!val || typeof val !== "string") {
        throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.");
      }
    }
    function assertValidPublishableKey(val) {
      (0, import_keys.parsePublishableKey)(val, { fatal: true });
    }
    var AuthenticateContext = class {
      constructor(cookieSuffix, clerkRequest, options) {
        this.cookieSuffix = cookieSuffix;
        this.clerkRequest = clerkRequest;
        this.originalFrontendApi = "";
        this.initPublishableKeyValues(options);
        this.initHeaderValues();
        this.initCookieValues();
        this.initHandshakeValues();
        Object.assign(this, options);
        this.clerkUrl = this.clerkRequest.clerkUrl;
      }
      /**
       * Retrieves the session token from either the cookie or the header.
       *
       * @returns {string | undefined} The session token if available, otherwise undefined.
       */
      get sessionToken() {
        return this.sessionTokenInCookie || this.tokenInHeader;
      }
      usesSuffixedCookies() {
        const suffixedClientUat = this.getSuffixedCookie(constants.Cookies.ClientUat);
        const clientUat = this.getCookie(constants.Cookies.ClientUat);
        const suffixedSession = this.getSuffixedCookie(constants.Cookies.Session) || "";
        const session = this.getCookie(constants.Cookies.Session) || "";
        if (session && !this.tokenHasIssuer(session)) {
          return false;
        }
        if (session && !this.tokenBelongsToInstance(session)) {
          return true;
        }
        if (!suffixedClientUat && !suffixedSession) {
          return false;
        }
        const { data: sessionData } = decodeJwt(session);
        const sessionIat = (sessionData == null ? void 0 : sessionData.payload.iat) || 0;
        const { data: suffixedSessionData } = decodeJwt(suffixedSession);
        const suffixedSessionIat = (suffixedSessionData == null ? void 0 : suffixedSessionData.payload.iat) || 0;
        if (suffixedClientUat !== "0" && clientUat !== "0" && sessionIat > suffixedSessionIat) {
          return false;
        }
        if (suffixedClientUat === "0" && clientUat !== "0") {
          return false;
        }
        if (this.instanceType !== "production") {
          const isSuffixedSessionExpired = this.sessionExpired(suffixedSessionData);
          if (suffixedClientUat !== "0" && clientUat === "0" && isSuffixedSessionExpired) {
            return false;
          }
        }
        if (!suffixedClientUat && suffixedSession) {
          return false;
        }
        return true;
      }
      initPublishableKeyValues(options) {
        assertValidPublishableKey(options.publishableKey);
        this.publishableKey = options.publishableKey;
        const originalPk = (0, import_keys.parsePublishableKey)(this.publishableKey, {
          fatal: true,
          domain: options.domain,
          isSatellite: options.isSatellite
        });
        this.originalFrontendApi = originalPk.frontendApi;
        const pk = (0, import_keys.parsePublishableKey)(this.publishableKey, {
          fatal: true,
          proxyUrl: options.proxyUrl,
          domain: options.domain,
          isSatellite: options.isSatellite
        });
        this.instanceType = pk.instanceType;
        this.frontendApi = pk.frontendApi;
      }
      initHeaderValues() {
        this.tokenInHeader = this.parseAuthorizationHeader(this.getHeader(constants.Headers.Authorization));
        this.origin = this.getHeader(constants.Headers.Origin);
        this.host = this.getHeader(constants.Headers.Host);
        this.forwardedHost = this.getHeader(constants.Headers.ForwardedHost);
        this.forwardedProto = this.getHeader(constants.Headers.CloudFrontForwardedProto) || this.getHeader(constants.Headers.ForwardedProto);
        this.referrer = this.getHeader(constants.Headers.Referrer);
        this.userAgent = this.getHeader(constants.Headers.UserAgent);
        this.secFetchDest = this.getHeader(constants.Headers.SecFetchDest);
        this.accept = this.getHeader(constants.Headers.Accept);
      }
      initCookieValues() {
        this.sessionTokenInCookie = this.getSuffixedOrUnSuffixedCookie(constants.Cookies.Session);
        this.refreshTokenInCookie = this.getSuffixedCookie(constants.Cookies.Refresh);
        this.clientUat = Number.parseInt(this.getSuffixedOrUnSuffixedCookie(constants.Cookies.ClientUat) || "") || 0;
      }
      initHandshakeValues() {
        this.devBrowserToken = this.getQueryParam(constants.QueryParameters.DevBrowser) || this.getSuffixedOrUnSuffixedCookie(constants.Cookies.DevBrowser);
        this.handshakeToken = this.getQueryParam(constants.QueryParameters.Handshake) || this.getCookie(constants.Cookies.Handshake);
        this.handshakeRedirectLoopCounter = Number(this.getCookie(constants.Cookies.RedirectCount)) || 0;
        this.handshakeNonce = this.getQueryParam(constants.QueryParameters.HandshakeNonce) || this.getCookie(constants.Cookies.HandshakeNonce);
      }
      getQueryParam(name) {
        return this.clerkRequest.clerkUrl.searchParams.get(name);
      }
      getHeader(name) {
        return this.clerkRequest.headers.get(name) || void 0;
      }
      getCookie(name) {
        return this.clerkRequest.cookies.get(name) || void 0;
      }
      getSuffixedCookie(name) {
        return this.getCookie((0, import_keys.getSuffixedCookieName)(name, this.cookieSuffix)) || void 0;
      }
      getSuffixedOrUnSuffixedCookie(cookieName) {
        if (this.usesSuffixedCookies()) {
          return this.getSuffixedCookie(cookieName);
        }
        return this.getCookie(cookieName);
      }
      parseAuthorizationHeader(authorizationHeader) {
        if (!authorizationHeader) {
          return void 0;
        }
        const [scheme, token] = authorizationHeader.split(" ", 2);
        if (!token) {
          return scheme;
        }
        if (scheme === "Bearer") {
          return token;
        }
        return void 0;
      }
      tokenHasIssuer(token) {
        const { data, errors } = decodeJwt(token);
        if (errors) {
          return false;
        }
        return !!data.payload.iss;
      }
      tokenBelongsToInstance(token) {
        if (!token) {
          return false;
        }
        const { data, errors } = decodeJwt(token);
        if (errors) {
          return false;
        }
        const tokenIssuer = data.payload.iss.replace(/https?:\/\//gi, "");
        return this.originalFrontendApi === tokenIssuer;
      }
      sessionExpired(jwt) {
        return !!jwt && (jwt == null ? void 0 : jwt.payload.exp) <= Date.now() / 1e3 >> 0;
      }
    };
    var createAuthenticateContext = async (clerkRequest, options) => {
      const cookieSuffix = options.publishableKey ? await (0, import_keys.getCookieSuffix)(options.publishableKey, runtime.crypto.subtle) : "";
      return new AuthenticateContext(cookieSuffix, clerkRequest, options);
    };
    var import_authorization = require_authorization();
    var import_jwtPayloadParser = require_jwtPayloadParser();
    var SEPARATOR = "/";
    var MULTIPLE_SEPARATOR_REGEX = new RegExp("(?<!:)" + SEPARATOR + "{1,}", "g");
    function joinPaths(...args) {
      return args.filter((p) => p).join(SEPARATOR).replace(MULTIPLE_SEPARATOR_REGEX, SEPARATOR);
    }
    var AbstractAPI = class {
      constructor(request) {
        this.request = request;
      }
      requireId(id) {
        if (!id) {
          throw new Error("A valid resource ID is required.");
        }
      }
    };
    var basePath = "/actor_tokens";
    var ActorTokenAPI = class extends AbstractAPI {
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath,
          bodyParams: params
        });
      }
      async revoke(actorTokenId) {
        this.requireId(actorTokenId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath, actorTokenId, "revoke")
        });
      }
    };
    var basePath2 = "/accountless_applications";
    var AccountlessApplicationAPI = class extends AbstractAPI {
      async createAccountlessApplication() {
        return this.request({
          method: "POST",
          path: basePath2
        });
      }
      async completeAccountlessApplicationOnboarding() {
        return this.request({
          method: "POST",
          path: joinPaths(basePath2, "complete")
        });
      }
    };
    var basePath3 = "/allowlist_identifiers";
    var AllowlistIdentifierAPI = class extends AbstractAPI {
      async getAllowlistIdentifierList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath3,
          queryParams: { ...params, paginated: true }
        });
      }
      async createAllowlistIdentifier(params) {
        return this.request({
          method: "POST",
          path: basePath3,
          bodyParams: params
        });
      }
      async deleteAllowlistIdentifier(allowlistIdentifierId) {
        this.requireId(allowlistIdentifierId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath3, allowlistIdentifierId)
        });
      }
    };
    var basePath4 = "/api_keys";
    var APIKeysAPI = class extends AbstractAPI {
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath4,
          bodyParams: params
        });
      }
      async revoke(params) {
        const { apiKeyId, ...bodyParams } = params;
        this.requireId(apiKeyId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath4, apiKeyId, "revoke"),
          bodyParams
        });
      }
      async getSecret(apiKeyId) {
        this.requireId(apiKeyId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath4, apiKeyId, "secret")
        });
      }
      async verifySecret(secret) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath4, "verify"),
          bodyParams: { secret }
        });
      }
    };
    var basePath5 = "/beta_features";
    var BetaFeaturesAPI = class extends AbstractAPI {
      /**
       * Change the domain of a production instance.
       *
       * Changing the domain requires updating the DNS records accordingly, deploying new SSL certificates,
       * updating your Social Connection's redirect URLs and setting the new keys in your code.
       *
       * @remarks
       * WARNING: Changing your domain will invalidate all current user sessions (i.e. users will be logged out).
       *          Also, while your application is being deployed, a small downtime is expected to occur.
       */
      async changeDomain(params) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath5, "change_domain"),
          bodyParams: params
        });
      }
    };
    var basePath6 = "/blocklist_identifiers";
    var BlocklistIdentifierAPI = class extends AbstractAPI {
      async getBlocklistIdentifierList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath6,
          queryParams: params
        });
      }
      async createBlocklistIdentifier(params) {
        return this.request({
          method: "POST",
          path: basePath6,
          bodyParams: params
        });
      }
      async deleteBlocklistIdentifier(blocklistIdentifierId) {
        this.requireId(blocklistIdentifierId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath6, blocklistIdentifierId)
        });
      }
    };
    var basePath7 = "/clients";
    var ClientAPI = class extends AbstractAPI {
      async getClientList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath7,
          queryParams: { ...params, paginated: true }
        });
      }
      async getClient(clientId) {
        this.requireId(clientId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath7, clientId)
        });
      }
      verifyClient(token) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath7, "verify"),
          bodyParams: { token }
        });
      }
      async getHandshakePayload(queryParams) {
        return this.request({
          method: "GET",
          path: joinPaths(basePath7, "handshake_payload"),
          queryParams
        });
      }
    };
    var basePath8 = "/domains";
    var DomainAPI = class extends AbstractAPI {
      async list() {
        return this.request({
          method: "GET",
          path: basePath8
        });
      }
      async add(params) {
        return this.request({
          method: "POST",
          path: basePath8,
          bodyParams: params
        });
      }
      async update(params) {
        const { domainId, ...bodyParams } = params;
        this.requireId(domainId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath8, domainId),
          bodyParams
        });
      }
      /**
       * Deletes a satellite domain for the instance.
       * It is currently not possible to delete the instance's primary domain.
       */
      async delete(satelliteDomainId) {
        return this.deleteDomain(satelliteDomainId);
      }
      /**
       * @deprecated Use `delete` instead
       */
      async deleteDomain(satelliteDomainId) {
        this.requireId(satelliteDomainId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath8, satelliteDomainId)
        });
      }
    };
    var basePath9 = "/email_addresses";
    var EmailAddressAPI = class extends AbstractAPI {
      async getEmailAddress(emailAddressId) {
        this.requireId(emailAddressId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath9, emailAddressId)
        });
      }
      async createEmailAddress(params) {
        return this.request({
          method: "POST",
          path: basePath9,
          bodyParams: params
        });
      }
      async updateEmailAddress(emailAddressId, params = {}) {
        this.requireId(emailAddressId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath9, emailAddressId),
          bodyParams: params
        });
      }
      async deleteEmailAddress(emailAddressId) {
        this.requireId(emailAddressId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath9, emailAddressId)
        });
      }
    };
    var basePath10 = "/oauth_applications/access_tokens";
    var IdPOAuthAccessTokenApi = class extends AbstractAPI {
      async verifyAccessToken(accessToken) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath10, "verify"),
          bodyParams: { access_token: accessToken }
        });
      }
    };
    var basePath11 = "/instance";
    var InstanceAPI = class extends AbstractAPI {
      async get() {
        return this.request({
          method: "GET",
          path: basePath11
        });
      }
      async update(params) {
        return this.request({
          method: "PATCH",
          path: basePath11,
          bodyParams: params
        });
      }
      async updateRestrictions(params) {
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath11, "restrictions"),
          bodyParams: params
        });
      }
      async updateOrganizationSettings(params) {
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath11, "organization_settings"),
          bodyParams: params
        });
      }
    };
    var basePath12 = "/invitations";
    var InvitationAPI = class extends AbstractAPI {
      async getInvitationList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath12,
          queryParams: { ...params, paginated: true }
        });
      }
      async createInvitation(params) {
        return this.request({
          method: "POST",
          path: basePath12,
          bodyParams: params
        });
      }
      async revokeInvitation(invitationId) {
        this.requireId(invitationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath12, invitationId, "revoke")
        });
      }
    };
    var basePath13 = "/m2m_tokens";
    var MachineTokensApi = class extends AbstractAPI {
      async verifySecret(secret) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath13, "verify"),
          bodyParams: { secret }
        });
      }
    };
    var basePath14 = "/jwks";
    var JwksAPI = class extends AbstractAPI {
      async getJwks() {
        return this.request({
          method: "GET",
          path: basePath14
        });
      }
    };
    var basePath15 = "/jwt_templates";
    var JwtTemplatesApi = class extends AbstractAPI {
      async list(params = {}) {
        return this.request({
          method: "GET",
          path: basePath15,
          queryParams: { ...params, paginated: true }
        });
      }
      async get(templateId) {
        this.requireId(templateId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath15, templateId)
        });
      }
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath15,
          bodyParams: params
        });
      }
      async update(params) {
        const { templateId, ...bodyParams } = params;
        this.requireId(templateId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath15, templateId),
          bodyParams
        });
      }
      async delete(templateId) {
        this.requireId(templateId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath15, templateId)
        });
      }
    };
    var basePath16 = "/organizations";
    var OrganizationAPI = class extends AbstractAPI {
      async getOrganizationList(params) {
        return this.request({
          method: "GET",
          path: basePath16,
          queryParams: params
        });
      }
      async createOrganization(params) {
        return this.request({
          method: "POST",
          path: basePath16,
          bodyParams: params
        });
      }
      async getOrganization(params) {
        const { includeMembersCount } = params;
        const organizationIdOrSlug = "organizationId" in params ? params.organizationId : params.slug;
        this.requireId(organizationIdOrSlug);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationIdOrSlug),
          queryParams: {
            includeMembersCount
          }
        });
      }
      async updateOrganization(organizationId, params) {
        this.requireId(organizationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId),
          bodyParams: params
        });
      }
      async updateOrganizationLogo(organizationId, params) {
        this.requireId(organizationId);
        const formData = new runtime.FormData();
        formData.append("file", params == null ? void 0 : params.file);
        if (params == null ? void 0 : params.uploaderUserId) {
          formData.append("uploader_user_id", params == null ? void 0 : params.uploaderUserId);
        }
        return this.request({
          method: "PUT",
          path: joinPaths(basePath16, organizationId, "logo"),
          formData
        });
      }
      async deleteOrganizationLogo(organizationId) {
        this.requireId(organizationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId, "logo")
        });
      }
      async updateOrganizationMetadata(organizationId, params) {
        this.requireId(organizationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "metadata"),
          bodyParams: params
        });
      }
      async deleteOrganization(organizationId) {
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId)
        });
      }
      async getOrganizationMembershipList(params) {
        const { organizationId, ...queryParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "memberships"),
          queryParams
        });
      }
      async getInstanceOrganizationMembershipList(params) {
        return this.request({
          method: "GET",
          path: "/organization_memberships",
          queryParams: params
        });
      }
      async createOrganizationMembership(params) {
        const { organizationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "memberships"),
          bodyParams
        });
      }
      async updateOrganizationMembership(params) {
        const { organizationId, userId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "memberships", userId),
          bodyParams
        });
      }
      async updateOrganizationMembershipMetadata(params) {
        const { organizationId, userId, ...bodyParams } = params;
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "memberships", userId, "metadata"),
          bodyParams
        });
      }
      async deleteOrganizationMembership(params) {
        const { organizationId, userId } = params;
        this.requireId(organizationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId, "memberships", userId)
        });
      }
      async getOrganizationInvitationList(params) {
        const { organizationId, ...queryParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "invitations"),
          queryParams
        });
      }
      async createOrganizationInvitation(params) {
        const { organizationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "invitations"),
          bodyParams
        });
      }
      async createOrganizationInvitationBulk(organizationId, params) {
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "invitations", "bulk"),
          bodyParams: params
        });
      }
      async getOrganizationInvitation(params) {
        const { organizationId, invitationId } = params;
        this.requireId(organizationId);
        this.requireId(invitationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "invitations", invitationId)
        });
      }
      async revokeOrganizationInvitation(params) {
        const { organizationId, invitationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "invitations", invitationId, "revoke"),
          bodyParams
        });
      }
      async getOrganizationDomainList(params) {
        const { organizationId, ...queryParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "domains"),
          queryParams
        });
      }
      async createOrganizationDomain(params) {
        const { organizationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "domains"),
          bodyParams: {
            ...bodyParams,
            verified: bodyParams.verified ?? true
          }
        });
      }
      async updateOrganizationDomain(params) {
        const { organizationId, domainId, ...bodyParams } = params;
        this.requireId(organizationId);
        this.requireId(domainId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "domains", domainId),
          bodyParams
        });
      }
      async deleteOrganizationDomain(params) {
        const { organizationId, domainId } = params;
        this.requireId(organizationId);
        this.requireId(domainId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId, "domains", domainId)
        });
      }
    };
    var basePath17 = "/oauth_applications";
    var OAuthApplicationsApi = class extends AbstractAPI {
      async list(params = {}) {
        return this.request({
          method: "GET",
          path: basePath17,
          queryParams: params
        });
      }
      async get(oauthApplicationId) {
        this.requireId(oauthApplicationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath17, oauthApplicationId)
        });
      }
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath17,
          bodyParams: params
        });
      }
      async update(params) {
        const { oauthApplicationId, ...bodyParams } = params;
        this.requireId(oauthApplicationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath17, oauthApplicationId),
          bodyParams
        });
      }
      async delete(oauthApplicationId) {
        this.requireId(oauthApplicationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath17, oauthApplicationId)
        });
      }
      async rotateSecret(oauthApplicationId) {
        this.requireId(oauthApplicationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath17, oauthApplicationId, "rotate_secret")
        });
      }
    };
    var basePath18 = "/phone_numbers";
    var PhoneNumberAPI = class extends AbstractAPI {
      async getPhoneNumber(phoneNumberId) {
        this.requireId(phoneNumberId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath18, phoneNumberId)
        });
      }
      async createPhoneNumber(params) {
        return this.request({
          method: "POST",
          path: basePath18,
          bodyParams: params
        });
      }
      async updatePhoneNumber(phoneNumberId, params = {}) {
        this.requireId(phoneNumberId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath18, phoneNumberId),
          bodyParams: params
        });
      }
      async deletePhoneNumber(phoneNumberId) {
        this.requireId(phoneNumberId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath18, phoneNumberId)
        });
      }
    };
    var basePath19 = "/proxy_checks";
    var ProxyCheckAPI = class extends AbstractAPI {
      async verify(params) {
        return this.request({
          method: "POST",
          path: basePath19,
          bodyParams: params
        });
      }
    };
    var basePath20 = "/redirect_urls";
    var RedirectUrlAPI = class extends AbstractAPI {
      async getRedirectUrlList() {
        return this.request({
          method: "GET",
          path: basePath20,
          queryParams: { paginated: true }
        });
      }
      async getRedirectUrl(redirectUrlId) {
        this.requireId(redirectUrlId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath20, redirectUrlId)
        });
      }
      async createRedirectUrl(params) {
        return this.request({
          method: "POST",
          path: basePath20,
          bodyParams: params
        });
      }
      async deleteRedirectUrl(redirectUrlId) {
        this.requireId(redirectUrlId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath20, redirectUrlId)
        });
      }
    };
    var basePath21 = "/saml_connections";
    var SamlConnectionAPI = class extends AbstractAPI {
      async getSamlConnectionList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath21,
          queryParams: params
        });
      }
      async createSamlConnection(params) {
        return this.request({
          method: "POST",
          path: basePath21,
          bodyParams: params
        });
      }
      async getSamlConnection(samlConnectionId) {
        this.requireId(samlConnectionId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath21, samlConnectionId)
        });
      }
      async updateSamlConnection(samlConnectionId, params = {}) {
        this.requireId(samlConnectionId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath21, samlConnectionId),
          bodyParams: params
        });
      }
      async deleteSamlConnection(samlConnectionId) {
        this.requireId(samlConnectionId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath21, samlConnectionId)
        });
      }
    };
    var basePath22 = "/sessions";
    var SessionAPI = class extends AbstractAPI {
      async getSessionList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath22,
          queryParams: { ...params, paginated: true }
        });
      }
      async getSession(sessionId) {
        this.requireId(sessionId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath22, sessionId)
        });
      }
      async createSession(params) {
        return this.request({
          method: "POST",
          path: basePath22,
          bodyParams: params
        });
      }
      async revokeSession(sessionId) {
        this.requireId(sessionId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath22, sessionId, "revoke")
        });
      }
      async verifySession(sessionId, token) {
        this.requireId(sessionId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath22, sessionId, "verify"),
          bodyParams: { token }
        });
      }
      /**
       * Retrieves a session token or generates a JWT using a specified template.
       *
       * @param sessionId - The ID of the session for which to generate the token
       * @param template - Optional name of the JWT template configured in the Clerk Dashboard.
       * @param expiresInSeconds - Optional expiration time for the token in seconds.
       *   If not provided, uses the default expiration.
       *
       * @returns A promise that resolves to the generated token
       *
       * @throws {Error} When sessionId is invalid or empty
       */
      async getToken(sessionId, template, expiresInSeconds) {
        this.requireId(sessionId);
        const path = template ? joinPaths(basePath22, sessionId, "tokens", template) : joinPaths(basePath22, sessionId, "tokens");
        const requestOptions = {
          method: "POST",
          path
        };
        if (expiresInSeconds !== void 0) {
          requestOptions.bodyParams = { expires_in_seconds: expiresInSeconds };
        }
        return this.request(requestOptions);
      }
      async refreshSession(sessionId, params) {
        this.requireId(sessionId);
        const { suffixed_cookies, ...restParams } = params;
        return this.request({
          method: "POST",
          path: joinPaths(basePath22, sessionId, "refresh"),
          bodyParams: restParams,
          queryParams: { suffixed_cookies }
        });
      }
    };
    var basePath23 = "/sign_in_tokens";
    var SignInTokenAPI = class extends AbstractAPI {
      async createSignInToken(params) {
        return this.request({
          method: "POST",
          path: basePath23,
          bodyParams: params
        });
      }
      async revokeSignInToken(signInTokenId) {
        this.requireId(signInTokenId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath23, signInTokenId, "revoke")
        });
      }
    };
    var basePath24 = "/sign_ups";
    var SignUpAPI = class extends AbstractAPI {
      async get(signUpAttemptId) {
        this.requireId(signUpAttemptId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath24, signUpAttemptId)
        });
      }
      async update(params) {
        const { signUpAttemptId, ...bodyParams } = params;
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath24, signUpAttemptId),
          bodyParams
        });
      }
    };
    var basePath25 = "/testing_tokens";
    var TestingTokenAPI = class extends AbstractAPI {
      async createTestingToken() {
        return this.request({
          method: "POST",
          path: basePath25
        });
      }
    };
    var basePath26 = "/users";
    var UserAPI = class extends AbstractAPI {
      async getUserList(params = {}) {
        const { limit, offset, orderBy, ...userCountParams } = params;
        const [data, totalCount] = await Promise.all([
          this.request({
            method: "GET",
            path: basePath26,
            queryParams: params
          }),
          this.getCount(userCountParams)
        ]);
        return { data, totalCount };
      }
      async getUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId)
        });
      }
      async createUser(params) {
        return this.request({
          method: "POST",
          path: basePath26,
          bodyParams: params
        });
      }
      async updateUser(userId, params = {}) {
        this.requireId(userId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath26, userId),
          bodyParams: params
        });
      }
      async updateUserProfileImage(userId, params) {
        this.requireId(userId);
        const formData = new runtime.FormData();
        formData.append("file", params == null ? void 0 : params.file);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "profile_image"),
          formData
        });
      }
      async updateUserMetadata(userId, params) {
        this.requireId(userId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath26, userId, "metadata"),
          bodyParams: params
        });
      }
      async deleteUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId)
        });
      }
      async getCount(params = {}) {
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, "count"),
          queryParams: params
        });
      }
      async getUserOauthAccessToken(userId, provider) {
        this.requireId(userId);
        const hasPrefix = provider.startsWith("oauth_");
        const _provider = hasPrefix ? provider : `oauth_${provider}`;
        if (hasPrefix) {
          (0, import_deprecated.deprecated)(
            "getUserOauthAccessToken(userId, provider)",
            "Remove the `oauth_` prefix from the `provider` argument."
          );
        }
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId, "oauth_access_tokens", _provider),
          queryParams: { paginated: true }
        });
      }
      async disableUserMFA(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "mfa")
        });
      }
      async getOrganizationMembershipList(params) {
        const { userId, limit, offset } = params;
        this.requireId(userId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId, "organization_memberships"),
          queryParams: { limit, offset }
        });
      }
      async getOrganizationInvitationList(params) {
        const { userId, ...queryParams } = params;
        this.requireId(userId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId, "organization_invitations"),
          queryParams
        });
      }
      async verifyPassword(params) {
        const { userId, password } = params;
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "verify_password"),
          bodyParams: { password }
        });
      }
      async verifyTOTP(params) {
        const { userId, code } = params;
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "verify_totp"),
          bodyParams: { code }
        });
      }
      async banUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "ban")
        });
      }
      async unbanUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "unban")
        });
      }
      async lockUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "lock")
        });
      }
      async unlockUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "unlock")
        });
      }
      async deleteUserProfileImage(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "profile_image")
        });
      }
      async deleteUserPasskey(params) {
        this.requireId(params.userId);
        this.requireId(params.passkeyIdentificationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, params.userId, "passkeys", params.passkeyIdentificationId)
        });
      }
      async deleteUserWeb3Wallet(params) {
        this.requireId(params.userId);
        this.requireId(params.web3WalletIdentificationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, params.userId, "web3_wallets", params.web3WalletIdentificationId)
        });
      }
      async deleteUserExternalAccount(params) {
        this.requireId(params.userId);
        this.requireId(params.externalAccountId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, params.userId, "external_accounts", params.externalAccountId)
        });
      }
      async deleteUserBackupCodes(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "backup_code")
        });
      }
      async deleteUserTOTP(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "totp")
        });
      }
    };
    var basePath27 = "/waitlist_entries";
    var WaitlistEntryAPI = class extends AbstractAPI {
      async list(params = {}) {
        return this.request({
          method: "GET",
          path: basePath27,
          queryParams: params
        });
      }
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath27,
          bodyParams: params
        });
      }
    };
    var basePath28 = "/webhooks";
    var WebhookAPI = class extends AbstractAPI {
      async createSvixApp() {
        return this.request({
          method: "POST",
          path: joinPaths(basePath28, "svix")
        });
      }
      async generateSvixAuthURL() {
        return this.request({
          method: "POST",
          path: joinPaths(basePath28, "svix_url")
        });
      }
      async deleteSvixApp() {
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath28, "svix")
        });
      }
    };
    var import_error2 = require_error();
    var import_snakecase_keys = __toESM(require_snakecase_keys());
    var AccountlessApplication = class _AccountlessApplication {
      constructor(publishableKey, secretKey, claimUrl, apiKeysUrl) {
        this.publishableKey = publishableKey;
        this.secretKey = secretKey;
        this.claimUrl = claimUrl;
        this.apiKeysUrl = apiKeysUrl;
      }
      static fromJSON(data) {
        return new _AccountlessApplication(data.publishable_key, data.secret_key, data.claim_url, data.api_keys_url);
      }
    };
    var ActorToken = class _ActorToken {
      constructor(id, status, userId, actor, token, url, createdAt, updatedAt) {
        this.id = id;
        this.status = status;
        this.userId = userId;
        this.actor = actor;
        this.token = token;
        this.url = url;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _ActorToken(
          data.id,
          data.status,
          data.user_id,
          data.actor,
          data.token,
          data.url,
          data.created_at,
          data.updated_at
        );
      }
    };
    var AllowlistIdentifier = class _AllowlistIdentifier {
      constructor(id, identifier, identifierType, createdAt, updatedAt, instanceId, invitationId) {
        this.id = id;
        this.identifier = identifier;
        this.identifierType = identifierType;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.instanceId = instanceId;
        this.invitationId = invitationId;
      }
      static fromJSON(data) {
        return new _AllowlistIdentifier(
          data.id,
          data.identifier,
          data.identifier_type,
          data.created_at,
          data.updated_at,
          data.instance_id,
          data.invitation_id
        );
      }
    };
    var APIKey = class _APIKey {
      constructor(id, type, name, subject, scopes, claims, revoked, revocationReason, expired, expiration, createdBy, description, lastUsedAt, createdAt, updatedAt) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.subject = subject;
        this.scopes = scopes;
        this.claims = claims;
        this.revoked = revoked;
        this.revocationReason = revocationReason;
        this.expired = expired;
        this.expiration = expiration;
        this.createdBy = createdBy;
        this.description = description;
        this.lastUsedAt = lastUsedAt;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _APIKey(
          data.id,
          data.type,
          data.name,
          data.subject,
          data.scopes,
          data.claims,
          data.revoked,
          data.revocation_reason,
          data.expired,
          data.expiration,
          data.created_by,
          data.description,
          data.last_used_at,
          data.created_at,
          data.updated_at
        );
      }
    };
    var BlocklistIdentifier = class _BlocklistIdentifier {
      constructor(id, identifier, identifierType, createdAt, updatedAt, instanceId) {
        this.id = id;
        this.identifier = identifier;
        this.identifierType = identifierType;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.instanceId = instanceId;
      }
      static fromJSON(data) {
        return new _BlocklistIdentifier(
          data.id,
          data.identifier,
          data.identifier_type,
          data.created_at,
          data.updated_at,
          data.instance_id
        );
      }
    };
    var SessionActivity = class _SessionActivity {
      constructor(id, isMobile, ipAddress, city, country, browserVersion, browserName, deviceType) {
        this.id = id;
        this.isMobile = isMobile;
        this.ipAddress = ipAddress;
        this.city = city;
        this.country = country;
        this.browserVersion = browserVersion;
        this.browserName = browserName;
        this.deviceType = deviceType;
      }
      static fromJSON(data) {
        return new _SessionActivity(
          data.id,
          data.is_mobile,
          data.ip_address,
          data.city,
          data.country,
          data.browser_version,
          data.browser_name,
          data.device_type
        );
      }
    };
    var Session = class _Session {
      constructor(id, clientId, userId, status, lastActiveAt, expireAt, abandonAt, createdAt, updatedAt, lastActiveOrganizationId, latestActivity, actor = null) {
        this.id = id;
        this.clientId = clientId;
        this.userId = userId;
        this.status = status;
        this.lastActiveAt = lastActiveAt;
        this.expireAt = expireAt;
        this.abandonAt = abandonAt;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.lastActiveOrganizationId = lastActiveOrganizationId;
        this.latestActivity = latestActivity;
        this.actor = actor;
      }
      static fromJSON(data) {
        return new _Session(
          data.id,
          data.client_id,
          data.user_id,
          data.status,
          data.last_active_at,
          data.expire_at,
          data.abandon_at,
          data.created_at,
          data.updated_at,
          data.last_active_organization_id,
          data.latest_activity && SessionActivity.fromJSON(data.latest_activity),
          data.actor
        );
      }
    };
    var Client = class _Client {
      constructor(id, sessionIds, sessions, signInId, signUpId, lastActiveSessionId, createdAt, updatedAt) {
        this.id = id;
        this.sessionIds = sessionIds;
        this.sessions = sessions;
        this.signInId = signInId;
        this.signUpId = signUpId;
        this.lastActiveSessionId = lastActiveSessionId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _Client(
          data.id,
          data.session_ids,
          data.sessions.map((x) => Session.fromJSON(x)),
          data.sign_in_id,
          data.sign_up_id,
          data.last_active_session_id,
          data.created_at,
          data.updated_at
        );
      }
    };
    var CnameTarget = class _CnameTarget {
      constructor(host, value, required) {
        this.host = host;
        this.value = value;
        this.required = required;
      }
      static fromJSON(data) {
        return new _CnameTarget(data.host, data.value, data.required);
      }
    };
    var Cookies2 = class _Cookies {
      constructor(cookies) {
        this.cookies = cookies;
      }
      static fromJSON(data) {
        return new _Cookies(data.cookies);
      }
    };
    var DeletedObject = class _DeletedObject {
      constructor(object, id, slug, deleted) {
        this.object = object;
        this.id = id;
        this.slug = slug;
        this.deleted = deleted;
      }
      static fromJSON(data) {
        return new _DeletedObject(data.object, data.id || null, data.slug || null, data.deleted);
      }
    };
    var Domain = class _Domain {
      constructor(id, name, isSatellite, frontendApiUrl, developmentOrigin, cnameTargets, accountsPortalUrl, proxyUrl) {
        this.id = id;
        this.name = name;
        this.isSatellite = isSatellite;
        this.frontendApiUrl = frontendApiUrl;
        this.developmentOrigin = developmentOrigin;
        this.cnameTargets = cnameTargets;
        this.accountsPortalUrl = accountsPortalUrl;
        this.proxyUrl = proxyUrl;
      }
      static fromJSON(data) {
        return new _Domain(
          data.id,
          data.name,
          data.is_satellite,
          data.frontend_api_url,
          data.development_origin,
          data.cname_targets && data.cname_targets.map((x) => CnameTarget.fromJSON(x)),
          data.accounts_portal_url,
          data.proxy_url
        );
      }
    };
    var Email = class _Email {
      constructor(id, fromEmailName, emailAddressId, toEmailAddress, subject, body, bodyPlain, status, slug, data, deliveredByClerk) {
        this.id = id;
        this.fromEmailName = fromEmailName;
        this.emailAddressId = emailAddressId;
        this.toEmailAddress = toEmailAddress;
        this.subject = subject;
        this.body = body;
        this.bodyPlain = bodyPlain;
        this.status = status;
        this.slug = slug;
        this.data = data;
        this.deliveredByClerk = deliveredByClerk;
      }
      static fromJSON(data) {
        return new _Email(
          data.id,
          data.from_email_name,
          data.email_address_id,
          data.to_email_address,
          data.subject,
          data.body,
          data.body_plain,
          data.status,
          data.slug,
          data.data,
          data.delivered_by_clerk
        );
      }
    };
    var IdentificationLink = class _IdentificationLink {
      constructor(id, type) {
        this.id = id;
        this.type = type;
      }
      static fromJSON(data) {
        return new _IdentificationLink(data.id, data.type);
      }
    };
    var Verification = class _Verification {
      constructor(status, strategy, externalVerificationRedirectURL = null, attempts = null, expireAt = null, nonce = null, message = null) {
        this.status = status;
        this.strategy = strategy;
        this.externalVerificationRedirectURL = externalVerificationRedirectURL;
        this.attempts = attempts;
        this.expireAt = expireAt;
        this.nonce = nonce;
        this.message = message;
      }
      static fromJSON(data) {
        return new _Verification(
          data.status,
          data.strategy,
          data.external_verification_redirect_url ? new URL(data.external_verification_redirect_url) : null,
          data.attempts,
          data.expire_at,
          data.nonce
        );
      }
    };
    var EmailAddress = class _EmailAddress {
      constructor(id, emailAddress, verification, linkedTo) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.verification = verification;
        this.linkedTo = linkedTo;
      }
      static fromJSON(data) {
        return new _EmailAddress(
          data.id,
          data.email_address,
          data.verification && Verification.fromJSON(data.verification),
          data.linked_to.map((link) => IdentificationLink.fromJSON(link))
        );
      }
    };
    var ExternalAccount = class _ExternalAccount {
      constructor(id, provider, identificationId, externalId, approvedScopes, emailAddress, firstName, lastName, imageUrl, username, phoneNumber, publicMetadata = {}, label, verification) {
        this.id = id;
        this.provider = provider;
        this.identificationId = identificationId;
        this.externalId = externalId;
        this.approvedScopes = approvedScopes;
        this.emailAddress = emailAddress;
        this.firstName = firstName;
        this.lastName = lastName;
        this.imageUrl = imageUrl;
        this.username = username;
        this.phoneNumber = phoneNumber;
        this.publicMetadata = publicMetadata;
        this.label = label;
        this.verification = verification;
      }
      static fromJSON(data) {
        return new _ExternalAccount(
          data.id,
          data.provider,
          data.identification_id,
          data.provider_user_id,
          data.approved_scopes,
          data.email_address,
          data.first_name,
          data.last_name,
          data.image_url || "",
          data.username,
          data.phone_number,
          data.public_metadata,
          data.label,
          data.verification && Verification.fromJSON(data.verification)
        );
      }
    };
    var IdPOAuthAccessToken = class _IdPOAuthAccessToken {
      constructor(id, clientId, type, subject, scopes, revoked, revocationReason, expired, expiration, createdAt, updatedAt) {
        this.id = id;
        this.clientId = clientId;
        this.type = type;
        this.subject = subject;
        this.scopes = scopes;
        this.revoked = revoked;
        this.revocationReason = revocationReason;
        this.expired = expired;
        this.expiration = expiration;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _IdPOAuthAccessToken(
          data.id,
          data.client_id,
          data.type,
          data.subject,
          data.scopes,
          data.revoked,
          data.revocation_reason,
          data.expired,
          data.expiration,
          data.created_at,
          data.updated_at
        );
      }
    };
    var Instance = class _Instance {
      constructor(id, environmentType, allowedOrigins) {
        this.id = id;
        this.environmentType = environmentType;
        this.allowedOrigins = allowedOrigins;
      }
      static fromJSON(data) {
        return new _Instance(data.id, data.environment_type, data.allowed_origins);
      }
    };
    var InstanceRestrictions = class _InstanceRestrictions {
      constructor(allowlist, blocklist, blockEmailSubaddresses, blockDisposableEmailDomains, ignoreDotsForGmailAddresses) {
        this.allowlist = allowlist;
        this.blocklist = blocklist;
        this.blockEmailSubaddresses = blockEmailSubaddresses;
        this.blockDisposableEmailDomains = blockDisposableEmailDomains;
        this.ignoreDotsForGmailAddresses = ignoreDotsForGmailAddresses;
      }
      static fromJSON(data) {
        return new _InstanceRestrictions(
          data.allowlist,
          data.blocklist,
          data.block_email_subaddresses,
          data.block_disposable_email_domains,
          data.ignore_dots_for_gmail_addresses
        );
      }
    };
    var InstanceSettings = class _InstanceSettings {
      constructor(id, restrictedToAllowlist, fromEmailAddress, progressiveSignUp, enhancedEmailDeliverability) {
        this.id = id;
        this.restrictedToAllowlist = restrictedToAllowlist;
        this.fromEmailAddress = fromEmailAddress;
        this.progressiveSignUp = progressiveSignUp;
        this.enhancedEmailDeliverability = enhancedEmailDeliverability;
      }
      static fromJSON(data) {
        return new _InstanceSettings(
          data.id,
          data.restricted_to_allowlist,
          data.from_email_address,
          data.progressive_sign_up,
          data.enhanced_email_deliverability
        );
      }
    };
    var Invitation = class _Invitation {
      constructor(id, emailAddress, publicMetadata, createdAt, updatedAt, status, url, revoked) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.publicMetadata = publicMetadata;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.status = status;
        this.url = url;
        this.revoked = revoked;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _Invitation(
          data.id,
          data.email_address,
          data.public_metadata,
          data.created_at,
          data.updated_at,
          data.status,
          data.url,
          data.revoked
        );
        res._raw = data;
        return res;
      }
    };
    var ObjectType = {
      AccountlessApplication: "accountless_application",
      ActorToken: "actor_token",
      AllowlistIdentifier: "allowlist_identifier",
      ApiKey: "api_key",
      BlocklistIdentifier: "blocklist_identifier",
      Client: "client",
      Cookies: "cookies",
      Domain: "domain",
      Email: "email",
      EmailAddress: "email_address",
      ExternalAccount: "external_account",
      FacebookAccount: "facebook_account",
      GoogleAccount: "google_account",
      Instance: "instance",
      InstanceRestrictions: "instance_restrictions",
      InstanceSettings: "instance_settings",
      Invitation: "invitation",
      MachineToken: "machine_to_machine_token",
      JwtTemplate: "jwt_template",
      OauthAccessToken: "oauth_access_token",
      IdpOAuthAccessToken: "clerk_idp_oauth_access_token",
      OAuthApplication: "oauth_application",
      Organization: "organization",
      OrganizationDomain: "organization_domain",
      OrganizationInvitation: "organization_invitation",
      OrganizationMembership: "organization_membership",
      OrganizationSettings: "organization_settings",
      PhoneNumber: "phone_number",
      ProxyCheck: "proxy_check",
      RedirectUrl: "redirect_url",
      SamlAccount: "saml_account",
      SamlConnection: "saml_connection",
      Session: "session",
      SignInAttempt: "sign_in_attempt",
      SignInToken: "sign_in_token",
      SignUpAttempt: "sign_up_attempt",
      SmsMessage: "sms_message",
      User: "user",
      WaitlistEntry: "waitlist_entry",
      Web3Wallet: "web3_wallet",
      Token: "token",
      TotalCount: "total_count",
      TestingToken: "testing_token",
      Role: "role",
      Permission: "permission"
    };
    var MachineToken = class _MachineToken {
      constructor(id, name, subject, scopes, claims, revoked, revocationReason, expired, expiration, createdBy, creationReason, createdAt, updatedAt) {
        this.id = id;
        this.name = name;
        this.subject = subject;
        this.scopes = scopes;
        this.claims = claims;
        this.revoked = revoked;
        this.revocationReason = revocationReason;
        this.expired = expired;
        this.expiration = expiration;
        this.createdBy = createdBy;
        this.creationReason = creationReason;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _MachineToken(
          data.id,
          data.name,
          data.subject,
          data.scopes,
          data.claims,
          data.revoked,
          data.revocation_reason,
          data.expired,
          data.expiration,
          data.created_by,
          data.creation_reason,
          data.created_at,
          data.updated_at
        );
      }
    };
    var JwtTemplate = class _JwtTemplate {
      constructor(id, name, claims, lifetime, allowedClockSkew, customSigningKey, signingAlgorithm, createdAt, updatedAt) {
        this.id = id;
        this.name = name;
        this.claims = claims;
        this.lifetime = lifetime;
        this.allowedClockSkew = allowedClockSkew;
        this.customSigningKey = customSigningKey;
        this.signingAlgorithm = signingAlgorithm;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _JwtTemplate(
          data.id,
          data.name,
          data.claims,
          data.lifetime,
          data.allowed_clock_skew,
          data.custom_signing_key,
          data.signing_algorithm,
          data.created_at,
          data.updated_at
        );
      }
    };
    var OauthAccessToken = class _OauthAccessToken {
      constructor(externalAccountId, provider, token, publicMetadata = {}, label, scopes, tokenSecret, expiresAt) {
        this.externalAccountId = externalAccountId;
        this.provider = provider;
        this.token = token;
        this.publicMetadata = publicMetadata;
        this.label = label;
        this.scopes = scopes;
        this.tokenSecret = tokenSecret;
        this.expiresAt = expiresAt;
      }
      static fromJSON(data) {
        return new _OauthAccessToken(
          data.external_account_id,
          data.provider,
          data.token,
          data.public_metadata,
          data.label || "",
          data.scopes,
          data.token_secret,
          data.expires_at
        );
      }
    };
    var OAuthApplication = class _OAuthApplication {
      constructor(id, instanceId, name, clientId, isPublic, scopes, redirectUris, authorizeUrl, tokenFetchUrl, userInfoUrl, discoveryUrl, tokenIntrospectionUrl, createdAt, updatedAt, clientSecret) {
        this.id = id;
        this.instanceId = instanceId;
        this.name = name;
        this.clientId = clientId;
        this.isPublic = isPublic;
        this.scopes = scopes;
        this.redirectUris = redirectUris;
        this.authorizeUrl = authorizeUrl;
        this.tokenFetchUrl = tokenFetchUrl;
        this.userInfoUrl = userInfoUrl;
        this.discoveryUrl = discoveryUrl;
        this.tokenIntrospectionUrl = tokenIntrospectionUrl;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.clientSecret = clientSecret;
      }
      static fromJSON(data) {
        return new _OAuthApplication(
          data.id,
          data.instance_id,
          data.name,
          data.client_id,
          data.public,
          data.scopes,
          data.redirect_uris,
          data.authorize_url,
          data.token_fetch_url,
          data.user_info_url,
          data.discovery_url,
          data.token_introspection_url,
          data.created_at,
          data.updated_at,
          data.client_secret
        );
      }
    };
    var Organization = class _Organization {
      constructor(id, name, slug, imageUrl, hasImage, createdAt, updatedAt, publicMetadata = {}, privateMetadata = {}, maxAllowedMemberships, adminDeleteEnabled, membersCount, createdBy) {
        this.id = id;
        this.name = name;
        this.slug = slug;
        this.imageUrl = imageUrl;
        this.hasImage = hasImage;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.maxAllowedMemberships = maxAllowedMemberships;
        this.adminDeleteEnabled = adminDeleteEnabled;
        this.membersCount = membersCount;
        this.createdBy = createdBy;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _Organization(
          data.id,
          data.name,
          data.slug,
          data.image_url || "",
          data.has_image,
          data.created_at,
          data.updated_at,
          data.public_metadata,
          data.private_metadata,
          data.max_allowed_memberships,
          data.admin_delete_enabled,
          data.members_count,
          data.created_by
        );
        res._raw = data;
        return res;
      }
    };
    var OrganizationInvitation = class _OrganizationInvitation {
      constructor(id, emailAddress, role, roleName, organizationId, createdAt, updatedAt, expiresAt, url, status, publicMetadata = {}, privateMetadata = {}, publicOrganizationData) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.role = role;
        this.roleName = roleName;
        this.organizationId = organizationId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.expiresAt = expiresAt;
        this.url = url;
        this.status = status;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.publicOrganizationData = publicOrganizationData;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _OrganizationInvitation(
          data.id,
          data.email_address,
          data.role,
          data.role_name,
          data.organization_id,
          data.created_at,
          data.updated_at,
          data.expires_at,
          data.url,
          data.status,
          data.public_metadata,
          data.private_metadata,
          data.public_organization_data
        );
        res._raw = data;
        return res;
      }
    };
    var OrganizationMembership = class _OrganizationMembership {
      constructor(id, role, permissions, publicMetadata = {}, privateMetadata = {}, createdAt, updatedAt, organization, publicUserData) {
        this.id = id;
        this.role = role;
        this.permissions = permissions;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.organization = organization;
        this.publicUserData = publicUserData;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _OrganizationMembership(
          data.id,
          data.role,
          data.permissions,
          data.public_metadata,
          data.private_metadata,
          data.created_at,
          data.updated_at,
          Organization.fromJSON(data.organization),
          OrganizationMembershipPublicUserData.fromJSON(data.public_user_data)
        );
        res._raw = data;
        return res;
      }
    };
    var OrganizationMembershipPublicUserData = class _OrganizationMembershipPublicUserData {
      constructor(identifier, firstName, lastName, imageUrl, hasImage, userId) {
        this.identifier = identifier;
        this.firstName = firstName;
        this.lastName = lastName;
        this.imageUrl = imageUrl;
        this.hasImage = hasImage;
        this.userId = userId;
      }
      static fromJSON(data) {
        return new _OrganizationMembershipPublicUserData(
          data.identifier,
          data.first_name,
          data.last_name,
          data.image_url,
          data.has_image,
          data.user_id
        );
      }
    };
    var OrganizationSettings = class _OrganizationSettings {
      constructor(enabled, maxAllowedMemberships, maxAllowedRoles, maxAllowedPermissions, creatorRole, adminDeleteEnabled, domainsEnabled, domainsEnrollmentModes, domainsDefaultRole) {
        this.enabled = enabled;
        this.maxAllowedMemberships = maxAllowedMemberships;
        this.maxAllowedRoles = maxAllowedRoles;
        this.maxAllowedPermissions = maxAllowedPermissions;
        this.creatorRole = creatorRole;
        this.adminDeleteEnabled = adminDeleteEnabled;
        this.domainsEnabled = domainsEnabled;
        this.domainsEnrollmentModes = domainsEnrollmentModes;
        this.domainsDefaultRole = domainsDefaultRole;
      }
      static fromJSON(data) {
        return new _OrganizationSettings(
          data.enabled,
          data.max_allowed_memberships,
          data.max_allowed_roles,
          data.max_allowed_permissions,
          data.creator_role,
          data.admin_delete_enabled,
          data.domains_enabled,
          data.domains_enrollment_modes,
          data.domains_default_role
        );
      }
    };
    var PhoneNumber = class _PhoneNumber {
      constructor(id, phoneNumber, reservedForSecondFactor, defaultSecondFactor, verification, linkedTo) {
        this.id = id;
        this.phoneNumber = phoneNumber;
        this.reservedForSecondFactor = reservedForSecondFactor;
        this.defaultSecondFactor = defaultSecondFactor;
        this.verification = verification;
        this.linkedTo = linkedTo;
      }
      static fromJSON(data) {
        return new _PhoneNumber(
          data.id,
          data.phone_number,
          data.reserved_for_second_factor,
          data.default_second_factor,
          data.verification && Verification.fromJSON(data.verification),
          data.linked_to.map((link) => IdentificationLink.fromJSON(link))
        );
      }
    };
    var ProxyCheck = class _ProxyCheck {
      constructor(id, domainId, lastRunAt, proxyUrl, successful, createdAt, updatedAt) {
        this.id = id;
        this.domainId = domainId;
        this.lastRunAt = lastRunAt;
        this.proxyUrl = proxyUrl;
        this.successful = successful;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _ProxyCheck(
          data.id,
          data.domain_id,
          data.last_run_at,
          data.proxy_url,
          data.successful,
          data.created_at,
          data.updated_at
        );
      }
    };
    var RedirectUrl = class _RedirectUrl {
      constructor(id, url, createdAt, updatedAt) {
        this.id = id;
        this.url = url;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _RedirectUrl(data.id, data.url, data.created_at, data.updated_at);
      }
    };
    var SamlConnection = class _SamlConnection {
      constructor(id, name, domain, organizationId, idpEntityId, idpSsoUrl, idpCertificate, idpMetadataUrl, idpMetadata, acsUrl, spEntityId, spMetadataUrl, active, provider, userCount, syncUserAttributes, allowSubdomains, allowIdpInitiated, createdAt, updatedAt, attributeMapping) {
        this.id = id;
        this.name = name;
        this.domain = domain;
        this.organizationId = organizationId;
        this.idpEntityId = idpEntityId;
        this.idpSsoUrl = idpSsoUrl;
        this.idpCertificate = idpCertificate;
        this.idpMetadataUrl = idpMetadataUrl;
        this.idpMetadata = idpMetadata;
        this.acsUrl = acsUrl;
        this.spEntityId = spEntityId;
        this.spMetadataUrl = spMetadataUrl;
        this.active = active;
        this.provider = provider;
        this.userCount = userCount;
        this.syncUserAttributes = syncUserAttributes;
        this.allowSubdomains = allowSubdomains;
        this.allowIdpInitiated = allowIdpInitiated;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.attributeMapping = attributeMapping;
      }
      static fromJSON(data) {
        return new _SamlConnection(
          data.id,
          data.name,
          data.domain,
          data.organization_id,
          data.idp_entity_id,
          data.idp_sso_url,
          data.idp_certificate,
          data.idp_metadata_url,
          data.idp_metadata,
          data.acs_url,
          data.sp_entity_id,
          data.sp_metadata_url,
          data.active,
          data.provider,
          data.user_count,
          data.sync_user_attributes,
          data.allow_subdomains,
          data.allow_idp_initiated,
          data.created_at,
          data.updated_at,
          data.attribute_mapping && AttributeMapping.fromJSON(data.attribute_mapping)
        );
      }
    };
    var SamlAccountConnection = class _SamlAccountConnection {
      constructor(id, name, domain, active, provider, syncUserAttributes, allowSubdomains, allowIdpInitiated, createdAt, updatedAt) {
        this.id = id;
        this.name = name;
        this.domain = domain;
        this.active = active;
        this.provider = provider;
        this.syncUserAttributes = syncUserAttributes;
        this.allowSubdomains = allowSubdomains;
        this.allowIdpInitiated = allowIdpInitiated;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _SamlAccountConnection(
          data.id,
          data.name,
          data.domain,
          data.active,
          data.provider,
          data.sync_user_attributes,
          data.allow_subdomains,
          data.allow_idp_initiated,
          data.created_at,
          data.updated_at
        );
      }
    };
    var AttributeMapping = class _AttributeMapping {
      constructor(userId, emailAddress, firstName, lastName) {
        this.userId = userId;
        this.emailAddress = emailAddress;
        this.firstName = firstName;
        this.lastName = lastName;
      }
      static fromJSON(data) {
        return new _AttributeMapping(data.user_id, data.email_address, data.first_name, data.last_name);
      }
    };
    var SamlAccount = class _SamlAccount {
      constructor(id, provider, providerUserId, active, emailAddress, firstName, lastName, verification, samlConnection) {
        this.id = id;
        this.provider = provider;
        this.providerUserId = providerUserId;
        this.active = active;
        this.emailAddress = emailAddress;
        this.firstName = firstName;
        this.lastName = lastName;
        this.verification = verification;
        this.samlConnection = samlConnection;
      }
      static fromJSON(data) {
        return new _SamlAccount(
          data.id,
          data.provider,
          data.provider_user_id,
          data.active,
          data.email_address,
          data.first_name,
          data.last_name,
          data.verification && Verification.fromJSON(data.verification),
          data.saml_connection && SamlAccountConnection.fromJSON(data.saml_connection)
        );
      }
    };
    var SignInToken = class _SignInToken {
      constructor(id, userId, token, status, url, createdAt, updatedAt) {
        this.id = id;
        this.userId = userId;
        this.token = token;
        this.status = status;
        this.url = url;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _SignInToken(data.id, data.user_id, data.token, data.status, data.url, data.created_at, data.updated_at);
      }
    };
    var SignUpAttemptVerification = class _SignUpAttemptVerification {
      constructor(nextAction, supportedStrategies) {
        this.nextAction = nextAction;
        this.supportedStrategies = supportedStrategies;
      }
      static fromJSON(data) {
        return new _SignUpAttemptVerification(data.next_action, data.supported_strategies);
      }
    };
    var SignUpAttemptVerifications = class _SignUpAttemptVerifications {
      constructor(emailAddress, phoneNumber, web3Wallet, externalAccount) {
        this.emailAddress = emailAddress;
        this.phoneNumber = phoneNumber;
        this.web3Wallet = web3Wallet;
        this.externalAccount = externalAccount;
      }
      static fromJSON(data) {
        return new _SignUpAttemptVerifications(
          data.email_address && SignUpAttemptVerification.fromJSON(data.email_address),
          data.phone_number && SignUpAttemptVerification.fromJSON(data.phone_number),
          data.web3_wallet && SignUpAttemptVerification.fromJSON(data.web3_wallet),
          data.external_account
        );
      }
    };
    var SignUpAttempt = class _SignUpAttempt {
      constructor(id, status, requiredFields, optionalFields, missingFields, unverifiedFields, verifications, username, emailAddress, phoneNumber, web3Wallet, passwordEnabled, firstName, lastName, customAction, externalId, createdSessionId, createdUserId, abandonAt, legalAcceptedAt, publicMetadata, unsafeMetadata) {
        this.id = id;
        this.status = status;
        this.requiredFields = requiredFields;
        this.optionalFields = optionalFields;
        this.missingFields = missingFields;
        this.unverifiedFields = unverifiedFields;
        this.verifications = verifications;
        this.username = username;
        this.emailAddress = emailAddress;
        this.phoneNumber = phoneNumber;
        this.web3Wallet = web3Wallet;
        this.passwordEnabled = passwordEnabled;
        this.firstName = firstName;
        this.lastName = lastName;
        this.customAction = customAction;
        this.externalId = externalId;
        this.createdSessionId = createdSessionId;
        this.createdUserId = createdUserId;
        this.abandonAt = abandonAt;
        this.legalAcceptedAt = legalAcceptedAt;
        this.publicMetadata = publicMetadata;
        this.unsafeMetadata = unsafeMetadata;
      }
      static fromJSON(data) {
        return new _SignUpAttempt(
          data.id,
          data.status,
          data.required_fields,
          data.optional_fields,
          data.missing_fields,
          data.unverified_fields,
          data.verifications ? SignUpAttemptVerifications.fromJSON(data.verifications) : null,
          data.username,
          data.email_address,
          data.phone_number,
          data.web3_wallet,
          data.password_enabled,
          data.first_name,
          data.last_name,
          data.custom_action,
          data.external_id,
          data.created_session_id,
          data.created_user_id,
          data.abandon_at,
          data.legal_accepted_at,
          data.public_metadata,
          data.unsafe_metadata
        );
      }
    };
    var SMSMessage = class _SMSMessage {
      constructor(id, fromPhoneNumber, toPhoneNumber, message, status, phoneNumberId, data) {
        this.id = id;
        this.fromPhoneNumber = fromPhoneNumber;
        this.toPhoneNumber = toPhoneNumber;
        this.message = message;
        this.status = status;
        this.phoneNumberId = phoneNumberId;
        this.data = data;
      }
      static fromJSON(data) {
        return new _SMSMessage(
          data.id,
          data.from_phone_number,
          data.to_phone_number,
          data.message,
          data.status,
          data.phone_number_id,
          data.data
        );
      }
    };
    var Token = class _Token {
      constructor(jwt) {
        this.jwt = jwt;
      }
      static fromJSON(data) {
        return new _Token(data.jwt);
      }
    };
    var Web3Wallet = class _Web3Wallet {
      constructor(id, web3Wallet, verification) {
        this.id = id;
        this.web3Wallet = web3Wallet;
        this.verification = verification;
      }
      static fromJSON(data) {
        return new _Web3Wallet(data.id, data.web3_wallet, data.verification && Verification.fromJSON(data.verification));
      }
    };
    var User = class _User {
      constructor(id, passwordEnabled, totpEnabled, backupCodeEnabled, twoFactorEnabled, banned, locked, createdAt, updatedAt, imageUrl, hasImage, primaryEmailAddressId, primaryPhoneNumberId, primaryWeb3WalletId, lastSignInAt, externalId, username, firstName, lastName, publicMetadata = {}, privateMetadata = {}, unsafeMetadata = {}, emailAddresses = [], phoneNumbers = [], web3Wallets = [], externalAccounts = [], samlAccounts = [], lastActiveAt, createOrganizationEnabled, createOrganizationsLimit = null, deleteSelfEnabled, legalAcceptedAt) {
        this.id = id;
        this.passwordEnabled = passwordEnabled;
        this.totpEnabled = totpEnabled;
        this.backupCodeEnabled = backupCodeEnabled;
        this.twoFactorEnabled = twoFactorEnabled;
        this.banned = banned;
        this.locked = locked;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.imageUrl = imageUrl;
        this.hasImage = hasImage;
        this.primaryEmailAddressId = primaryEmailAddressId;
        this.primaryPhoneNumberId = primaryPhoneNumberId;
        this.primaryWeb3WalletId = primaryWeb3WalletId;
        this.lastSignInAt = lastSignInAt;
        this.externalId = externalId;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.unsafeMetadata = unsafeMetadata;
        this.emailAddresses = emailAddresses;
        this.phoneNumbers = phoneNumbers;
        this.web3Wallets = web3Wallets;
        this.externalAccounts = externalAccounts;
        this.samlAccounts = samlAccounts;
        this.lastActiveAt = lastActiveAt;
        this.createOrganizationEnabled = createOrganizationEnabled;
        this.createOrganizationsLimit = createOrganizationsLimit;
        this.deleteSelfEnabled = deleteSelfEnabled;
        this.legalAcceptedAt = legalAcceptedAt;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _User(
          data.id,
          data.password_enabled,
          data.totp_enabled,
          data.backup_code_enabled,
          data.two_factor_enabled,
          data.banned,
          data.locked,
          data.created_at,
          data.updated_at,
          data.image_url,
          data.has_image,
          data.primary_email_address_id,
          data.primary_phone_number_id,
          data.primary_web3_wallet_id,
          data.last_sign_in_at,
          data.external_id,
          data.username,
          data.first_name,
          data.last_name,
          data.public_metadata,
          data.private_metadata,
          data.unsafe_metadata,
          (data.email_addresses || []).map((x) => EmailAddress.fromJSON(x)),
          (data.phone_numbers || []).map((x) => PhoneNumber.fromJSON(x)),
          (data.web3_wallets || []).map((x) => Web3Wallet.fromJSON(x)),
          (data.external_accounts || []).map((x) => ExternalAccount.fromJSON(x)),
          (data.saml_accounts || []).map((x) => SamlAccount.fromJSON(x)),
          data.last_active_at,
          data.create_organization_enabled,
          data.create_organizations_limit,
          data.delete_self_enabled,
          data.legal_accepted_at
        );
        res._raw = data;
        return res;
      }
      /**
       * The primary email address of the user.
       */
      get primaryEmailAddress() {
        return this.emailAddresses.find(({ id }) => id === this.primaryEmailAddressId) ?? null;
      }
      /**
       * The primary phone number of the user.
       */
      get primaryPhoneNumber() {
        return this.phoneNumbers.find(({ id }) => id === this.primaryPhoneNumberId) ?? null;
      }
      /**
       * The primary web3 wallet of the user.
       */
      get primaryWeb3Wallet() {
        return this.web3Wallets.find(({ id }) => id === this.primaryWeb3WalletId) ?? null;
      }
      /**
       * The full name of the user.
       */
      get fullName() {
        return [this.firstName, this.lastName].join(" ").trim() || null;
      }
    };
    var WaitlistEntry = class _WaitlistEntry {
      constructor(id, emailAddress, status, invitation, createdAt, updatedAt, isLocked) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.status = status;
        this.invitation = invitation;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isLocked = isLocked;
      }
      static fromJSON(data) {
        return new _WaitlistEntry(
          data.id,
          data.email_address,
          data.status,
          data.invitation && Invitation.fromJSON(data.invitation),
          data.created_at,
          data.updated_at,
          data.is_locked
        );
      }
    };
    function deserialize(payload) {
      let data, totalCount;
      if (Array.isArray(payload)) {
        const data2 = payload.map((item) => jsonToObject(item));
        return { data: data2 };
      } else if (isPaginated(payload)) {
        data = payload.data.map((item) => jsonToObject(item));
        totalCount = payload.total_count;
        return { data, totalCount };
      } else {
        return { data: jsonToObject(payload) };
      }
    }
    function isPaginated(payload) {
      if (!payload || typeof payload !== "object" || !("data" in payload)) {
        return false;
      }
      return Array.isArray(payload.data) && payload.data !== void 0;
    }
    function getCount(item) {
      return item.total_count;
    }
    function jsonToObject(item) {
      if (typeof item !== "string" && "object" in item && "deleted" in item) {
        return DeletedObject.fromJSON(item);
      }
      switch (item.object) {
        case ObjectType.AccountlessApplication:
          return AccountlessApplication.fromJSON(item);
        case ObjectType.ActorToken:
          return ActorToken.fromJSON(item);
        case ObjectType.AllowlistIdentifier:
          return AllowlistIdentifier.fromJSON(item);
        case ObjectType.ApiKey:
          return APIKey.fromJSON(item);
        case ObjectType.BlocklistIdentifier:
          return BlocklistIdentifier.fromJSON(item);
        case ObjectType.Client:
          return Client.fromJSON(item);
        case ObjectType.Cookies:
          return Cookies2.fromJSON(item);
        case ObjectType.Domain:
          return Domain.fromJSON(item);
        case ObjectType.EmailAddress:
          return EmailAddress.fromJSON(item);
        case ObjectType.Email:
          return Email.fromJSON(item);
        case ObjectType.IdpOAuthAccessToken:
          return IdPOAuthAccessToken.fromJSON(item);
        case ObjectType.Instance:
          return Instance.fromJSON(item);
        case ObjectType.InstanceRestrictions:
          return InstanceRestrictions.fromJSON(item);
        case ObjectType.InstanceSettings:
          return InstanceSettings.fromJSON(item);
        case ObjectType.Invitation:
          return Invitation.fromJSON(item);
        case ObjectType.JwtTemplate:
          return JwtTemplate.fromJSON(item);
        case ObjectType.MachineToken:
          return MachineToken.fromJSON(item);
        case ObjectType.OauthAccessToken:
          return OauthAccessToken.fromJSON(item);
        case ObjectType.OAuthApplication:
          return OAuthApplication.fromJSON(item);
        case ObjectType.Organization:
          return Organization.fromJSON(item);
        case ObjectType.OrganizationInvitation:
          return OrganizationInvitation.fromJSON(item);
        case ObjectType.OrganizationMembership:
          return OrganizationMembership.fromJSON(item);
        case ObjectType.OrganizationSettings:
          return OrganizationSettings.fromJSON(item);
        case ObjectType.PhoneNumber:
          return PhoneNumber.fromJSON(item);
        case ObjectType.ProxyCheck:
          return ProxyCheck.fromJSON(item);
        case ObjectType.RedirectUrl:
          return RedirectUrl.fromJSON(item);
        case ObjectType.SamlConnection:
          return SamlConnection.fromJSON(item);
        case ObjectType.SignInToken:
          return SignInToken.fromJSON(item);
        case ObjectType.SignUpAttempt:
          return SignUpAttempt.fromJSON(item);
        case ObjectType.Session:
          return Session.fromJSON(item);
        case ObjectType.SmsMessage:
          return SMSMessage.fromJSON(item);
        case ObjectType.Token:
          return Token.fromJSON(item);
        case ObjectType.TotalCount:
          return getCount(item);
        case ObjectType.User:
          return User.fromJSON(item);
        case ObjectType.WaitlistEntry:
          return WaitlistEntry.fromJSON(item);
        default:
          return item;
      }
    }
    function buildRequest(options) {
      const requestFn = async (requestOptions) => {
        var _a;
        const {
          secretKey,
          requireSecretKey = true,
          apiUrl = API_URL,
          apiVersion = API_VERSION,
          userAgent = USER_AGENT
        } = options;
        const { path, method, queryParams, headerParams, bodyParams, formData } = requestOptions;
        if (requireSecretKey) {
          assertValidSecretKey(secretKey);
        }
        const url = joinPaths(apiUrl, apiVersion, path);
        const finalUrl = new URL(url);
        if (queryParams) {
          const snakecasedQueryParams = (0, import_snakecase_keys.default)({ ...queryParams });
          for (const [key, val] of Object.entries(snakecasedQueryParams)) {
            if (val) {
              [val].flat().forEach((v) => finalUrl.searchParams.append(key, v));
            }
          }
        }
        const headers = {
          "Clerk-API-Version": SUPPORTED_BAPI_VERSION,
          "User-Agent": userAgent,
          ...headerParams
        };
        if (secretKey) {
          headers.Authorization = `Bearer ${secretKey}`;
        }
        let res;
        try {
          if (formData) {
            res = await runtime.fetch(finalUrl.href, {
              method,
              headers,
              body: formData
            });
          } else {
            headers["Content-Type"] = "application/json";
            const buildBody = () => {
              const hasBody = method !== "GET" && bodyParams && Object.keys(bodyParams).length > 0;
              if (!hasBody) {
                return null;
              }
              const formatKeys = (object) => (0, import_snakecase_keys.default)(object, { deep: false });
              return {
                body: JSON.stringify(Array.isArray(bodyParams) ? bodyParams.map(formatKeys) : formatKeys(bodyParams))
              };
            };
            res = await runtime.fetch(finalUrl.href, {
              method,
              headers,
              ...buildBody()
            });
          }
          const isJSONResponse = (res == null ? void 0 : res.headers) && ((_a = res.headers) == null ? void 0 : _a.get(constants.Headers.ContentType)) === constants.ContentTypes.Json;
          const responseBody = await (isJSONResponse ? res.json() : res.text());
          if (!res.ok) {
            return {
              data: null,
              errors: parseErrors(responseBody),
              status: res == null ? void 0 : res.status,
              statusText: res == null ? void 0 : res.statusText,
              clerkTraceId: getTraceId(responseBody, res == null ? void 0 : res.headers),
              retryAfter: getRetryAfter(res == null ? void 0 : res.headers)
            };
          }
          return {
            ...deserialize(responseBody),
            errors: null
          };
        } catch (err) {
          if (err instanceof Error) {
            return {
              data: null,
              errors: [
                {
                  code: "unexpected_error",
                  message: err.message || "Unexpected error"
                }
              ],
              clerkTraceId: getTraceId(err, res == null ? void 0 : res.headers)
            };
          }
          return {
            data: null,
            errors: parseErrors(err),
            status: res == null ? void 0 : res.status,
            statusText: res == null ? void 0 : res.statusText,
            clerkTraceId: getTraceId(err, res == null ? void 0 : res.headers),
            retryAfter: getRetryAfter(res == null ? void 0 : res.headers)
          };
        }
      };
      return withLegacyRequestReturn(requestFn);
    }
    function getTraceId(data, headers) {
      if (data && typeof data === "object" && "clerk_trace_id" in data && typeof data.clerk_trace_id === "string") {
        return data.clerk_trace_id;
      }
      const cfRay = headers == null ? void 0 : headers.get("cf-ray");
      return cfRay || "";
    }
    function getRetryAfter(headers) {
      const retryAfter = headers == null ? void 0 : headers.get("Retry-After");
      if (!retryAfter)
        return;
      const value = parseInt(retryAfter, 10);
      if (isNaN(value))
        return;
      return value;
    }
    function parseErrors(data) {
      if (!!data && typeof data === "object" && "errors" in data) {
        const errors = data.errors;
        return errors.length > 0 ? errors.map(import_error2.parseError) : [];
      }
      return [];
    }
    function withLegacyRequestReturn(cb) {
      return async (...args) => {
        const { data, errors, totalCount, status, statusText, clerkTraceId, retryAfter } = await cb(...args);
        if (errors) {
          const error = new import_error2.ClerkAPIResponseError(statusText || "", {
            data: [],
            status,
            clerkTraceId,
            retryAfter
          });
          error.errors = errors;
          throw error;
        }
        if (typeof totalCount !== "undefined") {
          return { data, totalCount };
        }
        return data;
      };
    }
    function createBackendApiClient(options) {
      const request = buildRequest(options);
      return {
        __experimental_accountlessApplications: new AccountlessApplicationAPI(
          buildRequest({ ...options, requireSecretKey: false })
        ),
        actorTokens: new ActorTokenAPI(request),
        allowlistIdentifiers: new AllowlistIdentifierAPI(request),
        betaFeatures: new BetaFeaturesAPI(request),
        blocklistIdentifiers: new BlocklistIdentifierAPI(request),
        clients: new ClientAPI(request),
        domains: new DomainAPI(request),
        emailAddresses: new EmailAddressAPI(request),
        instance: new InstanceAPI(request),
        invitations: new InvitationAPI(request),
        // Using "/" instead of an actual version since they're bapi-proxy endpoints.
        // bapi-proxy connects directly to C1 without URL versioning,
        // while API versioning is handled through the Clerk-API-Version header.
        machineTokens: new MachineTokensApi(
          buildRequest({
            ...options,
            apiVersion: "/"
          })
        ),
        idPOAuthAccessToken: new IdPOAuthAccessTokenApi(
          buildRequest({
            ...options,
            apiVersion: "/"
          })
        ),
        apiKeys: new APIKeysAPI(
          buildRequest({
            ...options,
            apiVersion: "/"
          })
        ),
        jwks: new JwksAPI(request),
        jwtTemplates: new JwtTemplatesApi(request),
        oauthApplications: new OAuthApplicationsApi(request),
        organizations: new OrganizationAPI(request),
        phoneNumbers: new PhoneNumberAPI(request),
        proxyChecks: new ProxyCheckAPI(request),
        redirectUrls: new RedirectUrlAPI(request),
        samlConnections: new SamlConnectionAPI(request),
        sessions: new SessionAPI(request),
        signInTokens: new SignInTokenAPI(request),
        signUps: new SignUpAPI(request),
        testingTokens: new TestingTokenAPI(request),
        users: new UserAPI(request),
        waitlistEntries: new WaitlistEntryAPI(request),
        webhooks: new WebhookAPI(request)
      };
    }
    var TokenType = {
      SessionToken: "session_token",
      ApiKey: "api_key",
      MachineToken: "machine_token",
      OAuthToken: "oauth_token"
    };
    var M2M_TOKEN_PREFIX = "mt_";
    var OAUTH_TOKEN_PREFIX = "oat_";
    var API_KEY_PREFIX = "ak_";
    var MACHINE_TOKEN_PREFIXES = [M2M_TOKEN_PREFIX, OAUTH_TOKEN_PREFIX, API_KEY_PREFIX];
    function isMachineTokenByPrefix(token) {
      return MACHINE_TOKEN_PREFIXES.some((prefix) => token.startsWith(prefix));
    }
    function getMachineTokenType(token) {
      if (token.startsWith(M2M_TOKEN_PREFIX)) {
        return TokenType.MachineToken;
      }
      if (token.startsWith(OAUTH_TOKEN_PREFIX)) {
        return TokenType.OAuthToken;
      }
      if (token.startsWith(API_KEY_PREFIX)) {
        return TokenType.ApiKey;
      }
      throw new Error("Unknown machine token type");
    }
    var isTokenTypeAccepted = (tokenType, acceptsToken) => {
      if (!tokenType) {
        return false;
      }
      if (acceptsToken === "any") {
        return true;
      }
      const tokenTypes = Array.isArray(acceptsToken) ? acceptsToken : [acceptsToken];
      return tokenTypes.includes(tokenType);
    };
    function isMachineTokenType(type) {
      return type === TokenType.ApiKey || type === TokenType.MachineToken || type === TokenType.OAuthToken;
    }
    var createDebug = (data) => {
      return () => {
        const res = { ...data };
        res.secretKey = (res.secretKey || "").substring(0, 7);
        res.jwtKey = (res.jwtKey || "").substring(0, 7);
        return { ...res };
      };
    };
    function signedInAuthObject(authenticateContext, sessionToken, sessionClaims) {
      const { actor, sessionId, sessionStatus, userId, orgId, orgRole, orgSlug, orgPermissions, factorVerificationAge } = (0, import_jwtPayloadParser.__experimental_JWTPayloadToAuthObjectProperties)(sessionClaims);
      const apiClient = createBackendApiClient(authenticateContext);
      const getToken = createGetToken({
        sessionId,
        sessionToken,
        fetcher: async (sessionId2, template, expiresInSeconds) => (await apiClient.sessions.getToken(sessionId2, template || "", expiresInSeconds)).jwt
      });
      return {
        tokenType: TokenType.SessionToken,
        actor,
        sessionClaims,
        sessionId,
        sessionStatus,
        userId,
        orgId,
        orgRole,
        orgSlug,
        orgPermissions,
        factorVerificationAge,
        getToken,
        has: (0, import_authorization.createCheckAuthorization)({
          orgId,
          orgRole,
          orgPermissions,
          userId,
          factorVerificationAge,
          features: sessionClaims.fea || "",
          plans: sessionClaims.pla || ""
        }),
        debug: createDebug({ ...authenticateContext, sessionToken }),
        isAuthenticated: true
      };
    }
    function signedOutAuthObject(debugData, initialSessionStatus) {
      return {
        tokenType: TokenType.SessionToken,
        sessionClaims: null,
        sessionId: null,
        sessionStatus: initialSessionStatus ?? null,
        userId: null,
        actor: null,
        orgId: null,
        orgRole: null,
        orgSlug: null,
        orgPermissions: null,
        factorVerificationAge: null,
        getToken: () => Promise.resolve(null),
        has: () => false,
        debug: createDebug(debugData),
        isAuthenticated: false
      };
    }
    function authenticatedMachineObject(tokenType, token, verificationResult, debugData) {
      const baseObject = {
        id: verificationResult.id,
        subject: verificationResult.subject,
        getToken: () => Promise.resolve(token),
        has: () => false,
        debug: createDebug(debugData),
        isAuthenticated: true
      };
      switch (tokenType) {
        case TokenType.ApiKey: {
          const result = verificationResult;
          return {
            ...baseObject,
            tokenType,
            name: result.name,
            claims: result.claims,
            scopes: result.scopes,
            userId: result.subject.startsWith("user_") ? result.subject : null,
            orgId: result.subject.startsWith("org_") ? result.subject : null
          };
        }
        case TokenType.MachineToken: {
          const result = verificationResult;
          return {
            ...baseObject,
            tokenType,
            name: result.name,
            claims: result.claims,
            scopes: result.scopes,
            machineId: result.subject
          };
        }
        case TokenType.OAuthToken: {
          const result = verificationResult;
          return {
            ...baseObject,
            tokenType,
            scopes: result.scopes,
            userId: result.subject,
            clientId: result.clientId
          };
        }
        default:
          throw new Error(`Invalid token type: ${tokenType}`);
      }
    }
    function unauthenticatedMachineObject(tokenType, debugData) {
      const baseObject = {
        id: null,
        subject: null,
        scopes: null,
        has: () => false,
        getToken: () => Promise.resolve(null),
        debug: createDebug(debugData),
        isAuthenticated: false
      };
      switch (tokenType) {
        case TokenType.ApiKey: {
          return {
            ...baseObject,
            tokenType,
            name: null,
            claims: null,
            scopes: null,
            userId: null,
            orgId: null
          };
        }
        case TokenType.MachineToken: {
          return {
            ...baseObject,
            tokenType,
            name: null,
            claims: null,
            scopes: null,
            machineId: null
          };
        }
        case TokenType.OAuthToken: {
          return {
            ...baseObject,
            tokenType,
            scopes: null,
            userId: null,
            clientId: null
          };
        }
        default:
          throw new Error(`Invalid token type: ${tokenType}`);
      }
    }
    function invalidTokenAuthObject() {
      return {
        isAuthenticated: false,
        tokenType: null,
        getToken: () => Promise.resolve(null),
        has: () => false,
        debug: () => ({})
      };
    }
    var makeAuthObjectSerializable = (obj) => {
      const { debug, getToken, has, ...rest } = obj;
      return rest;
    };
    var createGetToken = (params) => {
      const { fetcher, sessionToken, sessionId } = params || {};
      return async (options = {}) => {
        if (!sessionId) {
          return null;
        }
        if (options.template || options.expiresInSeconds !== void 0) {
          return fetcher(sessionId, options.template, options.expiresInSeconds);
        }
        return sessionToken;
      };
    };
    var getAuthObjectFromJwt = (jwt, { treatPendingAsSignedOut = true, ...options }) => {
      const authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);
      if (treatPendingAsSignedOut && authObject.sessionStatus === "pending") {
        return signedOutAuthObject(options, authObject.sessionStatus);
      }
      return authObject;
    };
    var getAuthObjectForAcceptedToken = ({
      authObject,
      acceptsToken = TokenType.SessionToken
    }) => {
      if (acceptsToken === "any") {
        return authObject;
      }
      if (Array.isArray(acceptsToken)) {
        if (!isTokenTypeAccepted(authObject.tokenType, acceptsToken)) {
          return invalidTokenAuthObject();
        }
        return authObject;
      }
      if (!isTokenTypeAccepted(authObject.tokenType, acceptsToken)) {
        if (isMachineTokenType(acceptsToken)) {
          return unauthenticatedMachineObject(acceptsToken, authObject.debug);
        }
        return signedOutAuthObject(authObject.debug);
      }
      return authObject;
    };
    var AuthStatus = {
      SignedIn: "signed-in",
      SignedOut: "signed-out",
      Handshake: "handshake"
    };
    var AuthErrorReason = {
      ClientUATWithoutSessionToken: "client-uat-but-no-session-token",
      DevBrowserMissing: "dev-browser-missing",
      DevBrowserSync: "dev-browser-sync",
      PrimaryRespondsToSyncing: "primary-responds-to-syncing",
      SatelliteCookieNeedsSyncing: "satellite-needs-syncing",
      SessionTokenAndUATMissing: "session-token-and-uat-missing",
      SessionTokenMissing: "session-token-missing",
      SessionTokenExpired: "session-token-expired",
      SessionTokenIATBeforeClientUAT: "session-token-iat-before-client-uat",
      SessionTokenNBF: "session-token-nbf",
      SessionTokenIatInTheFuture: "session-token-iat-in-the-future",
      SessionTokenWithoutClientUAT: "session-token-but-no-client-uat",
      ActiveOrganizationMismatch: "active-organization-mismatch",
      TokenTypeMismatch: "token-type-mismatch",
      UnexpectedError: "unexpected-error"
    };
    function signedIn(params) {
      const { authenticateContext, headers = new Headers(), token } = params;
      const toAuth = ({ treatPendingAsSignedOut = true } = {}) => {
        if (params.tokenType === TokenType.SessionToken) {
          const { sessionClaims } = params;
          const authObject = signedInAuthObject(authenticateContext, token, sessionClaims);
          if (treatPendingAsSignedOut && authObject.sessionStatus === "pending") {
            return signedOutAuthObject(void 0, authObject.sessionStatus);
          }
          return authObject;
        }
        const { machineData } = params;
        return authenticatedMachineObject(params.tokenType, token, machineData, authenticateContext);
      };
      return {
        status: AuthStatus.SignedIn,
        reason: null,
        message: null,
        proxyUrl: authenticateContext.proxyUrl || "",
        publishableKey: authenticateContext.publishableKey || "",
        isSatellite: authenticateContext.isSatellite || false,
        domain: authenticateContext.domain || "",
        signInUrl: authenticateContext.signInUrl || "",
        signUpUrl: authenticateContext.signUpUrl || "",
        afterSignInUrl: authenticateContext.afterSignInUrl || "",
        afterSignUpUrl: authenticateContext.afterSignUpUrl || "",
        isSignedIn: true,
        isAuthenticated: true,
        tokenType: params.tokenType,
        toAuth,
        headers,
        token
      };
    }
    function signedOut(params) {
      const { authenticateContext, headers = new Headers(), reason, message = "", tokenType } = params;
      const toAuth = () => {
        if (tokenType === TokenType.SessionToken) {
          return signedOutAuthObject({ ...authenticateContext, status: AuthStatus.SignedOut, reason, message });
        }
        return unauthenticatedMachineObject(tokenType, { reason, message, headers });
      };
      return withDebugHeaders({
        status: AuthStatus.SignedOut,
        reason,
        message,
        proxyUrl: authenticateContext.proxyUrl || "",
        publishableKey: authenticateContext.publishableKey || "",
        isSatellite: authenticateContext.isSatellite || false,
        domain: authenticateContext.domain || "",
        signInUrl: authenticateContext.signInUrl || "",
        signUpUrl: authenticateContext.signUpUrl || "",
        afterSignInUrl: authenticateContext.afterSignInUrl || "",
        afterSignUpUrl: authenticateContext.afterSignUpUrl || "",
        isSignedIn: false,
        isAuthenticated: false,
        tokenType,
        toAuth,
        headers,
        token: null
      });
    }
    function handshake(authenticateContext, reason, message = "", headers) {
      return withDebugHeaders({
        status: AuthStatus.Handshake,
        reason,
        message,
        publishableKey: authenticateContext.publishableKey || "",
        isSatellite: authenticateContext.isSatellite || false,
        domain: authenticateContext.domain || "",
        proxyUrl: authenticateContext.proxyUrl || "",
        signInUrl: authenticateContext.signInUrl || "",
        signUpUrl: authenticateContext.signUpUrl || "",
        afterSignInUrl: authenticateContext.afterSignInUrl || "",
        afterSignUpUrl: authenticateContext.afterSignUpUrl || "",
        isSignedIn: false,
        isAuthenticated: false,
        tokenType: TokenType.SessionToken,
        toAuth: () => null,
        headers,
        token: null
      });
    }
    function signedOutInvalidToken() {
      const authObject = invalidTokenAuthObject();
      return withDebugHeaders({
        status: AuthStatus.SignedOut,
        reason: AuthErrorReason.TokenTypeMismatch,
        message: "",
        proxyUrl: "",
        publishableKey: "",
        isSatellite: false,
        domain: "",
        signInUrl: "",
        signUpUrl: "",
        afterSignInUrl: "",
        afterSignUpUrl: "",
        isSignedIn: false,
        isAuthenticated: false,
        tokenType: null,
        toAuth: () => authObject,
        headers: new Headers(),
        token: null
      });
    }
    var withDebugHeaders = (requestState) => {
      const headers = new Headers(requestState.headers || {});
      if (requestState.message) {
        try {
          headers.set(constants.Headers.AuthMessage, requestState.message);
        } catch {
        }
      }
      if (requestState.reason) {
        try {
          headers.set(constants.Headers.AuthReason, requestState.reason);
        } catch {
        }
      }
      if (requestState.status) {
        try {
          headers.set(constants.Headers.AuthStatus, requestState.status);
        } catch {
        }
      }
      requestState.headers = headers;
      return requestState;
    };
    var import_cookie = require_dist();
    var ClerkUrl = class extends URL {
      isCrossOrigin(other) {
        return this.origin !== new URL(other.toString()).origin;
      }
    };
    var createClerkUrl = (...args) => {
      return new ClerkUrl(...args);
    };
    var ClerkRequest = class extends Request {
      constructor(input, init) {
        const url = typeof input !== "string" && "url" in input ? input.url : String(input);
        super(url, init || typeof input === "string" ? void 0 : input);
        this.clerkUrl = this.deriveUrlFromHeaders(this);
        this.cookies = this.parseCookies(this);
      }
      toJSON() {
        return {
          url: this.clerkUrl.href,
          method: this.method,
          headers: JSON.stringify(Object.fromEntries(this.headers)),
          clerkUrl: this.clerkUrl.toString(),
          cookies: JSON.stringify(Object.fromEntries(this.cookies))
        };
      }
      /**
       * Used to fix request.url using the x-forwarded-* headers
       * TODO add detailed description of the issues this solves
       */
      deriveUrlFromHeaders(req) {
        const initialUrl = new URL(req.url);
        const forwardedProto = req.headers.get(constants.Headers.ForwardedProto);
        const forwardedHost = req.headers.get(constants.Headers.ForwardedHost);
        const host = req.headers.get(constants.Headers.Host);
        const protocol = initialUrl.protocol;
        const resolvedHost = this.getFirstValueFromHeader(forwardedHost) ?? host;
        const resolvedProtocol = this.getFirstValueFromHeader(forwardedProto) ?? (protocol == null ? void 0 : protocol.replace(/[:/]/, ""));
        const origin = resolvedHost && resolvedProtocol ? `${resolvedProtocol}://${resolvedHost}` : initialUrl.origin;
        if (origin === initialUrl.origin) {
          return createClerkUrl(initialUrl);
        }
        return createClerkUrl(initialUrl.pathname + initialUrl.search, origin);
      }
      getFirstValueFromHeader(value) {
        return value == null ? void 0 : value.split(",")[0];
      }
      parseCookies(req) {
        const cookiesRecord = (0, import_cookie.parse)(this.decodeCookieValue(req.headers.get("cookie") || ""));
        return new Map(Object.entries(cookiesRecord));
      }
      decodeCookieValue(str) {
        return str ? str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent) : str;
      }
    };
    var createClerkRequest = (...args) => {
      return args[0] instanceof ClerkRequest ? args[0] : new ClerkRequest(...args);
    };
    var getCookieName = (cookieDirective) => {
      var _a;
      return (_a = cookieDirective.split(";")[0]) == null ? void 0 : _a.split("=")[0];
    };
    var getCookieValue = (cookieDirective) => {
      var _a;
      return (_a = cookieDirective.split(";")[0]) == null ? void 0 : _a.split("=")[1];
    };
    var cache = {};
    var lastUpdatedAt = 0;
    function getFromCache(kid) {
      return cache[kid];
    }
    function getCacheValues() {
      return Object.values(cache);
    }
    function setInCache(jwk, shouldExpire = true) {
      cache[jwk.kid] = jwk;
      lastUpdatedAt = shouldExpire ? Date.now() : -1;
    }
    var LocalJwkKid = "local";
    var PEM_HEADER = "-----BEGIN PUBLIC KEY-----";
    var PEM_TRAILER = "-----END PUBLIC KEY-----";
    var RSA_PREFIX = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA";
    var RSA_SUFFIX = "IDAQAB";
    function loadClerkJWKFromLocal(localKey) {
      if (!getFromCache(LocalJwkKid)) {
        if (!localKey) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.SetClerkJWTKey,
            message: "Missing local JWK.",
            reason: TokenVerificationErrorReason.LocalJWKMissing
          });
        }
        const modulus = localKey.replace(/\r\n|\n|\r/g, "").replace(PEM_HEADER, "").replace(PEM_TRAILER, "").replace(RSA_PREFIX, "").replace(RSA_SUFFIX, "").replace(/\+/g, "-").replace(/\//g, "_");
        setInCache(
          {
            kid: "local",
            kty: "RSA",
            alg: "RS256",
            n: modulus,
            e: "AQAB"
          },
          false
          // local key never expires in cache
        );
      }
      return getFromCache(LocalJwkKid);
    }
    async function loadClerkJWKFromRemote({
      secretKey,
      apiUrl = API_URL,
      apiVersion = API_VERSION,
      kid,
      skipJwksCache
    }) {
      if (skipJwksCache || cacheHasExpired() || !getFromCache(kid)) {
        if (!secretKey) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.ContactSupport,
            message: "Failed to load JWKS from Clerk Backend or Frontend API.",
            reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
          });
        }
        const fetcher = () => fetchJWKSFromBAPI(apiUrl, secretKey, apiVersion);
        const { keys } = await (0, import_retry.retry)(fetcher);
        if (!keys || !keys.length) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.ContactSupport,
            message: "The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",
            reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
          });
        }
        keys.forEach((key) => setInCache(key));
      }
      const jwk = getFromCache(kid);
      if (!jwk) {
        const cacheValues = getCacheValues();
        const jwkKeys = cacheValues.map((jwk2) => jwk2.kid).sort().join(", ");
        throw new TokenVerificationError({
          action: `Go to your Dashboard and validate your secret and public keys are correct. ${TokenVerificationErrorAction.ContactSupport} if the issue persists.`,
          message: `Unable to find a signing key in JWKS that matches the kid='${kid}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${jwkKeys}`,
          reason: TokenVerificationErrorReason.JWKKidMismatch
        });
      }
      return jwk;
    }
    async function fetchJWKSFromBAPI(apiUrl, key, apiVersion) {
      if (!key) {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.SetClerkSecretKey,
          message: "Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",
          reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
        });
      }
      const url = new URL(apiUrl);
      url.pathname = joinPaths(url.pathname, apiVersion, "/jwks");
      const response = await runtime.fetch(url.href, {
        headers: {
          Authorization: `Bearer ${key}`,
          "Clerk-API-Version": SUPPORTED_BAPI_VERSION,
          "Content-Type": "application/json",
          "User-Agent": USER_AGENT
        }
      });
      if (!response.ok) {
        const json = await response.json();
        const invalidSecretKeyError = getErrorObjectByCode(json == null ? void 0 : json.errors, TokenVerificationErrorCode.InvalidSecretKey);
        if (invalidSecretKeyError) {
          const reason = TokenVerificationErrorReason.InvalidSecretKey;
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.ContactSupport,
            message: invalidSecretKeyError.message,
            reason
          });
        }
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.ContactSupport,
          message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,
          reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
        });
      }
      return response.json();
    }
    function cacheHasExpired() {
      if (lastUpdatedAt === -1) {
        return false;
      }
      const isExpired = Date.now() - lastUpdatedAt >= MAX_CACHE_LAST_UPDATED_AT_SECONDS * 1e3;
      if (isExpired) {
        cache = {};
      }
      return isExpired;
    }
    var getErrorObjectByCode = (errors, code) => {
      if (!errors) {
        return null;
      }
      return errors.find((err) => err.code === code);
    };
    var import_error3 = require_error();
    async function verifyToken(token, options) {
      const { data: decodedResult, errors } = decodeJwt(token);
      if (errors) {
        return { errors };
      }
      const { header } = decodedResult;
      const { kid } = header;
      try {
        let key;
        if (options.jwtKey) {
          key = loadClerkJWKFromLocal(options.jwtKey);
        } else if (options.secretKey) {
          key = await loadClerkJWKFromRemote({ ...options, kid });
        } else {
          return {
            errors: [
              new TokenVerificationError({
                action: TokenVerificationErrorAction.SetClerkJWTKey,
                message: "Failed to resolve JWK during verification.",
                reason: TokenVerificationErrorReason.JWKFailedToResolve
              })
            ]
          };
        }
        return await verifyJwt(token, { ...options, key });
      } catch (error) {
        return { errors: [error] };
      }
    }
    function handleClerkAPIError(tokenType, err, notFoundMessage) {
      var _a;
      if ((0, import_error3.isClerkAPIResponseError)(err)) {
        let code;
        let message;
        switch (err.status) {
          case 401:
            code = MachineTokenVerificationErrorCode.InvalidSecretKey;
            message = ((_a = err.errors[0]) == null ? void 0 : _a.message) || "Invalid secret key";
            break;
          case 404:
            code = MachineTokenVerificationErrorCode.TokenInvalid;
            message = notFoundMessage;
            break;
          default:
            code = MachineTokenVerificationErrorCode.UnexpectedError;
            message = "Unexpected error";
        }
        return {
          data: void 0,
          tokenType,
          errors: [
            new MachineTokenVerificationError({
              message,
              code,
              status: err.status
            })
          ]
        };
      }
      return {
        data: void 0,
        tokenType,
        errors: [
          new MachineTokenVerificationError({
            message: "Unexpected error",
            code: MachineTokenVerificationErrorCode.UnexpectedError,
            status: err.status
          })
        ]
      };
    }
    async function verifyMachineToken(secret, options) {
      try {
        const client = createBackendApiClient(options);
        const verifiedToken = await client.machineTokens.verifySecret(secret);
        return { data: verifiedToken, tokenType: TokenType.MachineToken, errors: void 0 };
      } catch (err) {
        return handleClerkAPIError(TokenType.MachineToken, err, "Machine token not found");
      }
    }
    async function verifyOAuthToken(accessToken, options) {
      try {
        const client = createBackendApiClient(options);
        const verifiedToken = await client.idPOAuthAccessToken.verifyAccessToken(accessToken);
        return { data: verifiedToken, tokenType: TokenType.OAuthToken, errors: void 0 };
      } catch (err) {
        return handleClerkAPIError(TokenType.OAuthToken, err, "OAuth token not found");
      }
    }
    async function verifyAPIKey(secret, options) {
      try {
        const client = createBackendApiClient(options);
        const verifiedToken = await client.apiKeys.verifySecret(secret);
        return { data: verifiedToken, tokenType: TokenType.ApiKey, errors: void 0 };
      } catch (err) {
        return handleClerkAPIError(TokenType.ApiKey, err, "API key not found");
      }
    }
    async function verifyMachineAuthToken(token, options) {
      if (token.startsWith(M2M_TOKEN_PREFIX)) {
        return verifyMachineToken(token, options);
      }
      if (token.startsWith(OAUTH_TOKEN_PREFIX)) {
        return verifyOAuthToken(token, options);
      }
      if (token.startsWith(API_KEY_PREFIX)) {
        return verifyAPIKey(token, options);
      }
      throw new Error("Unknown machine token type");
    }
    async function verifyHandshakeJwt(token, { key }) {
      const { data: decoded, errors } = decodeJwt(token);
      if (errors) {
        throw errors[0];
      }
      const { header, payload } = decoded;
      const { typ, alg } = header;
      assertHeaderType(typ);
      assertHeaderAlgorithm(alg);
      const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);
      if (signatureErrors) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Error verifying handshake token. ${signatureErrors[0]}`
        });
      }
      if (!signatureValid) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenInvalidSignature,
          message: "Handshake signature is invalid."
        });
      }
      return payload;
    }
    async function verifyHandshakeToken(token, options) {
      const { secretKey, apiUrl, apiVersion, jwksCacheTtlInMs, jwtKey, skipJwksCache } = options;
      const { data, errors } = decodeJwt(token);
      if (errors) {
        throw errors[0];
      }
      const { kid } = data.header;
      let key;
      if (jwtKey) {
        key = loadClerkJWKFromLocal(jwtKey);
      } else if (secretKey) {
        key = await loadClerkJWKFromRemote({ secretKey, apiUrl, apiVersion, kid, jwksCacheTtlInMs, skipJwksCache });
      } else {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.SetClerkJWTKey,
          message: "Failed to resolve JWK during handshake verification.",
          reason: TokenVerificationErrorReason.JWKFailedToResolve
        });
      }
      return await verifyHandshakeJwt(token, {
        key
      });
    }
    var HandshakeService = class {
      constructor(authenticateContext, options, organizationMatcher) {
        this.authenticateContext = authenticateContext;
        this.options = options;
        this.organizationMatcher = organizationMatcher;
      }
      /**
       * Determines if a request is eligible for handshake based on its headers
       *
       * Currently, a request is only eligible for a handshake if we can say it's *probably* a request for a document, not a fetch or some other exotic request.
       * This heuristic should give us a reliable enough signal for browsers that support `Sec-Fetch-Dest` and for those that don't.
       *
       * @returns boolean indicating if the request is eligible for handshake
       */
      isRequestEligibleForHandshake() {
        const { accept, secFetchDest } = this.authenticateContext;
        if (secFetchDest === "document" || secFetchDest === "iframe") {
          return true;
        }
        if (!secFetchDest && (accept == null ? void 0 : accept.startsWith("text/html"))) {
          return true;
        }
        return false;
      }
      /**
       * Builds the redirect headers for a handshake request
       * @param reason - The reason for the handshake (e.g. 'session-token-expired')
       * @returns Headers object containing the Location header for redirect
       * @throws Error if clerkUrl is missing in authenticateContext
       */
      buildRedirectToHandshake(reason) {
        var _a;
        if (!((_a = this.authenticateContext) == null ? void 0 : _a.clerkUrl)) {
          throw new Error("Missing clerkUrl in authenticateContext");
        }
        const redirectUrl = this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl);
        let baseUrl = this.authenticateContext.frontendApi.startsWith("http") ? this.authenticateContext.frontendApi : `https://${this.authenticateContext.frontendApi}`;
        baseUrl = baseUrl.replace(/\/+$/, "") + "/";
        const url = new URL("v1/client/handshake", baseUrl);
        url.searchParams.append("redirect_url", (redirectUrl == null ? void 0 : redirectUrl.href) || "");
        url.searchParams.append("__clerk_api_version", SUPPORTED_BAPI_VERSION);
        url.searchParams.append(
          constants.QueryParameters.SuffixedCookies,
          this.authenticateContext.usesSuffixedCookies().toString()
        );
        url.searchParams.append(constants.QueryParameters.HandshakeReason, reason);
        url.searchParams.append(constants.QueryParameters.HandshakeFormat, "nonce");
        if (this.authenticateContext.instanceType === "development" && this.authenticateContext.devBrowserToken) {
          url.searchParams.append(constants.QueryParameters.DevBrowser, this.authenticateContext.devBrowserToken);
        }
        const toActivate = this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl, this.organizationMatcher);
        if (toActivate) {
          const params = this.getOrganizationSyncQueryParams(toActivate);
          params.forEach((value, key) => {
            url.searchParams.append(key, value);
          });
        }
        return new Headers({ [constants.Headers.Location]: url.href });
      }
      /**
       * Gets cookies from either a handshake nonce or a handshake token
       * @returns Promise resolving to string array of cookie directives
       */
      async getCookiesFromHandshake() {
        var _a;
        const cookiesToSet = [];
        if (this.authenticateContext.handshakeNonce) {
          try {
            const handshakePayload = await ((_a = this.authenticateContext.apiClient) == null ? void 0 : _a.clients.getHandshakePayload({
              nonce: this.authenticateContext.handshakeNonce
            }));
            if (handshakePayload) {
              cookiesToSet.push(...handshakePayload.directives);
            }
          } catch (error) {
            console.error("Clerk: HandshakeService: error getting handshake payload:", error);
          }
        } else if (this.authenticateContext.handshakeToken) {
          const handshakePayload = await verifyHandshakeToken(
            this.authenticateContext.handshakeToken,
            this.authenticateContext
          );
          if (handshakePayload && Array.isArray(handshakePayload.handshake)) {
            cookiesToSet.push(...handshakePayload.handshake);
          }
        }
        return cookiesToSet;
      }
      /**
       * Resolves a handshake request by verifying the handshake token and setting appropriate cookies
       * @returns Promise resolving to either a SignedInState or SignedOutState
       * @throws Error if handshake verification fails or if there are issues with the session token
       */
      async resolveHandshake() {
        const headers = new Headers({
          "Access-Control-Allow-Origin": "null",
          "Access-Control-Allow-Credentials": "true"
        });
        const cookiesToSet = await this.getCookiesFromHandshake();
        let sessionToken = "";
        cookiesToSet.forEach((x) => {
          headers.append("Set-Cookie", x);
          if (getCookieName(x).startsWith(constants.Cookies.Session)) {
            sessionToken = getCookieValue(x);
          }
        });
        if (this.authenticateContext.instanceType === "development") {
          const newUrl = new URL(this.authenticateContext.clerkUrl);
          newUrl.searchParams.delete(constants.QueryParameters.Handshake);
          newUrl.searchParams.delete(constants.QueryParameters.HandshakeHelp);
          headers.append(constants.Headers.Location, newUrl.toString());
          headers.set(constants.Headers.CacheControl, "no-store");
        }
        if (sessionToken === "") {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext: this.authenticateContext,
            reason: AuthErrorReason.SessionTokenMissing,
            message: "",
            headers
          });
        }
        const { data, errors: [error] = [] } = await verifyToken(sessionToken, this.authenticateContext);
        if (data) {
          return signedIn({
            tokenType: TokenType.SessionToken,
            authenticateContext: this.authenticateContext,
            sessionClaims: data,
            headers,
            token: sessionToken
          });
        }
        if (this.authenticateContext.instanceType === "development" && ((error == null ? void 0 : error.reason) === TokenVerificationErrorReason.TokenExpired || (error == null ? void 0 : error.reason) === TokenVerificationErrorReason.TokenNotActiveYet || (error == null ? void 0 : error.reason) === TokenVerificationErrorReason.TokenIatInTheFuture)) {
          const developmentError = new TokenVerificationError({
            action: error.action,
            message: error.message,
            reason: error.reason
          });
          developmentError.tokenCarrier = "cookie";
          console.error(
            `Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${developmentError.getFullMessage()}`
          );
          const { data: retryResult, errors: [retryError] = [] } = await verifyToken(sessionToken, {
            ...this.authenticateContext,
            clockSkewInMs: 864e5
          });
          if (retryResult) {
            return signedIn({
              tokenType: TokenType.SessionToken,
              authenticateContext: this.authenticateContext,
              sessionClaims: retryResult,
              headers,
              token: sessionToken
            });
          }
          throw new Error((retryError == null ? void 0 : retryError.message) || "Clerk: Handshake retry failed.");
        }
        throw new Error((error == null ? void 0 : error.message) || "Clerk: Handshake failed.");
      }
      /**
       * Handles handshake token verification errors in development mode
       * @param error - The TokenVerificationError that occurred
       * @throws Error with a descriptive message about the verification failure
       */
      handleTokenVerificationErrorInDevelopment(error) {
        if (error.reason === TokenVerificationErrorReason.TokenInvalidSignature) {
          const msg = `Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.`;
          throw new Error(msg);
        }
        throw new Error(`Clerk: Handshake token verification failed: ${error.getFullMessage()}.`);
      }
      /**
       * Checks if a redirect loop is detected and sets headers to track redirect count
       * @param headers - The Headers object to modify
       * @returns boolean indicating if a redirect loop was detected (true) or if the request can proceed (false)
       */
      checkAndTrackRedirectLoop(headers) {
        if (this.authenticateContext.handshakeRedirectLoopCounter === 3) {
          return true;
        }
        const newCounterValue = this.authenticateContext.handshakeRedirectLoopCounter + 1;
        const cookieName = constants.Cookies.RedirectCount;
        headers.append("Set-Cookie", `${cookieName}=${newCounterValue}; SameSite=Lax; HttpOnly; Max-Age=3`);
        return false;
      }
      removeDevBrowserFromURL(url) {
        const updatedURL = new URL(url);
        updatedURL.searchParams.delete(constants.QueryParameters.DevBrowser);
        updatedURL.searchParams.delete(constants.QueryParameters.LegacyDevBrowser);
        return updatedURL;
      }
      getOrganizationSyncTarget(url, matchers) {
        return matchers.findTarget(url);
      }
      getOrganizationSyncQueryParams(toActivate) {
        const ret = /* @__PURE__ */ new Map();
        if (toActivate.type === "personalAccount") {
          ret.set("organization_id", "");
        }
        if (toActivate.type === "organization") {
          if (toActivate.organizationId) {
            ret.set("organization_id", toActivate.organizationId);
          }
          if (toActivate.organizationSlug) {
            ret.set("organization_id", toActivate.organizationSlug);
          }
        }
        return ret;
      }
    };
    var import_pathToRegexp = require_pathToRegexp();
    var OrganizationMatcher = class {
      constructor(options) {
        this.organizationPattern = this.createMatcher(options == null ? void 0 : options.organizationPatterns);
        this.personalAccountPattern = this.createMatcher(options == null ? void 0 : options.personalAccountPatterns);
      }
      createMatcher(pattern) {
        if (!pattern)
          return null;
        try {
          return (0, import_pathToRegexp.match)(pattern);
        } catch (e) {
          throw new Error(`Invalid pattern "${pattern}": ${e}`);
        }
      }
      findTarget(url) {
        const orgTarget = this.findOrganizationTarget(url);
        if (orgTarget)
          return orgTarget;
        return this.findPersonalAccountTarget(url);
      }
      findOrganizationTarget(url) {
        if (!this.organizationPattern)
          return null;
        try {
          const result = this.organizationPattern(url.pathname);
          if (!result || !("params" in result))
            return null;
          const params = result.params;
          if (params.id)
            return { type: "organization", organizationId: params.id };
          if (params.slug)
            return { type: "organization", organizationSlug: params.slug };
          return null;
        } catch (e) {
          console.error("Failed to match organization pattern:", e);
          return null;
        }
      }
      findPersonalAccountTarget(url) {
        if (!this.personalAccountPattern)
          return null;
        try {
          const result = this.personalAccountPattern(url.pathname);
          return result ? { type: "personalAccount" } : null;
        } catch (e) {
          console.error("Failed to match personal account pattern:", e);
          return null;
        }
      }
    };
    var RefreshTokenErrorReason = {
      NonEligibleNoCookie: "non-eligible-no-refresh-cookie",
      NonEligibleNonGet: "non-eligible-non-get",
      InvalidSessionToken: "invalid-session-token",
      MissingApiClient: "missing-api-client",
      MissingSessionToken: "missing-session-token",
      MissingRefreshToken: "missing-refresh-token",
      ExpiredSessionTokenDecodeFailed: "expired-session-token-decode-failed",
      ExpiredSessionTokenMissingSidClaim: "expired-session-token-missing-sid-claim",
      FetchError: "fetch-error",
      UnexpectedSDKError: "unexpected-sdk-error",
      UnexpectedBAPIError: "unexpected-bapi-error"
    };
    function assertSignInUrlExists(signInUrl, key) {
      if (!signInUrl && (0, import_keys.isDevelopmentFromSecretKey)(key)) {
        throw new Error(`Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite`);
      }
    }
    function assertProxyUrlOrDomain(proxyUrlOrDomain) {
      if (!proxyUrlOrDomain) {
        throw new Error(`Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`);
      }
    }
    function assertSignInUrlFormatAndOrigin(_signInUrl, origin) {
      let signInUrl;
      try {
        signInUrl = new URL(_signInUrl);
      } catch {
        throw new Error(`The signInUrl needs to have a absolute url format.`);
      }
      if (signInUrl.origin === origin) {
        throw new Error(`The signInUrl needs to be on a different origin than your satellite application.`);
      }
    }
    function isRequestEligibleForRefresh(err, authenticateContext, request) {
      return err.reason === TokenVerificationErrorReason.TokenExpired && !!authenticateContext.refreshTokenInCookie && request.method === "GET";
    }
    function checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext) {
      const mismatch = !isTokenTypeAccepted(parsedTokenType, acceptsToken);
      if (mismatch) {
        return signedOut({
          tokenType: parsedTokenType,
          authenticateContext,
          reason: AuthErrorReason.TokenTypeMismatch
        });
      }
      return null;
    }
    function isTokenTypeInAcceptedArray(acceptsToken, authenticateContext) {
      let parsedTokenType = null;
      const { tokenInHeader } = authenticateContext;
      if (tokenInHeader) {
        if (isMachineTokenByPrefix(tokenInHeader)) {
          parsedTokenType = getMachineTokenType(tokenInHeader);
        } else {
          parsedTokenType = TokenType.SessionToken;
        }
      }
      const typeToCheck = parsedTokenType ?? TokenType.SessionToken;
      return isTokenTypeAccepted(typeToCheck, acceptsToken);
    }
    var authenticateRequest = async (request, options) => {
      const authenticateContext = await createAuthenticateContext(createClerkRequest(request), options);
      assertValidSecretKey(authenticateContext.secretKey);
      const acceptsToken = options.acceptsToken ?? TokenType.SessionToken;
      if (authenticateContext.isSatellite) {
        assertSignInUrlExists(authenticateContext.signInUrl, authenticateContext.secretKey);
        if (authenticateContext.signInUrl && authenticateContext.origin) {
          assertSignInUrlFormatAndOrigin(authenticateContext.signInUrl, authenticateContext.origin);
        }
        assertProxyUrlOrDomain(authenticateContext.proxyUrl || authenticateContext.domain);
      }
      const organizationMatcher = new OrganizationMatcher(options.organizationSyncOptions);
      const handshakeService = new HandshakeService(
        authenticateContext,
        { organizationSyncOptions: options.organizationSyncOptions },
        organizationMatcher
      );
      async function refreshToken(authenticateContext2) {
        var _a, _b;
        if (!options.apiClient) {
          return {
            data: null,
            error: {
              message: "An apiClient is needed to perform token refresh.",
              cause: { reason: RefreshTokenErrorReason.MissingApiClient }
            }
          };
        }
        const { sessionToken: expiredSessionToken, refreshTokenInCookie: refreshToken2 } = authenticateContext2;
        if (!expiredSessionToken) {
          return {
            data: null,
            error: {
              message: "Session token must be provided.",
              cause: { reason: RefreshTokenErrorReason.MissingSessionToken }
            }
          };
        }
        if (!refreshToken2) {
          return {
            data: null,
            error: {
              message: "Refresh token must be provided.",
              cause: { reason: RefreshTokenErrorReason.MissingRefreshToken }
            }
          };
        }
        const { data: decodeResult, errors: decodedErrors } = decodeJwt(expiredSessionToken);
        if (!decodeResult || decodedErrors) {
          return {
            data: null,
            error: {
              message: "Unable to decode the expired session token.",
              cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenDecodeFailed, errors: decodedErrors }
            }
          };
        }
        if (!((_a = decodeResult == null ? void 0 : decodeResult.payload) == null ? void 0 : _a.sid)) {
          return {
            data: null,
            error: {
              message: "Expired session token is missing the `sid` claim.",
              cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenMissingSidClaim }
            }
          };
        }
        try {
          const response = await options.apiClient.sessions.refreshSession(decodeResult.payload.sid, {
            format: "cookie",
            suffixed_cookies: authenticateContext2.usesSuffixedCookies(),
            expired_token: expiredSessionToken || "",
            refresh_token: refreshToken2 || "",
            request_origin: authenticateContext2.clerkUrl.origin,
            // The refresh endpoint expects headers as Record<string, string[]>, so we need to transform it.
            request_headers: Object.fromEntries(Array.from(request.headers.entries()).map(([k, v]) => [k, [v]]))
          });
          return { data: response.cookies, error: null };
        } catch (err) {
          if ((_b = err == null ? void 0 : err.errors) == null ? void 0 : _b.length) {
            if (err.errors[0].code === "unexpected_error") {
              return {
                data: null,
                error: {
                  message: `Fetch unexpected error`,
                  cause: { reason: RefreshTokenErrorReason.FetchError, errors: err.errors }
                }
              };
            }
            return {
              data: null,
              error: {
                message: err.errors[0].code,
                cause: { reason: err.errors[0].code, errors: err.errors }
              }
            };
          } else {
            return {
              data: null,
              error: {
                message: `Unexpected Server/BAPI error`,
                cause: { reason: RefreshTokenErrorReason.UnexpectedBAPIError, errors: [err] }
              }
            };
          }
        }
      }
      async function attemptRefresh(authenticateContext2) {
        const { data: cookiesToSet, error } = await refreshToken(authenticateContext2);
        if (!cookiesToSet || cookiesToSet.length === 0) {
          return { data: null, error };
        }
        const headers = new Headers();
        let sessionToken = "";
        cookiesToSet.forEach((x) => {
          headers.append("Set-Cookie", x);
          if (getCookieName(x).startsWith(constants.Cookies.Session)) {
            sessionToken = getCookieValue(x);
          }
        });
        const { data: jwtPayload, errors } = await verifyToken(sessionToken, authenticateContext2);
        if (errors) {
          return {
            data: null,
            error: {
              message: `Clerk: unable to verify refreshed session token.`,
              cause: { reason: RefreshTokenErrorReason.InvalidSessionToken, errors }
            }
          };
        }
        return { data: { jwtPayload, sessionToken, headers }, error: null };
      }
      function handleMaybeHandshakeStatus(authenticateContext2, reason, message, headers) {
        if (!handshakeService.isRequestEligibleForHandshake()) {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext: authenticateContext2,
            reason,
            message
          });
        }
        const handshakeHeaders = headers ?? handshakeService.buildRedirectToHandshake(reason);
        if (handshakeHeaders.get(constants.Headers.Location)) {
          handshakeHeaders.set(constants.Headers.CacheControl, "no-store");
        }
        const isRedirectLoop = handshakeService.checkAndTrackRedirectLoop(handshakeHeaders);
        if (isRedirectLoop) {
          const msg = `Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard.`;
          console.log(msg);
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext: authenticateContext2,
            reason,
            message
          });
        }
        return handshake(authenticateContext2, reason, message, handshakeHeaders);
      }
      function handleMaybeOrganizationSyncHandshake(authenticateContext2, auth) {
        const organizationSyncTarget = organizationMatcher.findTarget(authenticateContext2.clerkUrl);
        if (!organizationSyncTarget) {
          return null;
        }
        let mustActivate = false;
        if (organizationSyncTarget.type === "organization") {
          if (organizationSyncTarget.organizationSlug && organizationSyncTarget.organizationSlug !== auth.orgSlug) {
            mustActivate = true;
          }
          if (organizationSyncTarget.organizationId && organizationSyncTarget.organizationId !== auth.orgId) {
            mustActivate = true;
          }
        }
        if (organizationSyncTarget.type === "personalAccount" && auth.orgId) {
          mustActivate = true;
        }
        if (!mustActivate) {
          return null;
        }
        if (authenticateContext2.handshakeRedirectLoopCounter > 0) {
          console.warn(
            "Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."
          );
          return null;
        }
        const handshakeState = handleMaybeHandshakeStatus(
          authenticateContext2,
          AuthErrorReason.ActiveOrganizationMismatch,
          ""
        );
        if (handshakeState.status !== "handshake") {
          return null;
        }
        return handshakeState;
      }
      async function authenticateRequestWithTokenInHeader() {
        const { tokenInHeader } = authenticateContext;
        try {
          const { data, errors } = await verifyToken(tokenInHeader, authenticateContext);
          if (errors) {
            throw errors[0];
          }
          return signedIn({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            sessionClaims: data,
            headers: new Headers(),
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            token: tokenInHeader
          });
        } catch (err) {
          return handleSessionTokenError(err, "header");
        }
      }
      async function authenticateRequestWithTokenInCookie() {
        const hasActiveClient = authenticateContext.clientUat;
        const hasSessionToken = !!authenticateContext.sessionTokenInCookie;
        const hasDevBrowserToken = !!authenticateContext.devBrowserToken;
        if (authenticateContext.handshakeNonce || authenticateContext.handshakeToken) {
          try {
            return await handshakeService.resolveHandshake();
          } catch (error) {
            if (error instanceof TokenVerificationError && authenticateContext.instanceType === "development") {
              handshakeService.handleTokenVerificationErrorInDevelopment(error);
            } else {
              console.error("Clerk: unable to resolve handshake:", error);
            }
          }
        }
        if (authenticateContext.instanceType === "development" && authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.DevBrowser)) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserSync, "");
        }
        const isRequestEligibleForMultiDomainSync = authenticateContext.isSatellite && authenticateContext.secFetchDest === "document";
        if (authenticateContext.instanceType === "production" && isRequestEligibleForMultiDomainSync) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, "");
        }
        if (authenticateContext.instanceType === "development" && isRequestEligibleForMultiDomainSync && !authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.ClerkSynced)) {
          const redirectURL = new URL(authenticateContext.signInUrl);
          redirectURL.searchParams.append(
            constants.QueryParameters.ClerkRedirectUrl,
            authenticateContext.clerkUrl.toString()
          );
          const headers = new Headers({ [constants.Headers.Location]: redirectURL.toString() });
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, "", headers);
        }
        const redirectUrl = new URL(authenticateContext.clerkUrl).searchParams.get(
          constants.QueryParameters.ClerkRedirectUrl
        );
        if (authenticateContext.instanceType === "development" && !authenticateContext.isSatellite && redirectUrl) {
          const redirectBackToSatelliteUrl = new URL(redirectUrl);
          if (authenticateContext.devBrowserToken) {
            redirectBackToSatelliteUrl.searchParams.append(
              constants.QueryParameters.DevBrowser,
              authenticateContext.devBrowserToken
            );
          }
          redirectBackToSatelliteUrl.searchParams.append(constants.QueryParameters.ClerkSynced, "true");
          const headers = new Headers({ [constants.Headers.Location]: redirectBackToSatelliteUrl.toString() });
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.PrimaryRespondsToSyncing, "", headers);
        }
        if (authenticateContext.instanceType === "development" && !hasDevBrowserToken) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserMissing, "");
        }
        if (!hasActiveClient && !hasSessionToken) {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            reason: AuthErrorReason.SessionTokenAndUATMissing
          });
        }
        if (!hasActiveClient && hasSessionToken) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenWithoutClientUAT, "");
        }
        if (hasActiveClient && !hasSessionToken) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.ClientUATWithoutSessionToken, "");
        }
        const { data: decodeResult, errors: decodedErrors } = decodeJwt(authenticateContext.sessionTokenInCookie);
        if (decodedErrors) {
          return handleSessionTokenError(decodedErrors[0], "cookie");
        }
        if (decodeResult.payload.iat < authenticateContext.clientUat) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenIATBeforeClientUAT, "");
        }
        try {
          const { data, errors } = await verifyToken(authenticateContext.sessionTokenInCookie, authenticateContext);
          if (errors) {
            throw errors[0];
          }
          const signedInRequestState = signedIn({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            sessionClaims: data,
            headers: new Headers(),
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            token: authenticateContext.sessionTokenInCookie
          });
          const authObject = signedInRequestState.toAuth();
          if (authObject.userId) {
            const handshakeRequestState = handleMaybeOrganizationSyncHandshake(authenticateContext, authObject);
            if (handshakeRequestState) {
              return handshakeRequestState;
            }
          }
          return signedInRequestState;
        } catch (err) {
          return handleSessionTokenError(err, "cookie");
        }
        return signedOut({
          tokenType: TokenType.SessionToken,
          authenticateContext,
          reason: AuthErrorReason.UnexpectedError
        });
      }
      async function handleSessionTokenError(err, tokenCarrier) {
        var _a;
        if (!(err instanceof TokenVerificationError)) {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            reason: AuthErrorReason.UnexpectedError
          });
        }
        let refreshError;
        if (isRequestEligibleForRefresh(err, authenticateContext, request)) {
          const { data, error } = await attemptRefresh(authenticateContext);
          if (data) {
            return signedIn({
              tokenType: TokenType.SessionToken,
              authenticateContext,
              sessionClaims: data.jwtPayload,
              headers: data.headers,
              token: data.sessionToken
            });
          }
          if ((_a = error == null ? void 0 : error.cause) == null ? void 0 : _a.reason) {
            refreshError = error.cause.reason;
          } else {
            refreshError = RefreshTokenErrorReason.UnexpectedSDKError;
          }
        } else {
          if (request.method !== "GET") {
            refreshError = RefreshTokenErrorReason.NonEligibleNonGet;
          } else if (!authenticateContext.refreshTokenInCookie) {
            refreshError = RefreshTokenErrorReason.NonEligibleNoCookie;
          } else {
            refreshError = null;
          }
        }
        err.tokenCarrier = tokenCarrier;
        const reasonToHandshake = [
          TokenVerificationErrorReason.TokenExpired,
          TokenVerificationErrorReason.TokenNotActiveYet,
          TokenVerificationErrorReason.TokenIatInTheFuture
        ].includes(err.reason);
        if (reasonToHandshake) {
          return handleMaybeHandshakeStatus(
            authenticateContext,
            convertTokenVerificationErrorReasonToAuthErrorReason({ tokenError: err.reason, refreshError }),
            err.getFullMessage()
          );
        }
        return signedOut({
          tokenType: TokenType.SessionToken,
          authenticateContext,
          reason: err.reason,
          message: err.getFullMessage()
        });
      }
      function handleMachineError(tokenType, err) {
        if (!(err instanceof MachineTokenVerificationError)) {
          return signedOut({
            tokenType,
            authenticateContext,
            reason: AuthErrorReason.UnexpectedError
          });
        }
        return signedOut({
          tokenType,
          authenticateContext,
          reason: err.code,
          message: err.getFullMessage()
        });
      }
      async function authenticateMachineRequestWithTokenInHeader() {
        const { tokenInHeader } = authenticateContext;
        if (!tokenInHeader) {
          return handleSessionTokenError(new Error("Missing token in header"), "header");
        }
        if (!isMachineTokenByPrefix(tokenInHeader)) {
          return signedOut({
            tokenType: acceptsToken,
            authenticateContext,
            reason: AuthErrorReason.TokenTypeMismatch,
            message: ""
          });
        }
        const parsedTokenType = getMachineTokenType(tokenInHeader);
        const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);
        if (mismatchState) {
          return mismatchState;
        }
        const { data, tokenType, errors } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);
        if (errors) {
          return handleMachineError(tokenType, errors[0]);
        }
        return signedIn({
          tokenType,
          authenticateContext,
          machineData: data,
          token: tokenInHeader
        });
      }
      async function authenticateAnyRequestWithTokenInHeader() {
        const { tokenInHeader } = authenticateContext;
        if (!tokenInHeader) {
          return handleSessionTokenError(new Error("Missing token in header"), "header");
        }
        if (isMachineTokenByPrefix(tokenInHeader)) {
          const parsedTokenType = getMachineTokenType(tokenInHeader);
          const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);
          if (mismatchState) {
            return mismatchState;
          }
          const { data: data2, tokenType, errors: errors2 } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);
          if (errors2) {
            return handleMachineError(tokenType, errors2[0]);
          }
          return signedIn({
            tokenType,
            authenticateContext,
            machineData: data2,
            token: tokenInHeader
          });
        }
        const { data, errors } = await verifyToken(tokenInHeader, authenticateContext);
        if (errors) {
          return handleSessionTokenError(errors[0], "header");
        }
        return signedIn({
          tokenType: TokenType.SessionToken,
          authenticateContext,
          sessionClaims: data,
          token: tokenInHeader
        });
      }
      if (Array.isArray(acceptsToken)) {
        if (!isTokenTypeInAcceptedArray(acceptsToken, authenticateContext)) {
          return signedOutInvalidToken();
        }
      }
      if (authenticateContext.tokenInHeader) {
        if (acceptsToken === "any") {
          return authenticateAnyRequestWithTokenInHeader();
        }
        if (acceptsToken === TokenType.SessionToken) {
          return authenticateRequestWithTokenInHeader();
        }
        return authenticateMachineRequestWithTokenInHeader();
      }
      if (acceptsToken === TokenType.OAuthToken || acceptsToken === TokenType.ApiKey || acceptsToken === TokenType.MachineToken) {
        return signedOut({
          tokenType: acceptsToken,
          authenticateContext,
          reason: "No token in header"
        });
      }
      return authenticateRequestWithTokenInCookie();
    };
    var debugRequestState = (params) => {
      const { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain } = params;
      return { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain };
    };
    var convertTokenVerificationErrorReasonToAuthErrorReason = ({
      tokenError,
      refreshError
    }) => {
      switch (tokenError) {
        case TokenVerificationErrorReason.TokenExpired:
          return `${AuthErrorReason.SessionTokenExpired}-refresh-${refreshError}`;
        case TokenVerificationErrorReason.TokenNotActiveYet:
          return AuthErrorReason.SessionTokenNBF;
        case TokenVerificationErrorReason.TokenIatInTheFuture:
          return AuthErrorReason.SessionTokenIatInTheFuture;
        default:
          return AuthErrorReason.UnexpectedError;
      }
    };
    var defaultOptions = {
      secretKey: "",
      jwtKey: "",
      apiUrl: void 0,
      apiVersion: void 0,
      proxyUrl: "",
      publishableKey: "",
      isSatellite: false,
      domain: "",
      audience: ""
    };
    function createAuthenticateRequest(params) {
      const buildTimeOptions = mergePreDefinedOptions(defaultOptions, params.options);
      const apiClient = params.apiClient;
      const authenticateRequest2 = (request, options = {}) => {
        const { apiUrl, apiVersion } = buildTimeOptions;
        const runTimeOptions = mergePreDefinedOptions(buildTimeOptions, options);
        return authenticateRequest(request, {
          ...options,
          ...runTimeOptions,
          // We should add all the omitted props from options here (eg apiUrl / apiVersion)
          // to avoid runtime options override them.
          apiUrl,
          apiVersion,
          apiClient
        });
      };
      return {
        authenticateRequest: authenticateRequest2,
        debugRequestState
      };
    }
    var decorateObjectWithResources = async (obj, authObj, opts) => {
      const { loadSession, loadUser, loadOrganization } = opts || {};
      const { userId, sessionId, orgId } = authObj;
      const { sessions, users, organizations } = createBackendApiClient({ ...opts });
      const [sessionResp, userResp, organizationResp] = await Promise.all([
        loadSession && sessionId ? sessions.getSession(sessionId) : Promise.resolve(void 0),
        loadUser && userId ? users.getUser(userId) : Promise.resolve(void 0),
        loadOrganization && orgId ? organizations.getOrganization({ organizationId: orgId }) : Promise.resolve(void 0)
      ]);
      const resources = stripPrivateDataFromObject({
        session: sessionResp,
        user: userResp,
        organization: organizationResp
      });
      return Object.assign(obj, resources);
    };
    function stripPrivateDataFromObject(authObject) {
      const user = authObject.user ? { ...authObject.user } : authObject.user;
      const organization = authObject.organization ? { ...authObject.organization } : authObject.organization;
      prunePrivateMetadata(user);
      prunePrivateMetadata(organization);
      return { ...authObject, user, organization };
    }
    function prunePrivateMetadata(resource) {
      if (resource) {
        if ("privateMetadata" in resource) {
          delete resource["privateMetadata"];
        }
        if ("private_metadata" in resource) {
          delete resource["private_metadata"];
        }
      }
      return resource;
    }
    var import_authorization_errors = require_authorization_errors();
  }
});

// node_modules/@remix-run/server-runtime/dist/mode.js
var require_mode = __commonJS({
  "node_modules/@remix-run/server-runtime/dist/mode.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var ServerMode = function(ServerMode2) {
      ServerMode2["Development"] = "development";
      ServerMode2["Production"] = "production";
      ServerMode2["Test"] = "test";
      return ServerMode2;
    }({});
    function isServerMode(value) {
      return value === ServerMode.Development || value === ServerMode.Production || value === ServerMode.Test;
    }
    exports.ServerMode = ServerMode;
    exports.isServerMode = isServerMode;
  }
});

// node_modules/@remix-run/server-runtime/dist/errors.js
var require_errors2 = __commonJS({
  "node_modules/@remix-run/server-runtime/dist/errors.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var router = (init_router(), __toCommonJS(router_exports));
    var mode = require_mode();
    function sanitizeError(error, serverMode) {
      if (error instanceof Error && serverMode !== mode.ServerMode.Development) {
        let sanitized = new Error("Unexpected Server Error");
        sanitized.stack = void 0;
        return sanitized;
      }
      return error;
    }
    function sanitizeErrors(errors, serverMode) {
      return Object.entries(errors).reduce((acc, [routeId, error]) => {
        return Object.assign(acc, {
          [routeId]: sanitizeError(error, serverMode)
        });
      }, {});
    }
    function serializeError(error, serverMode) {
      let sanitized = sanitizeError(error, serverMode);
      return {
        message: sanitized.message,
        stack: sanitized.stack
      };
    }
    function serializeErrors(errors, serverMode) {
      if (!errors)
        return null;
      let entries = Object.entries(errors);
      let serialized = {};
      for (let [key, val] of entries) {
        if (router.isRouteErrorResponse(val)) {
          serialized[key] = {
            ...val,
            __type: "RouteErrorResponse"
          };
        } else if (val instanceof Error) {
          let sanitized = sanitizeError(val, serverMode);
          serialized[key] = {
            message: sanitized.message,
            stack: sanitized.stack,
            __type: "Error",
            // If this is a subclass (i.e., ReferenceError), send up the type so we
            // can re-create the same type during hydration.  This will only apply
            // in dev mode since all production errors are sanitized to normal
            // Error instances
            ...sanitized.name !== "Error" ? {
              __subType: sanitized.name
            } : {}
          };
        } else {
          serialized[key] = val;
        }
      }
      return serialized;
    }
    exports.sanitizeError = sanitizeError;
    exports.sanitizeErrors = sanitizeErrors;
    exports.serializeError = serializeError;
    exports.serializeErrors = serializeErrors;
  }
});

// node_modules/@remix-run/server-runtime/dist/responses.js
var require_responses = __commonJS({
  "node_modules/@remix-run/server-runtime/dist/responses.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var router = (init_router(), __toCommonJS(router_exports));
    var errors = require_errors2();
    var json = (data, init = {}) => {
      return router.json(data, init);
    };
    var defer = (data, init = {}) => {
      return router.defer(data, init);
    };
    var redirect = (url, init = 302) => {
      return router.redirect(url, init);
    };
    var replace2 = (url, init = 302) => {
      return router.replace(url, init);
    };
    var redirectDocument = (url, init = 302) => {
      return router.redirectDocument(url, init);
    };
    function isDeferredData(value) {
      let deferred = value;
      return deferred && typeof deferred === "object" && typeof deferred.data === "object" && typeof deferred.subscribe === "function" && typeof deferred.cancel === "function" && typeof deferred.resolveData === "function";
    }
    function isResponse(value) {
      return value != null && typeof value.status === "number" && typeof value.statusText === "string" && typeof value.headers === "object" && typeof value.body !== "undefined";
    }
    var redirectStatusCodes = /* @__PURE__ */ new Set([301, 302, 303, 307, 308]);
    function isRedirectStatusCode(statusCode) {
      return redirectStatusCodes.has(statusCode);
    }
    function isRedirectResponse(response) {
      return isRedirectStatusCode(response.status);
    }
    function isTrackedPromise(value) {
      return value != null && typeof value.then === "function" && value._tracked === true;
    }
    var DEFERRED_VALUE_PLACEHOLDER_PREFIX = "__deferred_promise:";
    function createDeferredReadableStream(deferredData, signal, serverMode) {
      let encoder = new TextEncoder();
      let stream = new ReadableStream({
        async start(controller) {
          let criticalData = {};
          let preresolvedKeys = [];
          for (let [key, value] of Object.entries(deferredData.data)) {
            if (isTrackedPromise(value)) {
              criticalData[key] = `${DEFERRED_VALUE_PLACEHOLDER_PREFIX}${key}`;
              if (typeof value._data !== "undefined" || typeof value._error !== "undefined") {
                preresolvedKeys.push(key);
              }
            } else {
              criticalData[key] = value;
            }
          }
          controller.enqueue(encoder.encode(JSON.stringify(criticalData) + "\n\n"));
          for (let preresolvedKey of preresolvedKeys) {
            enqueueTrackedPromise(controller, encoder, preresolvedKey, deferredData.data[preresolvedKey], serverMode);
          }
          let unsubscribe = deferredData.subscribe((aborted, settledKey) => {
            if (settledKey) {
              enqueueTrackedPromise(controller, encoder, settledKey, deferredData.data[settledKey], serverMode);
            }
          });
          await deferredData.resolveData(signal);
          unsubscribe();
          controller.close();
        }
      });
      return stream;
    }
    function enqueueTrackedPromise(controller, encoder, settledKey, promise, serverMode) {
      if ("_error" in promise) {
        controller.enqueue(encoder.encode("error:" + JSON.stringify({
          [settledKey]: promise._error instanceof Error ? errors.serializeError(promise._error, serverMode) : promise._error
        }) + "\n\n"));
      } else {
        controller.enqueue(encoder.encode("data:" + JSON.stringify({
          [settledKey]: promise._data ?? null
        }) + "\n\n"));
      }
    }
    exports.createDeferredReadableStream = createDeferredReadableStream;
    exports.defer = defer;
    exports.isDeferredData = isDeferredData;
    exports.isRedirectResponse = isRedirectResponse;
    exports.isRedirectStatusCode = isRedirectStatusCode;
    exports.isResponse = isResponse;
    exports.json = json;
    exports.redirect = redirect;
    exports.redirectDocument = redirectDocument;
    exports.replace = replace2;
  }
});

// node_modules/@clerk/backend/dist/index.js
var require_dist2 = __commonJS({
  "node_modules/@clerk/backend/dist/index.js"(exports, module) {
    "use strict";
    var __create = Object.create;
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __getProtoOf = Object.getPrototypeOf;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
      // If the importer is in node compatibility mode or this is not an ESM
      // file that has been converted to a CommonJS file using a Babel-
      // compatible transform (i.e. "__esModule" has not been set), then set
      // "default" to the CommonJS "module.exports" for node compatibility.
      isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
      mod
    ));
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var index_exports = {};
    __export2(index_exports, {
      createClerkClient: () => createClerkClient,
      verifyToken: () => verifyToken2
    });
    module.exports = __toCommonJS2(index_exports);
    var import_telemetry = require_telemetry();
    var SEPARATOR = "/";
    var MULTIPLE_SEPARATOR_REGEX = new RegExp("(?<!:)" + SEPARATOR + "{1,}", "g");
    function joinPaths(...args) {
      return args.filter((p) => p).join(SEPARATOR).replace(MULTIPLE_SEPARATOR_REGEX, SEPARATOR);
    }
    var AbstractAPI = class {
      constructor(request) {
        this.request = request;
      }
      requireId(id) {
        if (!id) {
          throw new Error("A valid resource ID is required.");
        }
      }
    };
    var basePath = "/actor_tokens";
    var ActorTokenAPI = class extends AbstractAPI {
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath,
          bodyParams: params
        });
      }
      async revoke(actorTokenId) {
        this.requireId(actorTokenId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath, actorTokenId, "revoke")
        });
      }
    };
    var basePath2 = "/accountless_applications";
    var AccountlessApplicationAPI = class extends AbstractAPI {
      async createAccountlessApplication() {
        return this.request({
          method: "POST",
          path: basePath2
        });
      }
      async completeAccountlessApplicationOnboarding() {
        return this.request({
          method: "POST",
          path: joinPaths(basePath2, "complete")
        });
      }
    };
    var basePath3 = "/allowlist_identifiers";
    var AllowlistIdentifierAPI = class extends AbstractAPI {
      async getAllowlistIdentifierList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath3,
          queryParams: { ...params, paginated: true }
        });
      }
      async createAllowlistIdentifier(params) {
        return this.request({
          method: "POST",
          path: basePath3,
          bodyParams: params
        });
      }
      async deleteAllowlistIdentifier(allowlistIdentifierId) {
        this.requireId(allowlistIdentifierId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath3, allowlistIdentifierId)
        });
      }
    };
    var basePath4 = "/api_keys";
    var APIKeysAPI = class extends AbstractAPI {
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath4,
          bodyParams: params
        });
      }
      async revoke(params) {
        const { apiKeyId, ...bodyParams } = params;
        this.requireId(apiKeyId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath4, apiKeyId, "revoke"),
          bodyParams
        });
      }
      async getSecret(apiKeyId) {
        this.requireId(apiKeyId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath4, apiKeyId, "secret")
        });
      }
      async verifySecret(secret) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath4, "verify"),
          bodyParams: { secret }
        });
      }
    };
    var basePath5 = "/beta_features";
    var BetaFeaturesAPI = class extends AbstractAPI {
      /**
       * Change the domain of a production instance.
       *
       * Changing the domain requires updating the DNS records accordingly, deploying new SSL certificates,
       * updating your Social Connection's redirect URLs and setting the new keys in your code.
       *
       * @remarks
       * WARNING: Changing your domain will invalidate all current user sessions (i.e. users will be logged out).
       *          Also, while your application is being deployed, a small downtime is expected to occur.
       */
      async changeDomain(params) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath5, "change_domain"),
          bodyParams: params
        });
      }
    };
    var basePath6 = "/blocklist_identifiers";
    var BlocklistIdentifierAPI = class extends AbstractAPI {
      async getBlocklistIdentifierList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath6,
          queryParams: params
        });
      }
      async createBlocklistIdentifier(params) {
        return this.request({
          method: "POST",
          path: basePath6,
          bodyParams: params
        });
      }
      async deleteBlocklistIdentifier(blocklistIdentifierId) {
        this.requireId(blocklistIdentifierId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath6, blocklistIdentifierId)
        });
      }
    };
    var basePath7 = "/clients";
    var ClientAPI = class extends AbstractAPI {
      async getClientList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath7,
          queryParams: { ...params, paginated: true }
        });
      }
      async getClient(clientId) {
        this.requireId(clientId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath7, clientId)
        });
      }
      verifyClient(token) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath7, "verify"),
          bodyParams: { token }
        });
      }
      async getHandshakePayload(queryParams) {
        return this.request({
          method: "GET",
          path: joinPaths(basePath7, "handshake_payload"),
          queryParams
        });
      }
    };
    var basePath8 = "/domains";
    var DomainAPI = class extends AbstractAPI {
      async list() {
        return this.request({
          method: "GET",
          path: basePath8
        });
      }
      async add(params) {
        return this.request({
          method: "POST",
          path: basePath8,
          bodyParams: params
        });
      }
      async update(params) {
        const { domainId, ...bodyParams } = params;
        this.requireId(domainId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath8, domainId),
          bodyParams
        });
      }
      /**
       * Deletes a satellite domain for the instance.
       * It is currently not possible to delete the instance's primary domain.
       */
      async delete(satelliteDomainId) {
        return this.deleteDomain(satelliteDomainId);
      }
      /**
       * @deprecated Use `delete` instead
       */
      async deleteDomain(satelliteDomainId) {
        this.requireId(satelliteDomainId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath8, satelliteDomainId)
        });
      }
    };
    var basePath9 = "/email_addresses";
    var EmailAddressAPI = class extends AbstractAPI {
      async getEmailAddress(emailAddressId) {
        this.requireId(emailAddressId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath9, emailAddressId)
        });
      }
      async createEmailAddress(params) {
        return this.request({
          method: "POST",
          path: basePath9,
          bodyParams: params
        });
      }
      async updateEmailAddress(emailAddressId, params = {}) {
        this.requireId(emailAddressId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath9, emailAddressId),
          bodyParams: params
        });
      }
      async deleteEmailAddress(emailAddressId) {
        this.requireId(emailAddressId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath9, emailAddressId)
        });
      }
    };
    var basePath10 = "/oauth_applications/access_tokens";
    var IdPOAuthAccessTokenApi = class extends AbstractAPI {
      async verifyAccessToken(accessToken) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath10, "verify"),
          bodyParams: { access_token: accessToken }
        });
      }
    };
    var basePath11 = "/instance";
    var InstanceAPI = class extends AbstractAPI {
      async get() {
        return this.request({
          method: "GET",
          path: basePath11
        });
      }
      async update(params) {
        return this.request({
          method: "PATCH",
          path: basePath11,
          bodyParams: params
        });
      }
      async updateRestrictions(params) {
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath11, "restrictions"),
          bodyParams: params
        });
      }
      async updateOrganizationSettings(params) {
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath11, "organization_settings"),
          bodyParams: params
        });
      }
    };
    var basePath12 = "/invitations";
    var InvitationAPI = class extends AbstractAPI {
      async getInvitationList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath12,
          queryParams: { ...params, paginated: true }
        });
      }
      async createInvitation(params) {
        return this.request({
          method: "POST",
          path: basePath12,
          bodyParams: params
        });
      }
      async revokeInvitation(invitationId) {
        this.requireId(invitationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath12, invitationId, "revoke")
        });
      }
    };
    var basePath13 = "/m2m_tokens";
    var MachineTokensApi = class extends AbstractAPI {
      async verifySecret(secret) {
        return this.request({
          method: "POST",
          path: joinPaths(basePath13, "verify"),
          bodyParams: { secret }
        });
      }
    };
    var basePath14 = "/jwks";
    var JwksAPI = class extends AbstractAPI {
      async getJwks() {
        return this.request({
          method: "GET",
          path: basePath14
        });
      }
    };
    var basePath15 = "/jwt_templates";
    var JwtTemplatesApi = class extends AbstractAPI {
      async list(params = {}) {
        return this.request({
          method: "GET",
          path: basePath15,
          queryParams: { ...params, paginated: true }
        });
      }
      async get(templateId) {
        this.requireId(templateId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath15, templateId)
        });
      }
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath15,
          bodyParams: params
        });
      }
      async update(params) {
        const { templateId, ...bodyParams } = params;
        this.requireId(templateId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath15, templateId),
          bodyParams
        });
      }
      async delete(templateId) {
        this.requireId(templateId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath15, templateId)
        });
      }
    };
    var import_crypto = (init_crypto(), __toCommonJS(crypto_exports));
    var globalFetch = fetch.bind(globalThis);
    var runtime = {
      crypto: import_crypto.webcrypto,
      get fetch() {
        return false ? fetch : globalFetch;
      },
      AbortController: globalThis.AbortController,
      Blob: globalThis.Blob,
      FormData: globalThis.FormData,
      Headers: globalThis.Headers,
      Request: globalThis.Request,
      Response: globalThis.Response
    };
    var basePath16 = "/organizations";
    var OrganizationAPI = class extends AbstractAPI {
      async getOrganizationList(params) {
        return this.request({
          method: "GET",
          path: basePath16,
          queryParams: params
        });
      }
      async createOrganization(params) {
        return this.request({
          method: "POST",
          path: basePath16,
          bodyParams: params
        });
      }
      async getOrganization(params) {
        const { includeMembersCount } = params;
        const organizationIdOrSlug = "organizationId" in params ? params.organizationId : params.slug;
        this.requireId(organizationIdOrSlug);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationIdOrSlug),
          queryParams: {
            includeMembersCount
          }
        });
      }
      async updateOrganization(organizationId, params) {
        this.requireId(organizationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId),
          bodyParams: params
        });
      }
      async updateOrganizationLogo(organizationId, params) {
        this.requireId(organizationId);
        const formData = new runtime.FormData();
        formData.append("file", params == null ? void 0 : params.file);
        if (params == null ? void 0 : params.uploaderUserId) {
          formData.append("uploader_user_id", params == null ? void 0 : params.uploaderUserId);
        }
        return this.request({
          method: "PUT",
          path: joinPaths(basePath16, organizationId, "logo"),
          formData
        });
      }
      async deleteOrganizationLogo(organizationId) {
        this.requireId(organizationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId, "logo")
        });
      }
      async updateOrganizationMetadata(organizationId, params) {
        this.requireId(organizationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "metadata"),
          bodyParams: params
        });
      }
      async deleteOrganization(organizationId) {
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId)
        });
      }
      async getOrganizationMembershipList(params) {
        const { organizationId, ...queryParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "memberships"),
          queryParams
        });
      }
      async getInstanceOrganizationMembershipList(params) {
        return this.request({
          method: "GET",
          path: "/organization_memberships",
          queryParams: params
        });
      }
      async createOrganizationMembership(params) {
        const { organizationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "memberships"),
          bodyParams
        });
      }
      async updateOrganizationMembership(params) {
        const { organizationId, userId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "memberships", userId),
          bodyParams
        });
      }
      async updateOrganizationMembershipMetadata(params) {
        const { organizationId, userId, ...bodyParams } = params;
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "memberships", userId, "metadata"),
          bodyParams
        });
      }
      async deleteOrganizationMembership(params) {
        const { organizationId, userId } = params;
        this.requireId(organizationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId, "memberships", userId)
        });
      }
      async getOrganizationInvitationList(params) {
        const { organizationId, ...queryParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "invitations"),
          queryParams
        });
      }
      async createOrganizationInvitation(params) {
        const { organizationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "invitations"),
          bodyParams
        });
      }
      async createOrganizationInvitationBulk(organizationId, params) {
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "invitations", "bulk"),
          bodyParams: params
        });
      }
      async getOrganizationInvitation(params) {
        const { organizationId, invitationId } = params;
        this.requireId(organizationId);
        this.requireId(invitationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "invitations", invitationId)
        });
      }
      async revokeOrganizationInvitation(params) {
        const { organizationId, invitationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "invitations", invitationId, "revoke"),
          bodyParams
        });
      }
      async getOrganizationDomainList(params) {
        const { organizationId, ...queryParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath16, organizationId, "domains"),
          queryParams
        });
      }
      async createOrganizationDomain(params) {
        const { organizationId, ...bodyParams } = params;
        this.requireId(organizationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath16, organizationId, "domains"),
          bodyParams: {
            ...bodyParams,
            verified: bodyParams.verified ?? true
          }
        });
      }
      async updateOrganizationDomain(params) {
        const { organizationId, domainId, ...bodyParams } = params;
        this.requireId(organizationId);
        this.requireId(domainId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath16, organizationId, "domains", domainId),
          bodyParams
        });
      }
      async deleteOrganizationDomain(params) {
        const { organizationId, domainId } = params;
        this.requireId(organizationId);
        this.requireId(domainId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath16, organizationId, "domains", domainId)
        });
      }
    };
    var basePath17 = "/oauth_applications";
    var OAuthApplicationsApi = class extends AbstractAPI {
      async list(params = {}) {
        return this.request({
          method: "GET",
          path: basePath17,
          queryParams: params
        });
      }
      async get(oauthApplicationId) {
        this.requireId(oauthApplicationId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath17, oauthApplicationId)
        });
      }
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath17,
          bodyParams: params
        });
      }
      async update(params) {
        const { oauthApplicationId, ...bodyParams } = params;
        this.requireId(oauthApplicationId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath17, oauthApplicationId),
          bodyParams
        });
      }
      async delete(oauthApplicationId) {
        this.requireId(oauthApplicationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath17, oauthApplicationId)
        });
      }
      async rotateSecret(oauthApplicationId) {
        this.requireId(oauthApplicationId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath17, oauthApplicationId, "rotate_secret")
        });
      }
    };
    var basePath18 = "/phone_numbers";
    var PhoneNumberAPI = class extends AbstractAPI {
      async getPhoneNumber(phoneNumberId) {
        this.requireId(phoneNumberId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath18, phoneNumberId)
        });
      }
      async createPhoneNumber(params) {
        return this.request({
          method: "POST",
          path: basePath18,
          bodyParams: params
        });
      }
      async updatePhoneNumber(phoneNumberId, params = {}) {
        this.requireId(phoneNumberId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath18, phoneNumberId),
          bodyParams: params
        });
      }
      async deletePhoneNumber(phoneNumberId) {
        this.requireId(phoneNumberId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath18, phoneNumberId)
        });
      }
    };
    var basePath19 = "/proxy_checks";
    var ProxyCheckAPI = class extends AbstractAPI {
      async verify(params) {
        return this.request({
          method: "POST",
          path: basePath19,
          bodyParams: params
        });
      }
    };
    var basePath20 = "/redirect_urls";
    var RedirectUrlAPI = class extends AbstractAPI {
      async getRedirectUrlList() {
        return this.request({
          method: "GET",
          path: basePath20,
          queryParams: { paginated: true }
        });
      }
      async getRedirectUrl(redirectUrlId) {
        this.requireId(redirectUrlId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath20, redirectUrlId)
        });
      }
      async createRedirectUrl(params) {
        return this.request({
          method: "POST",
          path: basePath20,
          bodyParams: params
        });
      }
      async deleteRedirectUrl(redirectUrlId) {
        this.requireId(redirectUrlId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath20, redirectUrlId)
        });
      }
    };
    var basePath21 = "/saml_connections";
    var SamlConnectionAPI = class extends AbstractAPI {
      async getSamlConnectionList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath21,
          queryParams: params
        });
      }
      async createSamlConnection(params) {
        return this.request({
          method: "POST",
          path: basePath21,
          bodyParams: params
        });
      }
      async getSamlConnection(samlConnectionId) {
        this.requireId(samlConnectionId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath21, samlConnectionId)
        });
      }
      async updateSamlConnection(samlConnectionId, params = {}) {
        this.requireId(samlConnectionId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath21, samlConnectionId),
          bodyParams: params
        });
      }
      async deleteSamlConnection(samlConnectionId) {
        this.requireId(samlConnectionId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath21, samlConnectionId)
        });
      }
    };
    var basePath22 = "/sessions";
    var SessionAPI = class extends AbstractAPI {
      async getSessionList(params = {}) {
        return this.request({
          method: "GET",
          path: basePath22,
          queryParams: { ...params, paginated: true }
        });
      }
      async getSession(sessionId) {
        this.requireId(sessionId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath22, sessionId)
        });
      }
      async createSession(params) {
        return this.request({
          method: "POST",
          path: basePath22,
          bodyParams: params
        });
      }
      async revokeSession(sessionId) {
        this.requireId(sessionId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath22, sessionId, "revoke")
        });
      }
      async verifySession(sessionId, token) {
        this.requireId(sessionId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath22, sessionId, "verify"),
          bodyParams: { token }
        });
      }
      /**
       * Retrieves a session token or generates a JWT using a specified template.
       *
       * @param sessionId - The ID of the session for which to generate the token
       * @param template - Optional name of the JWT template configured in the Clerk Dashboard.
       * @param expiresInSeconds - Optional expiration time for the token in seconds.
       *   If not provided, uses the default expiration.
       *
       * @returns A promise that resolves to the generated token
       *
       * @throws {Error} When sessionId is invalid or empty
       */
      async getToken(sessionId, template, expiresInSeconds) {
        this.requireId(sessionId);
        const path = template ? joinPaths(basePath22, sessionId, "tokens", template) : joinPaths(basePath22, sessionId, "tokens");
        const requestOptions = {
          method: "POST",
          path
        };
        if (expiresInSeconds !== void 0) {
          requestOptions.bodyParams = { expires_in_seconds: expiresInSeconds };
        }
        return this.request(requestOptions);
      }
      async refreshSession(sessionId, params) {
        this.requireId(sessionId);
        const { suffixed_cookies, ...restParams } = params;
        return this.request({
          method: "POST",
          path: joinPaths(basePath22, sessionId, "refresh"),
          bodyParams: restParams,
          queryParams: { suffixed_cookies }
        });
      }
    };
    var basePath23 = "/sign_in_tokens";
    var SignInTokenAPI = class extends AbstractAPI {
      async createSignInToken(params) {
        return this.request({
          method: "POST",
          path: basePath23,
          bodyParams: params
        });
      }
      async revokeSignInToken(signInTokenId) {
        this.requireId(signInTokenId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath23, signInTokenId, "revoke")
        });
      }
    };
    var basePath24 = "/sign_ups";
    var SignUpAPI = class extends AbstractAPI {
      async get(signUpAttemptId) {
        this.requireId(signUpAttemptId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath24, signUpAttemptId)
        });
      }
      async update(params) {
        const { signUpAttemptId, ...bodyParams } = params;
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath24, signUpAttemptId),
          bodyParams
        });
      }
    };
    var basePath25 = "/testing_tokens";
    var TestingTokenAPI = class extends AbstractAPI {
      async createTestingToken() {
        return this.request({
          method: "POST",
          path: basePath25
        });
      }
    };
    var import_url = require_url();
    var import_retry = require_retry();
    var import_keys = require_keys();
    var import_deprecated = require_deprecated();
    var import_error = require_error();
    var import_keys2 = require_keys();
    var errorThrower = (0, import_error.buildErrorThrower)({ packageName: "@clerk/backend" });
    var { isDevOrStagingUrl } = (0, import_keys2.createDevOrStagingUrlCache)();
    var basePath26 = "/users";
    var UserAPI = class extends AbstractAPI {
      async getUserList(params = {}) {
        const { limit, offset, orderBy, ...userCountParams } = params;
        const [data, totalCount] = await Promise.all([
          this.request({
            method: "GET",
            path: basePath26,
            queryParams: params
          }),
          this.getCount(userCountParams)
        ]);
        return { data, totalCount };
      }
      async getUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId)
        });
      }
      async createUser(params) {
        return this.request({
          method: "POST",
          path: basePath26,
          bodyParams: params
        });
      }
      async updateUser(userId, params = {}) {
        this.requireId(userId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath26, userId),
          bodyParams: params
        });
      }
      async updateUserProfileImage(userId, params) {
        this.requireId(userId);
        const formData = new runtime.FormData();
        formData.append("file", params == null ? void 0 : params.file);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "profile_image"),
          formData
        });
      }
      async updateUserMetadata(userId, params) {
        this.requireId(userId);
        return this.request({
          method: "PATCH",
          path: joinPaths(basePath26, userId, "metadata"),
          bodyParams: params
        });
      }
      async deleteUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId)
        });
      }
      async getCount(params = {}) {
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, "count"),
          queryParams: params
        });
      }
      async getUserOauthAccessToken(userId, provider) {
        this.requireId(userId);
        const hasPrefix = provider.startsWith("oauth_");
        const _provider = hasPrefix ? provider : `oauth_${provider}`;
        if (hasPrefix) {
          (0, import_deprecated.deprecated)(
            "getUserOauthAccessToken(userId, provider)",
            "Remove the `oauth_` prefix from the `provider` argument."
          );
        }
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId, "oauth_access_tokens", _provider),
          queryParams: { paginated: true }
        });
      }
      async disableUserMFA(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "mfa")
        });
      }
      async getOrganizationMembershipList(params) {
        const { userId, limit, offset } = params;
        this.requireId(userId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId, "organization_memberships"),
          queryParams: { limit, offset }
        });
      }
      async getOrganizationInvitationList(params) {
        const { userId, ...queryParams } = params;
        this.requireId(userId);
        return this.request({
          method: "GET",
          path: joinPaths(basePath26, userId, "organization_invitations"),
          queryParams
        });
      }
      async verifyPassword(params) {
        const { userId, password } = params;
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "verify_password"),
          bodyParams: { password }
        });
      }
      async verifyTOTP(params) {
        const { userId, code } = params;
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "verify_totp"),
          bodyParams: { code }
        });
      }
      async banUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "ban")
        });
      }
      async unbanUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "unban")
        });
      }
      async lockUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "lock")
        });
      }
      async unlockUser(userId) {
        this.requireId(userId);
        return this.request({
          method: "POST",
          path: joinPaths(basePath26, userId, "unlock")
        });
      }
      async deleteUserProfileImage(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "profile_image")
        });
      }
      async deleteUserPasskey(params) {
        this.requireId(params.userId);
        this.requireId(params.passkeyIdentificationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, params.userId, "passkeys", params.passkeyIdentificationId)
        });
      }
      async deleteUserWeb3Wallet(params) {
        this.requireId(params.userId);
        this.requireId(params.web3WalletIdentificationId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, params.userId, "web3_wallets", params.web3WalletIdentificationId)
        });
      }
      async deleteUserExternalAccount(params) {
        this.requireId(params.userId);
        this.requireId(params.externalAccountId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, params.userId, "external_accounts", params.externalAccountId)
        });
      }
      async deleteUserBackupCodes(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "backup_code")
        });
      }
      async deleteUserTOTP(userId) {
        this.requireId(userId);
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath26, userId, "totp")
        });
      }
    };
    var basePath27 = "/waitlist_entries";
    var WaitlistEntryAPI = class extends AbstractAPI {
      async list(params = {}) {
        return this.request({
          method: "GET",
          path: basePath27,
          queryParams: params
        });
      }
      async create(params) {
        return this.request({
          method: "POST",
          path: basePath27,
          bodyParams: params
        });
      }
    };
    var basePath28 = "/webhooks";
    var WebhookAPI = class extends AbstractAPI {
      async createSvixApp() {
        return this.request({
          method: "POST",
          path: joinPaths(basePath28, "svix")
        });
      }
      async generateSvixAuthURL() {
        return this.request({
          method: "POST",
          path: joinPaths(basePath28, "svix_url")
        });
      }
      async deleteSvixApp() {
        return this.request({
          method: "DELETE",
          path: joinPaths(basePath28, "svix")
        });
      }
    };
    var import_error2 = require_error();
    var import_snakecase_keys = __toESM(require_snakecase_keys());
    var API_URL = "https://api.clerk.com";
    var API_VERSION = "v1";
    var USER_AGENT = `${"@clerk/backend"}@${"2.3.1"}`;
    var MAX_CACHE_LAST_UPDATED_AT_SECONDS = 5 * 60;
    var SUPPORTED_BAPI_VERSION = "2025-04-10";
    var Attributes = {
      AuthToken: "__clerkAuthToken",
      AuthSignature: "__clerkAuthSignature",
      AuthStatus: "__clerkAuthStatus",
      AuthReason: "__clerkAuthReason",
      AuthMessage: "__clerkAuthMessage",
      ClerkUrl: "__clerkUrl"
    };
    var Cookies = {
      Session: "__session",
      Refresh: "__refresh",
      ClientUat: "__client_uat",
      Handshake: "__clerk_handshake",
      DevBrowser: "__clerk_db_jwt",
      RedirectCount: "__clerk_redirect_count",
      HandshakeNonce: "__clerk_handshake_nonce"
    };
    var QueryParameters = {
      ClerkSynced: "__clerk_synced",
      SuffixedCookies: "suffixed_cookies",
      ClerkRedirectUrl: "__clerk_redirect_url",
      // use the reference to Cookies to indicate that it's the same value
      DevBrowser: Cookies.DevBrowser,
      Handshake: Cookies.Handshake,
      HandshakeHelp: "__clerk_help",
      LegacyDevBrowser: "__dev_session",
      HandshakeReason: "__clerk_hs_reason",
      HandshakeNonce: Cookies.HandshakeNonce,
      HandshakeFormat: "format"
    };
    var Headers2 = {
      Accept: "accept",
      AuthMessage: "x-clerk-auth-message",
      Authorization: "authorization",
      AuthReason: "x-clerk-auth-reason",
      AuthSignature: "x-clerk-auth-signature",
      AuthStatus: "x-clerk-auth-status",
      AuthToken: "x-clerk-auth-token",
      CacheControl: "cache-control",
      ClerkRedirectTo: "x-clerk-redirect-to",
      ClerkRequestData: "x-clerk-request-data",
      ClerkUrl: "x-clerk-clerk-url",
      CloudFrontForwardedProto: "cloudfront-forwarded-proto",
      ContentType: "content-type",
      ContentSecurityPolicy: "content-security-policy",
      ContentSecurityPolicyReportOnly: "content-security-policy-report-only",
      EnableDebug: "x-clerk-debug",
      ForwardedHost: "x-forwarded-host",
      ForwardedPort: "x-forwarded-port",
      ForwardedProto: "x-forwarded-proto",
      Host: "host",
      Location: "location",
      Nonce: "x-nonce",
      Origin: "origin",
      Referrer: "referer",
      SecFetchDest: "sec-fetch-dest",
      UserAgent: "user-agent",
      ReportingEndpoints: "reporting-endpoints"
    };
    var ContentTypes = {
      Json: "application/json"
    };
    var constants = {
      Attributes,
      Cookies,
      Headers: Headers2,
      ContentTypes,
      QueryParameters
    };
    function assertValidSecretKey(val) {
      if (!val || typeof val !== "string") {
        throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.");
      }
    }
    function assertValidPublishableKey(val) {
      (0, import_keys.parsePublishableKey)(val, { fatal: true });
    }
    var AccountlessApplication = class _AccountlessApplication {
      constructor(publishableKey, secretKey, claimUrl, apiKeysUrl) {
        this.publishableKey = publishableKey;
        this.secretKey = secretKey;
        this.claimUrl = claimUrl;
        this.apiKeysUrl = apiKeysUrl;
      }
      static fromJSON(data) {
        return new _AccountlessApplication(data.publishable_key, data.secret_key, data.claim_url, data.api_keys_url);
      }
    };
    var ActorToken = class _ActorToken {
      constructor(id, status, userId, actor, token, url, createdAt, updatedAt) {
        this.id = id;
        this.status = status;
        this.userId = userId;
        this.actor = actor;
        this.token = token;
        this.url = url;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _ActorToken(
          data.id,
          data.status,
          data.user_id,
          data.actor,
          data.token,
          data.url,
          data.created_at,
          data.updated_at
        );
      }
    };
    var AllowlistIdentifier = class _AllowlistIdentifier {
      constructor(id, identifier, identifierType, createdAt, updatedAt, instanceId, invitationId) {
        this.id = id;
        this.identifier = identifier;
        this.identifierType = identifierType;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.instanceId = instanceId;
        this.invitationId = invitationId;
      }
      static fromJSON(data) {
        return new _AllowlistIdentifier(
          data.id,
          data.identifier,
          data.identifier_type,
          data.created_at,
          data.updated_at,
          data.instance_id,
          data.invitation_id
        );
      }
    };
    var APIKey = class _APIKey {
      constructor(id, type, name, subject, scopes, claims, revoked, revocationReason, expired, expiration, createdBy, description, lastUsedAt, createdAt, updatedAt) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.subject = subject;
        this.scopes = scopes;
        this.claims = claims;
        this.revoked = revoked;
        this.revocationReason = revocationReason;
        this.expired = expired;
        this.expiration = expiration;
        this.createdBy = createdBy;
        this.description = description;
        this.lastUsedAt = lastUsedAt;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _APIKey(
          data.id,
          data.type,
          data.name,
          data.subject,
          data.scopes,
          data.claims,
          data.revoked,
          data.revocation_reason,
          data.expired,
          data.expiration,
          data.created_by,
          data.description,
          data.last_used_at,
          data.created_at,
          data.updated_at
        );
      }
    };
    var BlocklistIdentifier = class _BlocklistIdentifier {
      constructor(id, identifier, identifierType, createdAt, updatedAt, instanceId) {
        this.id = id;
        this.identifier = identifier;
        this.identifierType = identifierType;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.instanceId = instanceId;
      }
      static fromJSON(data) {
        return new _BlocklistIdentifier(
          data.id,
          data.identifier,
          data.identifier_type,
          data.created_at,
          data.updated_at,
          data.instance_id
        );
      }
    };
    var SessionActivity = class _SessionActivity {
      constructor(id, isMobile, ipAddress, city, country, browserVersion, browserName, deviceType) {
        this.id = id;
        this.isMobile = isMobile;
        this.ipAddress = ipAddress;
        this.city = city;
        this.country = country;
        this.browserVersion = browserVersion;
        this.browserName = browserName;
        this.deviceType = deviceType;
      }
      static fromJSON(data) {
        return new _SessionActivity(
          data.id,
          data.is_mobile,
          data.ip_address,
          data.city,
          data.country,
          data.browser_version,
          data.browser_name,
          data.device_type
        );
      }
    };
    var Session = class _Session {
      constructor(id, clientId, userId, status, lastActiveAt, expireAt, abandonAt, createdAt, updatedAt, lastActiveOrganizationId, latestActivity, actor = null) {
        this.id = id;
        this.clientId = clientId;
        this.userId = userId;
        this.status = status;
        this.lastActiveAt = lastActiveAt;
        this.expireAt = expireAt;
        this.abandonAt = abandonAt;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.lastActiveOrganizationId = lastActiveOrganizationId;
        this.latestActivity = latestActivity;
        this.actor = actor;
      }
      static fromJSON(data) {
        return new _Session(
          data.id,
          data.client_id,
          data.user_id,
          data.status,
          data.last_active_at,
          data.expire_at,
          data.abandon_at,
          data.created_at,
          data.updated_at,
          data.last_active_organization_id,
          data.latest_activity && SessionActivity.fromJSON(data.latest_activity),
          data.actor
        );
      }
    };
    var Client = class _Client {
      constructor(id, sessionIds, sessions, signInId, signUpId, lastActiveSessionId, createdAt, updatedAt) {
        this.id = id;
        this.sessionIds = sessionIds;
        this.sessions = sessions;
        this.signInId = signInId;
        this.signUpId = signUpId;
        this.lastActiveSessionId = lastActiveSessionId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _Client(
          data.id,
          data.session_ids,
          data.sessions.map((x) => Session.fromJSON(x)),
          data.sign_in_id,
          data.sign_up_id,
          data.last_active_session_id,
          data.created_at,
          data.updated_at
        );
      }
    };
    var CnameTarget = class _CnameTarget {
      constructor(host, value, required) {
        this.host = host;
        this.value = value;
        this.required = required;
      }
      static fromJSON(data) {
        return new _CnameTarget(data.host, data.value, data.required);
      }
    };
    var Cookies2 = class _Cookies {
      constructor(cookies) {
        this.cookies = cookies;
      }
      static fromJSON(data) {
        return new _Cookies(data.cookies);
      }
    };
    var DeletedObject = class _DeletedObject {
      constructor(object, id, slug, deleted) {
        this.object = object;
        this.id = id;
        this.slug = slug;
        this.deleted = deleted;
      }
      static fromJSON(data) {
        return new _DeletedObject(data.object, data.id || null, data.slug || null, data.deleted);
      }
    };
    var Domain = class _Domain {
      constructor(id, name, isSatellite, frontendApiUrl, developmentOrigin, cnameTargets, accountsPortalUrl, proxyUrl) {
        this.id = id;
        this.name = name;
        this.isSatellite = isSatellite;
        this.frontendApiUrl = frontendApiUrl;
        this.developmentOrigin = developmentOrigin;
        this.cnameTargets = cnameTargets;
        this.accountsPortalUrl = accountsPortalUrl;
        this.proxyUrl = proxyUrl;
      }
      static fromJSON(data) {
        return new _Domain(
          data.id,
          data.name,
          data.is_satellite,
          data.frontend_api_url,
          data.development_origin,
          data.cname_targets && data.cname_targets.map((x) => CnameTarget.fromJSON(x)),
          data.accounts_portal_url,
          data.proxy_url
        );
      }
    };
    var Email = class _Email {
      constructor(id, fromEmailName, emailAddressId, toEmailAddress, subject, body, bodyPlain, status, slug, data, deliveredByClerk) {
        this.id = id;
        this.fromEmailName = fromEmailName;
        this.emailAddressId = emailAddressId;
        this.toEmailAddress = toEmailAddress;
        this.subject = subject;
        this.body = body;
        this.bodyPlain = bodyPlain;
        this.status = status;
        this.slug = slug;
        this.data = data;
        this.deliveredByClerk = deliveredByClerk;
      }
      static fromJSON(data) {
        return new _Email(
          data.id,
          data.from_email_name,
          data.email_address_id,
          data.to_email_address,
          data.subject,
          data.body,
          data.body_plain,
          data.status,
          data.slug,
          data.data,
          data.delivered_by_clerk
        );
      }
    };
    var IdentificationLink = class _IdentificationLink {
      constructor(id, type) {
        this.id = id;
        this.type = type;
      }
      static fromJSON(data) {
        return new _IdentificationLink(data.id, data.type);
      }
    };
    var Verification = class _Verification {
      constructor(status, strategy, externalVerificationRedirectURL = null, attempts = null, expireAt = null, nonce = null, message = null) {
        this.status = status;
        this.strategy = strategy;
        this.externalVerificationRedirectURL = externalVerificationRedirectURL;
        this.attempts = attempts;
        this.expireAt = expireAt;
        this.nonce = nonce;
        this.message = message;
      }
      static fromJSON(data) {
        return new _Verification(
          data.status,
          data.strategy,
          data.external_verification_redirect_url ? new URL(data.external_verification_redirect_url) : null,
          data.attempts,
          data.expire_at,
          data.nonce
        );
      }
    };
    var EmailAddress = class _EmailAddress {
      constructor(id, emailAddress, verification, linkedTo) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.verification = verification;
        this.linkedTo = linkedTo;
      }
      static fromJSON(data) {
        return new _EmailAddress(
          data.id,
          data.email_address,
          data.verification && Verification.fromJSON(data.verification),
          data.linked_to.map((link) => IdentificationLink.fromJSON(link))
        );
      }
    };
    var ExternalAccount = class _ExternalAccount {
      constructor(id, provider, identificationId, externalId, approvedScopes, emailAddress, firstName, lastName, imageUrl, username, phoneNumber, publicMetadata = {}, label, verification) {
        this.id = id;
        this.provider = provider;
        this.identificationId = identificationId;
        this.externalId = externalId;
        this.approvedScopes = approvedScopes;
        this.emailAddress = emailAddress;
        this.firstName = firstName;
        this.lastName = lastName;
        this.imageUrl = imageUrl;
        this.username = username;
        this.phoneNumber = phoneNumber;
        this.publicMetadata = publicMetadata;
        this.label = label;
        this.verification = verification;
      }
      static fromJSON(data) {
        return new _ExternalAccount(
          data.id,
          data.provider,
          data.identification_id,
          data.provider_user_id,
          data.approved_scopes,
          data.email_address,
          data.first_name,
          data.last_name,
          data.image_url || "",
          data.username,
          data.phone_number,
          data.public_metadata,
          data.label,
          data.verification && Verification.fromJSON(data.verification)
        );
      }
    };
    var IdPOAuthAccessToken = class _IdPOAuthAccessToken {
      constructor(id, clientId, type, subject, scopes, revoked, revocationReason, expired, expiration, createdAt, updatedAt) {
        this.id = id;
        this.clientId = clientId;
        this.type = type;
        this.subject = subject;
        this.scopes = scopes;
        this.revoked = revoked;
        this.revocationReason = revocationReason;
        this.expired = expired;
        this.expiration = expiration;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _IdPOAuthAccessToken(
          data.id,
          data.client_id,
          data.type,
          data.subject,
          data.scopes,
          data.revoked,
          data.revocation_reason,
          data.expired,
          data.expiration,
          data.created_at,
          data.updated_at
        );
      }
    };
    var Instance = class _Instance {
      constructor(id, environmentType, allowedOrigins) {
        this.id = id;
        this.environmentType = environmentType;
        this.allowedOrigins = allowedOrigins;
      }
      static fromJSON(data) {
        return new _Instance(data.id, data.environment_type, data.allowed_origins);
      }
    };
    var InstanceRestrictions = class _InstanceRestrictions {
      constructor(allowlist, blocklist, blockEmailSubaddresses, blockDisposableEmailDomains, ignoreDotsForGmailAddresses) {
        this.allowlist = allowlist;
        this.blocklist = blocklist;
        this.blockEmailSubaddresses = blockEmailSubaddresses;
        this.blockDisposableEmailDomains = blockDisposableEmailDomains;
        this.ignoreDotsForGmailAddresses = ignoreDotsForGmailAddresses;
      }
      static fromJSON(data) {
        return new _InstanceRestrictions(
          data.allowlist,
          data.blocklist,
          data.block_email_subaddresses,
          data.block_disposable_email_domains,
          data.ignore_dots_for_gmail_addresses
        );
      }
    };
    var InstanceSettings = class _InstanceSettings {
      constructor(id, restrictedToAllowlist, fromEmailAddress, progressiveSignUp, enhancedEmailDeliverability) {
        this.id = id;
        this.restrictedToAllowlist = restrictedToAllowlist;
        this.fromEmailAddress = fromEmailAddress;
        this.progressiveSignUp = progressiveSignUp;
        this.enhancedEmailDeliverability = enhancedEmailDeliverability;
      }
      static fromJSON(data) {
        return new _InstanceSettings(
          data.id,
          data.restricted_to_allowlist,
          data.from_email_address,
          data.progressive_sign_up,
          data.enhanced_email_deliverability
        );
      }
    };
    var Invitation = class _Invitation {
      constructor(id, emailAddress, publicMetadata, createdAt, updatedAt, status, url, revoked) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.publicMetadata = publicMetadata;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.status = status;
        this.url = url;
        this.revoked = revoked;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _Invitation(
          data.id,
          data.email_address,
          data.public_metadata,
          data.created_at,
          data.updated_at,
          data.status,
          data.url,
          data.revoked
        );
        res._raw = data;
        return res;
      }
    };
    var ObjectType = {
      AccountlessApplication: "accountless_application",
      ActorToken: "actor_token",
      AllowlistIdentifier: "allowlist_identifier",
      ApiKey: "api_key",
      BlocklistIdentifier: "blocklist_identifier",
      Client: "client",
      Cookies: "cookies",
      Domain: "domain",
      Email: "email",
      EmailAddress: "email_address",
      ExternalAccount: "external_account",
      FacebookAccount: "facebook_account",
      GoogleAccount: "google_account",
      Instance: "instance",
      InstanceRestrictions: "instance_restrictions",
      InstanceSettings: "instance_settings",
      Invitation: "invitation",
      MachineToken: "machine_to_machine_token",
      JwtTemplate: "jwt_template",
      OauthAccessToken: "oauth_access_token",
      IdpOAuthAccessToken: "clerk_idp_oauth_access_token",
      OAuthApplication: "oauth_application",
      Organization: "organization",
      OrganizationDomain: "organization_domain",
      OrganizationInvitation: "organization_invitation",
      OrganizationMembership: "organization_membership",
      OrganizationSettings: "organization_settings",
      PhoneNumber: "phone_number",
      ProxyCheck: "proxy_check",
      RedirectUrl: "redirect_url",
      SamlAccount: "saml_account",
      SamlConnection: "saml_connection",
      Session: "session",
      SignInAttempt: "sign_in_attempt",
      SignInToken: "sign_in_token",
      SignUpAttempt: "sign_up_attempt",
      SmsMessage: "sms_message",
      User: "user",
      WaitlistEntry: "waitlist_entry",
      Web3Wallet: "web3_wallet",
      Token: "token",
      TotalCount: "total_count",
      TestingToken: "testing_token",
      Role: "role",
      Permission: "permission"
    };
    var MachineToken = class _MachineToken {
      constructor(id, name, subject, scopes, claims, revoked, revocationReason, expired, expiration, createdBy, creationReason, createdAt, updatedAt) {
        this.id = id;
        this.name = name;
        this.subject = subject;
        this.scopes = scopes;
        this.claims = claims;
        this.revoked = revoked;
        this.revocationReason = revocationReason;
        this.expired = expired;
        this.expiration = expiration;
        this.createdBy = createdBy;
        this.creationReason = creationReason;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _MachineToken(
          data.id,
          data.name,
          data.subject,
          data.scopes,
          data.claims,
          data.revoked,
          data.revocation_reason,
          data.expired,
          data.expiration,
          data.created_by,
          data.creation_reason,
          data.created_at,
          data.updated_at
        );
      }
    };
    var JwtTemplate = class _JwtTemplate {
      constructor(id, name, claims, lifetime, allowedClockSkew, customSigningKey, signingAlgorithm, createdAt, updatedAt) {
        this.id = id;
        this.name = name;
        this.claims = claims;
        this.lifetime = lifetime;
        this.allowedClockSkew = allowedClockSkew;
        this.customSigningKey = customSigningKey;
        this.signingAlgorithm = signingAlgorithm;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _JwtTemplate(
          data.id,
          data.name,
          data.claims,
          data.lifetime,
          data.allowed_clock_skew,
          data.custom_signing_key,
          data.signing_algorithm,
          data.created_at,
          data.updated_at
        );
      }
    };
    var OauthAccessToken = class _OauthAccessToken {
      constructor(externalAccountId, provider, token, publicMetadata = {}, label, scopes, tokenSecret, expiresAt) {
        this.externalAccountId = externalAccountId;
        this.provider = provider;
        this.token = token;
        this.publicMetadata = publicMetadata;
        this.label = label;
        this.scopes = scopes;
        this.tokenSecret = tokenSecret;
        this.expiresAt = expiresAt;
      }
      static fromJSON(data) {
        return new _OauthAccessToken(
          data.external_account_id,
          data.provider,
          data.token,
          data.public_metadata,
          data.label || "",
          data.scopes,
          data.token_secret,
          data.expires_at
        );
      }
    };
    var OAuthApplication = class _OAuthApplication {
      constructor(id, instanceId, name, clientId, isPublic, scopes, redirectUris, authorizeUrl, tokenFetchUrl, userInfoUrl, discoveryUrl, tokenIntrospectionUrl, createdAt, updatedAt, clientSecret) {
        this.id = id;
        this.instanceId = instanceId;
        this.name = name;
        this.clientId = clientId;
        this.isPublic = isPublic;
        this.scopes = scopes;
        this.redirectUris = redirectUris;
        this.authorizeUrl = authorizeUrl;
        this.tokenFetchUrl = tokenFetchUrl;
        this.userInfoUrl = userInfoUrl;
        this.discoveryUrl = discoveryUrl;
        this.tokenIntrospectionUrl = tokenIntrospectionUrl;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.clientSecret = clientSecret;
      }
      static fromJSON(data) {
        return new _OAuthApplication(
          data.id,
          data.instance_id,
          data.name,
          data.client_id,
          data.public,
          data.scopes,
          data.redirect_uris,
          data.authorize_url,
          data.token_fetch_url,
          data.user_info_url,
          data.discovery_url,
          data.token_introspection_url,
          data.created_at,
          data.updated_at,
          data.client_secret
        );
      }
    };
    var Organization = class _Organization {
      constructor(id, name, slug, imageUrl, hasImage, createdAt, updatedAt, publicMetadata = {}, privateMetadata = {}, maxAllowedMemberships, adminDeleteEnabled, membersCount, createdBy) {
        this.id = id;
        this.name = name;
        this.slug = slug;
        this.imageUrl = imageUrl;
        this.hasImage = hasImage;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.maxAllowedMemberships = maxAllowedMemberships;
        this.adminDeleteEnabled = adminDeleteEnabled;
        this.membersCount = membersCount;
        this.createdBy = createdBy;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _Organization(
          data.id,
          data.name,
          data.slug,
          data.image_url || "",
          data.has_image,
          data.created_at,
          data.updated_at,
          data.public_metadata,
          data.private_metadata,
          data.max_allowed_memberships,
          data.admin_delete_enabled,
          data.members_count,
          data.created_by
        );
        res._raw = data;
        return res;
      }
    };
    var OrganizationInvitation = class _OrganizationInvitation {
      constructor(id, emailAddress, role, roleName, organizationId, createdAt, updatedAt, expiresAt, url, status, publicMetadata = {}, privateMetadata = {}, publicOrganizationData) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.role = role;
        this.roleName = roleName;
        this.organizationId = organizationId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.expiresAt = expiresAt;
        this.url = url;
        this.status = status;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.publicOrganizationData = publicOrganizationData;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _OrganizationInvitation(
          data.id,
          data.email_address,
          data.role,
          data.role_name,
          data.organization_id,
          data.created_at,
          data.updated_at,
          data.expires_at,
          data.url,
          data.status,
          data.public_metadata,
          data.private_metadata,
          data.public_organization_data
        );
        res._raw = data;
        return res;
      }
    };
    var OrganizationMembership = class _OrganizationMembership {
      constructor(id, role, permissions, publicMetadata = {}, privateMetadata = {}, createdAt, updatedAt, organization, publicUserData) {
        this.id = id;
        this.role = role;
        this.permissions = permissions;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.organization = organization;
        this.publicUserData = publicUserData;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _OrganizationMembership(
          data.id,
          data.role,
          data.permissions,
          data.public_metadata,
          data.private_metadata,
          data.created_at,
          data.updated_at,
          Organization.fromJSON(data.organization),
          OrganizationMembershipPublicUserData.fromJSON(data.public_user_data)
        );
        res._raw = data;
        return res;
      }
    };
    var OrganizationMembershipPublicUserData = class _OrganizationMembershipPublicUserData {
      constructor(identifier, firstName, lastName, imageUrl, hasImage, userId) {
        this.identifier = identifier;
        this.firstName = firstName;
        this.lastName = lastName;
        this.imageUrl = imageUrl;
        this.hasImage = hasImage;
        this.userId = userId;
      }
      static fromJSON(data) {
        return new _OrganizationMembershipPublicUserData(
          data.identifier,
          data.first_name,
          data.last_name,
          data.image_url,
          data.has_image,
          data.user_id
        );
      }
    };
    var OrganizationSettings = class _OrganizationSettings {
      constructor(enabled, maxAllowedMemberships, maxAllowedRoles, maxAllowedPermissions, creatorRole, adminDeleteEnabled, domainsEnabled, domainsEnrollmentModes, domainsDefaultRole) {
        this.enabled = enabled;
        this.maxAllowedMemberships = maxAllowedMemberships;
        this.maxAllowedRoles = maxAllowedRoles;
        this.maxAllowedPermissions = maxAllowedPermissions;
        this.creatorRole = creatorRole;
        this.adminDeleteEnabled = adminDeleteEnabled;
        this.domainsEnabled = domainsEnabled;
        this.domainsEnrollmentModes = domainsEnrollmentModes;
        this.domainsDefaultRole = domainsDefaultRole;
      }
      static fromJSON(data) {
        return new _OrganizationSettings(
          data.enabled,
          data.max_allowed_memberships,
          data.max_allowed_roles,
          data.max_allowed_permissions,
          data.creator_role,
          data.admin_delete_enabled,
          data.domains_enabled,
          data.domains_enrollment_modes,
          data.domains_default_role
        );
      }
    };
    var PhoneNumber = class _PhoneNumber {
      constructor(id, phoneNumber, reservedForSecondFactor, defaultSecondFactor, verification, linkedTo) {
        this.id = id;
        this.phoneNumber = phoneNumber;
        this.reservedForSecondFactor = reservedForSecondFactor;
        this.defaultSecondFactor = defaultSecondFactor;
        this.verification = verification;
        this.linkedTo = linkedTo;
      }
      static fromJSON(data) {
        return new _PhoneNumber(
          data.id,
          data.phone_number,
          data.reserved_for_second_factor,
          data.default_second_factor,
          data.verification && Verification.fromJSON(data.verification),
          data.linked_to.map((link) => IdentificationLink.fromJSON(link))
        );
      }
    };
    var ProxyCheck = class _ProxyCheck {
      constructor(id, domainId, lastRunAt, proxyUrl, successful, createdAt, updatedAt) {
        this.id = id;
        this.domainId = domainId;
        this.lastRunAt = lastRunAt;
        this.proxyUrl = proxyUrl;
        this.successful = successful;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _ProxyCheck(
          data.id,
          data.domain_id,
          data.last_run_at,
          data.proxy_url,
          data.successful,
          data.created_at,
          data.updated_at
        );
      }
    };
    var RedirectUrl = class _RedirectUrl {
      constructor(id, url, createdAt, updatedAt) {
        this.id = id;
        this.url = url;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _RedirectUrl(data.id, data.url, data.created_at, data.updated_at);
      }
    };
    var SamlConnection = class _SamlConnection {
      constructor(id, name, domain, organizationId, idpEntityId, idpSsoUrl, idpCertificate, idpMetadataUrl, idpMetadata, acsUrl, spEntityId, spMetadataUrl, active, provider, userCount, syncUserAttributes, allowSubdomains, allowIdpInitiated, createdAt, updatedAt, attributeMapping) {
        this.id = id;
        this.name = name;
        this.domain = domain;
        this.organizationId = organizationId;
        this.idpEntityId = idpEntityId;
        this.idpSsoUrl = idpSsoUrl;
        this.idpCertificate = idpCertificate;
        this.idpMetadataUrl = idpMetadataUrl;
        this.idpMetadata = idpMetadata;
        this.acsUrl = acsUrl;
        this.spEntityId = spEntityId;
        this.spMetadataUrl = spMetadataUrl;
        this.active = active;
        this.provider = provider;
        this.userCount = userCount;
        this.syncUserAttributes = syncUserAttributes;
        this.allowSubdomains = allowSubdomains;
        this.allowIdpInitiated = allowIdpInitiated;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.attributeMapping = attributeMapping;
      }
      static fromJSON(data) {
        return new _SamlConnection(
          data.id,
          data.name,
          data.domain,
          data.organization_id,
          data.idp_entity_id,
          data.idp_sso_url,
          data.idp_certificate,
          data.idp_metadata_url,
          data.idp_metadata,
          data.acs_url,
          data.sp_entity_id,
          data.sp_metadata_url,
          data.active,
          data.provider,
          data.user_count,
          data.sync_user_attributes,
          data.allow_subdomains,
          data.allow_idp_initiated,
          data.created_at,
          data.updated_at,
          data.attribute_mapping && AttributeMapping.fromJSON(data.attribute_mapping)
        );
      }
    };
    var SamlAccountConnection = class _SamlAccountConnection {
      constructor(id, name, domain, active, provider, syncUserAttributes, allowSubdomains, allowIdpInitiated, createdAt, updatedAt) {
        this.id = id;
        this.name = name;
        this.domain = domain;
        this.active = active;
        this.provider = provider;
        this.syncUserAttributes = syncUserAttributes;
        this.allowSubdomains = allowSubdomains;
        this.allowIdpInitiated = allowIdpInitiated;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _SamlAccountConnection(
          data.id,
          data.name,
          data.domain,
          data.active,
          data.provider,
          data.sync_user_attributes,
          data.allow_subdomains,
          data.allow_idp_initiated,
          data.created_at,
          data.updated_at
        );
      }
    };
    var AttributeMapping = class _AttributeMapping {
      constructor(userId, emailAddress, firstName, lastName) {
        this.userId = userId;
        this.emailAddress = emailAddress;
        this.firstName = firstName;
        this.lastName = lastName;
      }
      static fromJSON(data) {
        return new _AttributeMapping(data.user_id, data.email_address, data.first_name, data.last_name);
      }
    };
    var SamlAccount = class _SamlAccount {
      constructor(id, provider, providerUserId, active, emailAddress, firstName, lastName, verification, samlConnection) {
        this.id = id;
        this.provider = provider;
        this.providerUserId = providerUserId;
        this.active = active;
        this.emailAddress = emailAddress;
        this.firstName = firstName;
        this.lastName = lastName;
        this.verification = verification;
        this.samlConnection = samlConnection;
      }
      static fromJSON(data) {
        return new _SamlAccount(
          data.id,
          data.provider,
          data.provider_user_id,
          data.active,
          data.email_address,
          data.first_name,
          data.last_name,
          data.verification && Verification.fromJSON(data.verification),
          data.saml_connection && SamlAccountConnection.fromJSON(data.saml_connection)
        );
      }
    };
    var SignInToken = class _SignInToken {
      constructor(id, userId, token, status, url, createdAt, updatedAt) {
        this.id = id;
        this.userId = userId;
        this.token = token;
        this.status = status;
        this.url = url;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
      }
      static fromJSON(data) {
        return new _SignInToken(data.id, data.user_id, data.token, data.status, data.url, data.created_at, data.updated_at);
      }
    };
    var SignUpAttemptVerification = class _SignUpAttemptVerification {
      constructor(nextAction, supportedStrategies) {
        this.nextAction = nextAction;
        this.supportedStrategies = supportedStrategies;
      }
      static fromJSON(data) {
        return new _SignUpAttemptVerification(data.next_action, data.supported_strategies);
      }
    };
    var SignUpAttemptVerifications = class _SignUpAttemptVerifications {
      constructor(emailAddress, phoneNumber, web3Wallet, externalAccount) {
        this.emailAddress = emailAddress;
        this.phoneNumber = phoneNumber;
        this.web3Wallet = web3Wallet;
        this.externalAccount = externalAccount;
      }
      static fromJSON(data) {
        return new _SignUpAttemptVerifications(
          data.email_address && SignUpAttemptVerification.fromJSON(data.email_address),
          data.phone_number && SignUpAttemptVerification.fromJSON(data.phone_number),
          data.web3_wallet && SignUpAttemptVerification.fromJSON(data.web3_wallet),
          data.external_account
        );
      }
    };
    var SignUpAttempt = class _SignUpAttempt {
      constructor(id, status, requiredFields, optionalFields, missingFields, unverifiedFields, verifications, username, emailAddress, phoneNumber, web3Wallet, passwordEnabled, firstName, lastName, customAction, externalId, createdSessionId, createdUserId, abandonAt, legalAcceptedAt, publicMetadata, unsafeMetadata) {
        this.id = id;
        this.status = status;
        this.requiredFields = requiredFields;
        this.optionalFields = optionalFields;
        this.missingFields = missingFields;
        this.unverifiedFields = unverifiedFields;
        this.verifications = verifications;
        this.username = username;
        this.emailAddress = emailAddress;
        this.phoneNumber = phoneNumber;
        this.web3Wallet = web3Wallet;
        this.passwordEnabled = passwordEnabled;
        this.firstName = firstName;
        this.lastName = lastName;
        this.customAction = customAction;
        this.externalId = externalId;
        this.createdSessionId = createdSessionId;
        this.createdUserId = createdUserId;
        this.abandonAt = abandonAt;
        this.legalAcceptedAt = legalAcceptedAt;
        this.publicMetadata = publicMetadata;
        this.unsafeMetadata = unsafeMetadata;
      }
      static fromJSON(data) {
        return new _SignUpAttempt(
          data.id,
          data.status,
          data.required_fields,
          data.optional_fields,
          data.missing_fields,
          data.unverified_fields,
          data.verifications ? SignUpAttemptVerifications.fromJSON(data.verifications) : null,
          data.username,
          data.email_address,
          data.phone_number,
          data.web3_wallet,
          data.password_enabled,
          data.first_name,
          data.last_name,
          data.custom_action,
          data.external_id,
          data.created_session_id,
          data.created_user_id,
          data.abandon_at,
          data.legal_accepted_at,
          data.public_metadata,
          data.unsafe_metadata
        );
      }
    };
    var SMSMessage = class _SMSMessage {
      constructor(id, fromPhoneNumber, toPhoneNumber, message, status, phoneNumberId, data) {
        this.id = id;
        this.fromPhoneNumber = fromPhoneNumber;
        this.toPhoneNumber = toPhoneNumber;
        this.message = message;
        this.status = status;
        this.phoneNumberId = phoneNumberId;
        this.data = data;
      }
      static fromJSON(data) {
        return new _SMSMessage(
          data.id,
          data.from_phone_number,
          data.to_phone_number,
          data.message,
          data.status,
          data.phone_number_id,
          data.data
        );
      }
    };
    var Token = class _Token {
      constructor(jwt) {
        this.jwt = jwt;
      }
      static fromJSON(data) {
        return new _Token(data.jwt);
      }
    };
    var Web3Wallet = class _Web3Wallet {
      constructor(id, web3Wallet, verification) {
        this.id = id;
        this.web3Wallet = web3Wallet;
        this.verification = verification;
      }
      static fromJSON(data) {
        return new _Web3Wallet(data.id, data.web3_wallet, data.verification && Verification.fromJSON(data.verification));
      }
    };
    var User = class _User {
      constructor(id, passwordEnabled, totpEnabled, backupCodeEnabled, twoFactorEnabled, banned, locked, createdAt, updatedAt, imageUrl, hasImage, primaryEmailAddressId, primaryPhoneNumberId, primaryWeb3WalletId, lastSignInAt, externalId, username, firstName, lastName, publicMetadata = {}, privateMetadata = {}, unsafeMetadata = {}, emailAddresses = [], phoneNumbers = [], web3Wallets = [], externalAccounts = [], samlAccounts = [], lastActiveAt, createOrganizationEnabled, createOrganizationsLimit = null, deleteSelfEnabled, legalAcceptedAt) {
        this.id = id;
        this.passwordEnabled = passwordEnabled;
        this.totpEnabled = totpEnabled;
        this.backupCodeEnabled = backupCodeEnabled;
        this.twoFactorEnabled = twoFactorEnabled;
        this.banned = banned;
        this.locked = locked;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.imageUrl = imageUrl;
        this.hasImage = hasImage;
        this.primaryEmailAddressId = primaryEmailAddressId;
        this.primaryPhoneNumberId = primaryPhoneNumberId;
        this.primaryWeb3WalletId = primaryWeb3WalletId;
        this.lastSignInAt = lastSignInAt;
        this.externalId = externalId;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.publicMetadata = publicMetadata;
        this.privateMetadata = privateMetadata;
        this.unsafeMetadata = unsafeMetadata;
        this.emailAddresses = emailAddresses;
        this.phoneNumbers = phoneNumbers;
        this.web3Wallets = web3Wallets;
        this.externalAccounts = externalAccounts;
        this.samlAccounts = samlAccounts;
        this.lastActiveAt = lastActiveAt;
        this.createOrganizationEnabled = createOrganizationEnabled;
        this.createOrganizationsLimit = createOrganizationsLimit;
        this.deleteSelfEnabled = deleteSelfEnabled;
        this.legalAcceptedAt = legalAcceptedAt;
        this._raw = null;
      }
      get raw() {
        return this._raw;
      }
      static fromJSON(data) {
        const res = new _User(
          data.id,
          data.password_enabled,
          data.totp_enabled,
          data.backup_code_enabled,
          data.two_factor_enabled,
          data.banned,
          data.locked,
          data.created_at,
          data.updated_at,
          data.image_url,
          data.has_image,
          data.primary_email_address_id,
          data.primary_phone_number_id,
          data.primary_web3_wallet_id,
          data.last_sign_in_at,
          data.external_id,
          data.username,
          data.first_name,
          data.last_name,
          data.public_metadata,
          data.private_metadata,
          data.unsafe_metadata,
          (data.email_addresses || []).map((x) => EmailAddress.fromJSON(x)),
          (data.phone_numbers || []).map((x) => PhoneNumber.fromJSON(x)),
          (data.web3_wallets || []).map((x) => Web3Wallet.fromJSON(x)),
          (data.external_accounts || []).map((x) => ExternalAccount.fromJSON(x)),
          (data.saml_accounts || []).map((x) => SamlAccount.fromJSON(x)),
          data.last_active_at,
          data.create_organization_enabled,
          data.create_organizations_limit,
          data.delete_self_enabled,
          data.legal_accepted_at
        );
        res._raw = data;
        return res;
      }
      /**
       * The primary email address of the user.
       */
      get primaryEmailAddress() {
        return this.emailAddresses.find(({ id }) => id === this.primaryEmailAddressId) ?? null;
      }
      /**
       * The primary phone number of the user.
       */
      get primaryPhoneNumber() {
        return this.phoneNumbers.find(({ id }) => id === this.primaryPhoneNumberId) ?? null;
      }
      /**
       * The primary web3 wallet of the user.
       */
      get primaryWeb3Wallet() {
        return this.web3Wallets.find(({ id }) => id === this.primaryWeb3WalletId) ?? null;
      }
      /**
       * The full name of the user.
       */
      get fullName() {
        return [this.firstName, this.lastName].join(" ").trim() || null;
      }
    };
    var WaitlistEntry = class _WaitlistEntry {
      constructor(id, emailAddress, status, invitation, createdAt, updatedAt, isLocked) {
        this.id = id;
        this.emailAddress = emailAddress;
        this.status = status;
        this.invitation = invitation;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isLocked = isLocked;
      }
      static fromJSON(data) {
        return new _WaitlistEntry(
          data.id,
          data.email_address,
          data.status,
          data.invitation && Invitation.fromJSON(data.invitation),
          data.created_at,
          data.updated_at,
          data.is_locked
        );
      }
    };
    function deserialize(payload) {
      let data, totalCount;
      if (Array.isArray(payload)) {
        const data2 = payload.map((item) => jsonToObject(item));
        return { data: data2 };
      } else if (isPaginated(payload)) {
        data = payload.data.map((item) => jsonToObject(item));
        totalCount = payload.total_count;
        return { data, totalCount };
      } else {
        return { data: jsonToObject(payload) };
      }
    }
    function isPaginated(payload) {
      if (!payload || typeof payload !== "object" || !("data" in payload)) {
        return false;
      }
      return Array.isArray(payload.data) && payload.data !== void 0;
    }
    function getCount(item) {
      return item.total_count;
    }
    function jsonToObject(item) {
      if (typeof item !== "string" && "object" in item && "deleted" in item) {
        return DeletedObject.fromJSON(item);
      }
      switch (item.object) {
        case ObjectType.AccountlessApplication:
          return AccountlessApplication.fromJSON(item);
        case ObjectType.ActorToken:
          return ActorToken.fromJSON(item);
        case ObjectType.AllowlistIdentifier:
          return AllowlistIdentifier.fromJSON(item);
        case ObjectType.ApiKey:
          return APIKey.fromJSON(item);
        case ObjectType.BlocklistIdentifier:
          return BlocklistIdentifier.fromJSON(item);
        case ObjectType.Client:
          return Client.fromJSON(item);
        case ObjectType.Cookies:
          return Cookies2.fromJSON(item);
        case ObjectType.Domain:
          return Domain.fromJSON(item);
        case ObjectType.EmailAddress:
          return EmailAddress.fromJSON(item);
        case ObjectType.Email:
          return Email.fromJSON(item);
        case ObjectType.IdpOAuthAccessToken:
          return IdPOAuthAccessToken.fromJSON(item);
        case ObjectType.Instance:
          return Instance.fromJSON(item);
        case ObjectType.InstanceRestrictions:
          return InstanceRestrictions.fromJSON(item);
        case ObjectType.InstanceSettings:
          return InstanceSettings.fromJSON(item);
        case ObjectType.Invitation:
          return Invitation.fromJSON(item);
        case ObjectType.JwtTemplate:
          return JwtTemplate.fromJSON(item);
        case ObjectType.MachineToken:
          return MachineToken.fromJSON(item);
        case ObjectType.OauthAccessToken:
          return OauthAccessToken.fromJSON(item);
        case ObjectType.OAuthApplication:
          return OAuthApplication.fromJSON(item);
        case ObjectType.Organization:
          return Organization.fromJSON(item);
        case ObjectType.OrganizationInvitation:
          return OrganizationInvitation.fromJSON(item);
        case ObjectType.OrganizationMembership:
          return OrganizationMembership.fromJSON(item);
        case ObjectType.OrganizationSettings:
          return OrganizationSettings.fromJSON(item);
        case ObjectType.PhoneNumber:
          return PhoneNumber.fromJSON(item);
        case ObjectType.ProxyCheck:
          return ProxyCheck.fromJSON(item);
        case ObjectType.RedirectUrl:
          return RedirectUrl.fromJSON(item);
        case ObjectType.SamlConnection:
          return SamlConnection.fromJSON(item);
        case ObjectType.SignInToken:
          return SignInToken.fromJSON(item);
        case ObjectType.SignUpAttempt:
          return SignUpAttempt.fromJSON(item);
        case ObjectType.Session:
          return Session.fromJSON(item);
        case ObjectType.SmsMessage:
          return SMSMessage.fromJSON(item);
        case ObjectType.Token:
          return Token.fromJSON(item);
        case ObjectType.TotalCount:
          return getCount(item);
        case ObjectType.User:
          return User.fromJSON(item);
        case ObjectType.WaitlistEntry:
          return WaitlistEntry.fromJSON(item);
        default:
          return item;
      }
    }
    function buildRequest(options) {
      const requestFn = async (requestOptions) => {
        var _a;
        const {
          secretKey,
          requireSecretKey = true,
          apiUrl = API_URL,
          apiVersion = API_VERSION,
          userAgent = USER_AGENT
        } = options;
        const { path, method, queryParams, headerParams, bodyParams, formData } = requestOptions;
        if (requireSecretKey) {
          assertValidSecretKey(secretKey);
        }
        const url = joinPaths(apiUrl, apiVersion, path);
        const finalUrl = new URL(url);
        if (queryParams) {
          const snakecasedQueryParams = (0, import_snakecase_keys.default)({ ...queryParams });
          for (const [key, val] of Object.entries(snakecasedQueryParams)) {
            if (val) {
              [val].flat().forEach((v) => finalUrl.searchParams.append(key, v));
            }
          }
        }
        const headers = {
          "Clerk-API-Version": SUPPORTED_BAPI_VERSION,
          "User-Agent": userAgent,
          ...headerParams
        };
        if (secretKey) {
          headers.Authorization = `Bearer ${secretKey}`;
        }
        let res;
        try {
          if (formData) {
            res = await runtime.fetch(finalUrl.href, {
              method,
              headers,
              body: formData
            });
          } else {
            headers["Content-Type"] = "application/json";
            const buildBody = () => {
              const hasBody = method !== "GET" && bodyParams && Object.keys(bodyParams).length > 0;
              if (!hasBody) {
                return null;
              }
              const formatKeys = (object) => (0, import_snakecase_keys.default)(object, { deep: false });
              return {
                body: JSON.stringify(Array.isArray(bodyParams) ? bodyParams.map(formatKeys) : formatKeys(bodyParams))
              };
            };
            res = await runtime.fetch(finalUrl.href, {
              method,
              headers,
              ...buildBody()
            });
          }
          const isJSONResponse = (res == null ? void 0 : res.headers) && ((_a = res.headers) == null ? void 0 : _a.get(constants.Headers.ContentType)) === constants.ContentTypes.Json;
          const responseBody = await (isJSONResponse ? res.json() : res.text());
          if (!res.ok) {
            return {
              data: null,
              errors: parseErrors(responseBody),
              status: res == null ? void 0 : res.status,
              statusText: res == null ? void 0 : res.statusText,
              clerkTraceId: getTraceId(responseBody, res == null ? void 0 : res.headers),
              retryAfter: getRetryAfter(res == null ? void 0 : res.headers)
            };
          }
          return {
            ...deserialize(responseBody),
            errors: null
          };
        } catch (err) {
          if (err instanceof Error) {
            return {
              data: null,
              errors: [
                {
                  code: "unexpected_error",
                  message: err.message || "Unexpected error"
                }
              ],
              clerkTraceId: getTraceId(err, res == null ? void 0 : res.headers)
            };
          }
          return {
            data: null,
            errors: parseErrors(err),
            status: res == null ? void 0 : res.status,
            statusText: res == null ? void 0 : res.statusText,
            clerkTraceId: getTraceId(err, res == null ? void 0 : res.headers),
            retryAfter: getRetryAfter(res == null ? void 0 : res.headers)
          };
        }
      };
      return withLegacyRequestReturn(requestFn);
    }
    function getTraceId(data, headers) {
      if (data && typeof data === "object" && "clerk_trace_id" in data && typeof data.clerk_trace_id === "string") {
        return data.clerk_trace_id;
      }
      const cfRay = headers == null ? void 0 : headers.get("cf-ray");
      return cfRay || "";
    }
    function getRetryAfter(headers) {
      const retryAfter = headers == null ? void 0 : headers.get("Retry-After");
      if (!retryAfter)
        return;
      const value = parseInt(retryAfter, 10);
      if (isNaN(value))
        return;
      return value;
    }
    function parseErrors(data) {
      if (!!data && typeof data === "object" && "errors" in data) {
        const errors = data.errors;
        return errors.length > 0 ? errors.map(import_error2.parseError) : [];
      }
      return [];
    }
    function withLegacyRequestReturn(cb) {
      return async (...args) => {
        const { data, errors, totalCount, status, statusText, clerkTraceId, retryAfter } = await cb(...args);
        if (errors) {
          const error = new import_error2.ClerkAPIResponseError(statusText || "", {
            data: [],
            status,
            clerkTraceId,
            retryAfter
          });
          error.errors = errors;
          throw error;
        }
        if (typeof totalCount !== "undefined") {
          return { data, totalCount };
        }
        return data;
      };
    }
    function createBackendApiClient(options) {
      const request = buildRequest(options);
      return {
        __experimental_accountlessApplications: new AccountlessApplicationAPI(
          buildRequest({ ...options, requireSecretKey: false })
        ),
        actorTokens: new ActorTokenAPI(request),
        allowlistIdentifiers: new AllowlistIdentifierAPI(request),
        betaFeatures: new BetaFeaturesAPI(request),
        blocklistIdentifiers: new BlocklistIdentifierAPI(request),
        clients: new ClientAPI(request),
        domains: new DomainAPI(request),
        emailAddresses: new EmailAddressAPI(request),
        instance: new InstanceAPI(request),
        invitations: new InvitationAPI(request),
        // Using "/" instead of an actual version since they're bapi-proxy endpoints.
        // bapi-proxy connects directly to C1 without URL versioning,
        // while API versioning is handled through the Clerk-API-Version header.
        machineTokens: new MachineTokensApi(
          buildRequest({
            ...options,
            apiVersion: "/"
          })
        ),
        idPOAuthAccessToken: new IdPOAuthAccessTokenApi(
          buildRequest({
            ...options,
            apiVersion: "/"
          })
        ),
        apiKeys: new APIKeysAPI(
          buildRequest({
            ...options,
            apiVersion: "/"
          })
        ),
        jwks: new JwksAPI(request),
        jwtTemplates: new JwtTemplatesApi(request),
        oauthApplications: new OAuthApplicationsApi(request),
        organizations: new OrganizationAPI(request),
        phoneNumbers: new PhoneNumberAPI(request),
        proxyChecks: new ProxyCheckAPI(request),
        redirectUrls: new RedirectUrlAPI(request),
        samlConnections: new SamlConnectionAPI(request),
        sessions: new SessionAPI(request),
        signInTokens: new SignInTokenAPI(request),
        signUps: new SignUpAPI(request),
        testingTokens: new TestingTokenAPI(request),
        users: new UserAPI(request),
        waitlistEntries: new WaitlistEntryAPI(request),
        webhooks: new WebhookAPI(request)
      };
    }
    function withLegacyReturn(cb) {
      return async (...args) => {
        const { data, errors } = await cb(...args);
        if (errors) {
          throw errors[0];
        }
        return data;
      };
    }
    function mergePreDefinedOptions(preDefinedOptions, options) {
      return Object.keys(preDefinedOptions).reduce(
        (obj, key) => {
          return { ...obj, [key]: options[key] || obj[key] };
        },
        { ...preDefinedOptions }
      );
    }
    var TokenVerificationErrorCode = {
      InvalidSecretKey: "clerk_key_invalid"
    };
    var TokenVerificationErrorReason = {
      TokenExpired: "token-expired",
      TokenInvalid: "token-invalid",
      TokenInvalidAlgorithm: "token-invalid-algorithm",
      TokenInvalidAuthorizedParties: "token-invalid-authorized-parties",
      TokenInvalidSignature: "token-invalid-signature",
      TokenNotActiveYet: "token-not-active-yet",
      TokenIatInTheFuture: "token-iat-in-the-future",
      TokenVerificationFailed: "token-verification-failed",
      InvalidSecretKey: "secret-key-invalid",
      LocalJWKMissing: "jwk-local-missing",
      RemoteJWKFailedToLoad: "jwk-remote-failed-to-load",
      RemoteJWKInvalid: "jwk-remote-invalid",
      RemoteJWKMissing: "jwk-remote-missing",
      JWKFailedToResolve: "jwk-failed-to-resolve",
      JWKKidMismatch: "jwk-kid-mismatch"
    };
    var TokenVerificationErrorAction = {
      ContactSupport: "Contact <EMAIL>",
      EnsureClerkJWT: "Make sure that this is a valid Clerk generate JWT.",
      SetClerkJWTKey: "Set the CLERK_JWT_KEY environment variable.",
      SetClerkSecretKey: "Set the CLERK_SECRET_KEY environment variable.",
      EnsureClockSync: "Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."
    };
    var TokenVerificationError = class _TokenVerificationError extends Error {
      constructor({
        action,
        message,
        reason
      }) {
        super(message);
        Object.setPrototypeOf(this, _TokenVerificationError.prototype);
        this.reason = reason;
        this.message = message;
        this.action = action;
      }
      getFullMessage() {
        return `${[this.message, this.action].filter((m) => m).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`;
      }
    };
    var MachineTokenVerificationErrorCode = {
      TokenInvalid: "token-invalid",
      InvalidSecretKey: "secret-key-invalid",
      UnexpectedError: "unexpected-error"
    };
    var MachineTokenVerificationError = class _MachineTokenVerificationError extends Error {
      constructor({ message, code, status }) {
        super(message);
        Object.setPrototypeOf(this, _MachineTokenVerificationError.prototype);
        this.code = code;
        this.status = status;
      }
      getFullMessage() {
        return `${this.message} (code=${this.code}, status=${this.status})`;
      }
    };
    var base64url = {
      parse(string, opts) {
        return parse(string, base64UrlEncoding, opts);
      },
      stringify(data, opts) {
        return stringify(data, base64UrlEncoding, opts);
      }
    };
    var base64UrlEncoding = {
      chars: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",
      bits: 6
    };
    function parse(string, encoding, opts = {}) {
      if (!encoding.codes) {
        encoding.codes = {};
        for (let i = 0; i < encoding.chars.length; ++i) {
          encoding.codes[encoding.chars[i]] = i;
        }
      }
      if (!opts.loose && string.length * encoding.bits & 7) {
        throw new SyntaxError("Invalid padding");
      }
      let end = string.length;
      while (string[end - 1] === "=") {
        --end;
        if (!opts.loose && !((string.length - end) * encoding.bits & 7)) {
          throw new SyntaxError("Invalid padding");
        }
      }
      const out = new (opts.out ?? Uint8Array)(end * encoding.bits / 8 | 0);
      let bits = 0;
      let buffer = 0;
      let written = 0;
      for (let i = 0; i < end; ++i) {
        const value = encoding.codes[string[i]];
        if (value === void 0) {
          throw new SyntaxError("Invalid character " + string[i]);
        }
        buffer = buffer << encoding.bits | value;
        bits += encoding.bits;
        if (bits >= 8) {
          bits -= 8;
          out[written++] = 255 & buffer >> bits;
        }
      }
      if (bits >= encoding.bits || 255 & buffer << 8 - bits) {
        throw new SyntaxError("Unexpected end of data");
      }
      return out;
    }
    function stringify(data, encoding, opts = {}) {
      const { pad = true } = opts;
      const mask = (1 << encoding.bits) - 1;
      let out = "";
      let bits = 0;
      let buffer = 0;
      for (let i = 0; i < data.length; ++i) {
        buffer = buffer << 8 | 255 & data[i];
        bits += 8;
        while (bits > encoding.bits) {
          bits -= encoding.bits;
          out += encoding.chars[mask & buffer >> bits];
        }
      }
      if (bits) {
        out += encoding.chars[mask & buffer << encoding.bits - bits];
      }
      if (pad) {
        while (out.length * encoding.bits & 7) {
          out += "=";
        }
      }
      return out;
    }
    var algToHash = {
      RS256: "SHA-256",
      RS384: "SHA-384",
      RS512: "SHA-512"
    };
    var RSA_ALGORITHM_NAME = "RSASSA-PKCS1-v1_5";
    var jwksAlgToCryptoAlg = {
      RS256: RSA_ALGORITHM_NAME,
      RS384: RSA_ALGORITHM_NAME,
      RS512: RSA_ALGORITHM_NAME
    };
    var algs = Object.keys(algToHash);
    function getCryptoAlgorithm(algorithmName) {
      const hash = algToHash[algorithmName];
      const name = jwksAlgToCryptoAlg[algorithmName];
      if (!hash || !name) {
        throw new Error(`Unsupported algorithm ${algorithmName}, expected one of ${algs.join(",")}.`);
      }
      return {
        hash: { name: algToHash[algorithmName] },
        name: jwksAlgToCryptoAlg[algorithmName]
      };
    }
    var isArrayString = (s) => {
      return Array.isArray(s) && s.length > 0 && s.every((a) => typeof a === "string");
    };
    var assertAudienceClaim = (aud, audience) => {
      const audienceList = [audience].flat().filter((a) => !!a);
      const audList = [aud].flat().filter((a) => !!a);
      const shouldVerifyAudience = audienceList.length > 0 && audList.length > 0;
      if (!shouldVerifyAudience) {
        return;
      }
      if (typeof aud === "string") {
        if (!audienceList.includes(aud)) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.EnsureClerkJWT,
            reason: TokenVerificationErrorReason.TokenVerificationFailed,
            message: `Invalid JWT audience claim (aud) ${JSON.stringify(aud)}. Is not included in "${JSON.stringify(
              audienceList
            )}".`
          });
        }
      } else if (isArrayString(aud)) {
        if (!aud.some((a) => audienceList.includes(a))) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.EnsureClerkJWT,
            reason: TokenVerificationErrorReason.TokenVerificationFailed,
            message: `Invalid JWT audience claim array (aud) ${JSON.stringify(aud)}. Is not included in "${JSON.stringify(
              audienceList
            )}".`
          });
        }
      }
    };
    var assertHeaderType = (typ) => {
      if (typeof typ === "undefined") {
        return;
      }
      if (typ !== "JWT") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenInvalid,
          message: `Invalid JWT type ${JSON.stringify(typ)}. Expected "JWT".`
        });
      }
    };
    var assertHeaderAlgorithm = (alg) => {
      if (!algs.includes(alg)) {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenInvalidAlgorithm,
          message: `Invalid JWT algorithm ${JSON.stringify(alg)}. Supported: ${algs}.`
        });
      }
    };
    var assertSubClaim = (sub) => {
      if (typeof sub !== "string") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Subject claim (sub) is required and must be a string. Received ${JSON.stringify(sub)}.`
        });
      }
    };
    var assertAuthorizedPartiesClaim = (azp, authorizedParties) => {
      if (!azp || !authorizedParties || authorizedParties.length === 0) {
        return;
      }
      if (!authorizedParties.includes(azp)) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenInvalidAuthorizedParties,
          message: `Invalid JWT Authorized party claim (azp) ${JSON.stringify(azp)}. Expected "${authorizedParties}".`
        });
      }
    };
    var assertExpirationClaim = (exp, clockSkewInMs) => {
      if (typeof exp !== "number") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Invalid JWT expiry date claim (exp) ${JSON.stringify(exp)}. Expected number.`
        });
      }
      const currentDate = new Date(Date.now());
      const expiryDate = /* @__PURE__ */ new Date(0);
      expiryDate.setUTCSeconds(exp);
      const expired = expiryDate.getTime() <= currentDate.getTime() - clockSkewInMs;
      if (expired) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenExpired,
          message: `JWT is expired. Expiry date: ${expiryDate.toUTCString()}, Current date: ${currentDate.toUTCString()}.`
        });
      }
    };
    var assertActivationClaim = (nbf, clockSkewInMs) => {
      if (typeof nbf === "undefined") {
        return;
      }
      if (typeof nbf !== "number") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Invalid JWT not before date claim (nbf) ${JSON.stringify(nbf)}. Expected number.`
        });
      }
      const currentDate = new Date(Date.now());
      const notBeforeDate = /* @__PURE__ */ new Date(0);
      notBeforeDate.setUTCSeconds(nbf);
      const early = notBeforeDate.getTime() > currentDate.getTime() + clockSkewInMs;
      if (early) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenNotActiveYet,
          message: `JWT cannot be used prior to not before date claim (nbf). Not before date: ${notBeforeDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`
        });
      }
    };
    var assertIssuedAtClaim = (iat, clockSkewInMs) => {
      if (typeof iat === "undefined") {
        return;
      }
      if (typeof iat !== "number") {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.EnsureClerkJWT,
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Invalid JWT issued at date claim (iat) ${JSON.stringify(iat)}. Expected number.`
        });
      }
      const currentDate = new Date(Date.now());
      const issuedAtDate = /* @__PURE__ */ new Date(0);
      issuedAtDate.setUTCSeconds(iat);
      const postIssued = issuedAtDate.getTime() > currentDate.getTime() + clockSkewInMs;
      if (postIssued) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenIatInTheFuture,
          message: `JWT issued at date claim (iat) is in the future. Issued at date: ${issuedAtDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`
        });
      }
    };
    var import_isomorphicAtob = require_isomorphicAtob();
    function pemToBuffer(secret) {
      const trimmed = secret.replace(/-----BEGIN.*?-----/g, "").replace(/-----END.*?-----/g, "").replace(/\s/g, "");
      const decoded = (0, import_isomorphicAtob.isomorphicAtob)(trimmed);
      const buffer = new ArrayBuffer(decoded.length);
      const bufView = new Uint8Array(buffer);
      for (let i = 0, strLen = decoded.length; i < strLen; i++) {
        bufView[i] = decoded.charCodeAt(i);
      }
      return bufView;
    }
    function importKey(key, algorithm, keyUsage) {
      if (typeof key === "object") {
        return runtime.crypto.subtle.importKey("jwk", key, algorithm, false, [keyUsage]);
      }
      const keyData = pemToBuffer(key);
      const format = keyUsage === "sign" ? "pkcs8" : "spki";
      return runtime.crypto.subtle.importKey(format, keyData, algorithm, false, [keyUsage]);
    }
    var DEFAULT_CLOCK_SKEW_IN_MS = 5 * 1e3;
    async function hasValidSignature(jwt, key) {
      const { header, signature, raw } = jwt;
      const encoder = new TextEncoder();
      const data = encoder.encode([raw.header, raw.payload].join("."));
      const algorithm = getCryptoAlgorithm(header.alg);
      try {
        const cryptoKey = await importKey(key, algorithm, "verify");
        const verified = await runtime.crypto.subtle.verify(algorithm.name, cryptoKey, signature, data);
        return { data: verified };
      } catch (error) {
        return {
          errors: [
            new TokenVerificationError({
              reason: TokenVerificationErrorReason.TokenInvalidSignature,
              message: error == null ? void 0 : error.message
            })
          ]
        };
      }
    }
    function decodeJwt(token) {
      const tokenParts = (token || "").toString().split(".");
      if (tokenParts.length !== 3) {
        return {
          errors: [
            new TokenVerificationError({
              reason: TokenVerificationErrorReason.TokenInvalid,
              message: `Invalid JWT form. A JWT consists of three parts separated by dots.`
            })
          ]
        };
      }
      const [rawHeader, rawPayload, rawSignature] = tokenParts;
      const decoder = new TextDecoder();
      const header = JSON.parse(decoder.decode(base64url.parse(rawHeader, { loose: true })));
      const payload = JSON.parse(decoder.decode(base64url.parse(rawPayload, { loose: true })));
      const signature = base64url.parse(rawSignature, { loose: true });
      const data = {
        header,
        payload,
        signature,
        raw: {
          header: rawHeader,
          payload: rawPayload,
          signature: rawSignature,
          text: token
        }
      };
      return { data };
    }
    async function verifyJwt(token, options) {
      const { audience, authorizedParties, clockSkewInMs, key } = options;
      const clockSkew = clockSkewInMs || DEFAULT_CLOCK_SKEW_IN_MS;
      const { data: decoded, errors } = decodeJwt(token);
      if (errors) {
        return { errors };
      }
      const { header, payload } = decoded;
      try {
        const { typ, alg } = header;
        assertHeaderType(typ);
        assertHeaderAlgorithm(alg);
        const { azp, sub, aud, iat, exp, nbf } = payload;
        assertSubClaim(sub);
        assertAudienceClaim([aud], [audience]);
        assertAuthorizedPartiesClaim(azp, authorizedParties);
        assertExpirationClaim(exp, clockSkew);
        assertActivationClaim(nbf, clockSkew);
        assertIssuedAtClaim(iat, clockSkew);
      } catch (err) {
        return { errors: [err] };
      }
      const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);
      if (signatureErrors) {
        return {
          errors: [
            new TokenVerificationError({
              action: TokenVerificationErrorAction.EnsureClerkJWT,
              reason: TokenVerificationErrorReason.TokenVerificationFailed,
              message: `Error verifying JWT signature. ${signatureErrors[0]}`
            })
          ]
        };
      }
      if (!signatureValid) {
        return {
          errors: [
            new TokenVerificationError({
              reason: TokenVerificationErrorReason.TokenInvalidSignature,
              message: "JWT signature is invalid."
            })
          ]
        };
      }
      return { data: payload };
    }
    var AuthenticateContext = class {
      constructor(cookieSuffix, clerkRequest, options) {
        this.cookieSuffix = cookieSuffix;
        this.clerkRequest = clerkRequest;
        this.originalFrontendApi = "";
        this.initPublishableKeyValues(options);
        this.initHeaderValues();
        this.initCookieValues();
        this.initHandshakeValues();
        Object.assign(this, options);
        this.clerkUrl = this.clerkRequest.clerkUrl;
      }
      /**
       * Retrieves the session token from either the cookie or the header.
       *
       * @returns {string | undefined} The session token if available, otherwise undefined.
       */
      get sessionToken() {
        return this.sessionTokenInCookie || this.tokenInHeader;
      }
      usesSuffixedCookies() {
        const suffixedClientUat = this.getSuffixedCookie(constants.Cookies.ClientUat);
        const clientUat = this.getCookie(constants.Cookies.ClientUat);
        const suffixedSession = this.getSuffixedCookie(constants.Cookies.Session) || "";
        const session = this.getCookie(constants.Cookies.Session) || "";
        if (session && !this.tokenHasIssuer(session)) {
          return false;
        }
        if (session && !this.tokenBelongsToInstance(session)) {
          return true;
        }
        if (!suffixedClientUat && !suffixedSession) {
          return false;
        }
        const { data: sessionData } = decodeJwt(session);
        const sessionIat = (sessionData == null ? void 0 : sessionData.payload.iat) || 0;
        const { data: suffixedSessionData } = decodeJwt(suffixedSession);
        const suffixedSessionIat = (suffixedSessionData == null ? void 0 : suffixedSessionData.payload.iat) || 0;
        if (suffixedClientUat !== "0" && clientUat !== "0" && sessionIat > suffixedSessionIat) {
          return false;
        }
        if (suffixedClientUat === "0" && clientUat !== "0") {
          return false;
        }
        if (this.instanceType !== "production") {
          const isSuffixedSessionExpired = this.sessionExpired(suffixedSessionData);
          if (suffixedClientUat !== "0" && clientUat === "0" && isSuffixedSessionExpired) {
            return false;
          }
        }
        if (!suffixedClientUat && suffixedSession) {
          return false;
        }
        return true;
      }
      initPublishableKeyValues(options) {
        assertValidPublishableKey(options.publishableKey);
        this.publishableKey = options.publishableKey;
        const originalPk = (0, import_keys.parsePublishableKey)(this.publishableKey, {
          fatal: true,
          domain: options.domain,
          isSatellite: options.isSatellite
        });
        this.originalFrontendApi = originalPk.frontendApi;
        const pk = (0, import_keys.parsePublishableKey)(this.publishableKey, {
          fatal: true,
          proxyUrl: options.proxyUrl,
          domain: options.domain,
          isSatellite: options.isSatellite
        });
        this.instanceType = pk.instanceType;
        this.frontendApi = pk.frontendApi;
      }
      initHeaderValues() {
        this.tokenInHeader = this.parseAuthorizationHeader(this.getHeader(constants.Headers.Authorization));
        this.origin = this.getHeader(constants.Headers.Origin);
        this.host = this.getHeader(constants.Headers.Host);
        this.forwardedHost = this.getHeader(constants.Headers.ForwardedHost);
        this.forwardedProto = this.getHeader(constants.Headers.CloudFrontForwardedProto) || this.getHeader(constants.Headers.ForwardedProto);
        this.referrer = this.getHeader(constants.Headers.Referrer);
        this.userAgent = this.getHeader(constants.Headers.UserAgent);
        this.secFetchDest = this.getHeader(constants.Headers.SecFetchDest);
        this.accept = this.getHeader(constants.Headers.Accept);
      }
      initCookieValues() {
        this.sessionTokenInCookie = this.getSuffixedOrUnSuffixedCookie(constants.Cookies.Session);
        this.refreshTokenInCookie = this.getSuffixedCookie(constants.Cookies.Refresh);
        this.clientUat = Number.parseInt(this.getSuffixedOrUnSuffixedCookie(constants.Cookies.ClientUat) || "") || 0;
      }
      initHandshakeValues() {
        this.devBrowserToken = this.getQueryParam(constants.QueryParameters.DevBrowser) || this.getSuffixedOrUnSuffixedCookie(constants.Cookies.DevBrowser);
        this.handshakeToken = this.getQueryParam(constants.QueryParameters.Handshake) || this.getCookie(constants.Cookies.Handshake);
        this.handshakeRedirectLoopCounter = Number(this.getCookie(constants.Cookies.RedirectCount)) || 0;
        this.handshakeNonce = this.getQueryParam(constants.QueryParameters.HandshakeNonce) || this.getCookie(constants.Cookies.HandshakeNonce);
      }
      getQueryParam(name) {
        return this.clerkRequest.clerkUrl.searchParams.get(name);
      }
      getHeader(name) {
        return this.clerkRequest.headers.get(name) || void 0;
      }
      getCookie(name) {
        return this.clerkRequest.cookies.get(name) || void 0;
      }
      getSuffixedCookie(name) {
        return this.getCookie((0, import_keys.getSuffixedCookieName)(name, this.cookieSuffix)) || void 0;
      }
      getSuffixedOrUnSuffixedCookie(cookieName) {
        if (this.usesSuffixedCookies()) {
          return this.getSuffixedCookie(cookieName);
        }
        return this.getCookie(cookieName);
      }
      parseAuthorizationHeader(authorizationHeader) {
        if (!authorizationHeader) {
          return void 0;
        }
        const [scheme, token] = authorizationHeader.split(" ", 2);
        if (!token) {
          return scheme;
        }
        if (scheme === "Bearer") {
          return token;
        }
        return void 0;
      }
      tokenHasIssuer(token) {
        const { data, errors } = decodeJwt(token);
        if (errors) {
          return false;
        }
        return !!data.payload.iss;
      }
      tokenBelongsToInstance(token) {
        if (!token) {
          return false;
        }
        const { data, errors } = decodeJwt(token);
        if (errors) {
          return false;
        }
        const tokenIssuer = data.payload.iss.replace(/https?:\/\//gi, "");
        return this.originalFrontendApi === tokenIssuer;
      }
      sessionExpired(jwt) {
        return !!jwt && (jwt == null ? void 0 : jwt.payload.exp) <= Date.now() / 1e3 >> 0;
      }
    };
    var createAuthenticateContext = async (clerkRequest, options) => {
      const cookieSuffix = options.publishableKey ? await (0, import_keys.getCookieSuffix)(options.publishableKey, runtime.crypto.subtle) : "";
      return new AuthenticateContext(cookieSuffix, clerkRequest, options);
    };
    var import_authorization = require_authorization();
    var import_jwtPayloadParser = require_jwtPayloadParser();
    var import_buildAccountsBaseUrl = require_buildAccountsBaseUrl();
    var TokenType = {
      SessionToken: "session_token",
      ApiKey: "api_key",
      MachineToken: "machine_token",
      OAuthToken: "oauth_token"
    };
    var import_cookie = require_dist();
    var ClerkUrl = class extends URL {
      isCrossOrigin(other) {
        return this.origin !== new URL(other.toString()).origin;
      }
    };
    var createClerkUrl = (...args) => {
      return new ClerkUrl(...args);
    };
    var ClerkRequest = class extends Request {
      constructor(input, init) {
        const url = typeof input !== "string" && "url" in input ? input.url : String(input);
        super(url, init || typeof input === "string" ? void 0 : input);
        this.clerkUrl = this.deriveUrlFromHeaders(this);
        this.cookies = this.parseCookies(this);
      }
      toJSON() {
        return {
          url: this.clerkUrl.href,
          method: this.method,
          headers: JSON.stringify(Object.fromEntries(this.headers)),
          clerkUrl: this.clerkUrl.toString(),
          cookies: JSON.stringify(Object.fromEntries(this.cookies))
        };
      }
      /**
       * Used to fix request.url using the x-forwarded-* headers
       * TODO add detailed description of the issues this solves
       */
      deriveUrlFromHeaders(req) {
        const initialUrl = new URL(req.url);
        const forwardedProto = req.headers.get(constants.Headers.ForwardedProto);
        const forwardedHost = req.headers.get(constants.Headers.ForwardedHost);
        const host = req.headers.get(constants.Headers.Host);
        const protocol = initialUrl.protocol;
        const resolvedHost = this.getFirstValueFromHeader(forwardedHost) ?? host;
        const resolvedProtocol = this.getFirstValueFromHeader(forwardedProto) ?? (protocol == null ? void 0 : protocol.replace(/[:/]/, ""));
        const origin = resolvedHost && resolvedProtocol ? `${resolvedProtocol}://${resolvedHost}` : initialUrl.origin;
        if (origin === initialUrl.origin) {
          return createClerkUrl(initialUrl);
        }
        return createClerkUrl(initialUrl.pathname + initialUrl.search, origin);
      }
      getFirstValueFromHeader(value) {
        return value == null ? void 0 : value.split(",")[0];
      }
      parseCookies(req) {
        const cookiesRecord = (0, import_cookie.parse)(this.decodeCookieValue(req.headers.get("cookie") || ""));
        return new Map(Object.entries(cookiesRecord));
      }
      decodeCookieValue(str) {
        return str ? str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent) : str;
      }
    };
    var createClerkRequest = (...args) => {
      return args[0] instanceof ClerkRequest ? args[0] : new ClerkRequest(...args);
    };
    var import_authorization_errors = require_authorization_errors();
    var import_error3 = require_error();
    var cache = {};
    var lastUpdatedAt = 0;
    function getFromCache(kid) {
      return cache[kid];
    }
    function getCacheValues() {
      return Object.values(cache);
    }
    function setInCache(jwk, shouldExpire = true) {
      cache[jwk.kid] = jwk;
      lastUpdatedAt = shouldExpire ? Date.now() : -1;
    }
    var LocalJwkKid = "local";
    var PEM_HEADER = "-----BEGIN PUBLIC KEY-----";
    var PEM_TRAILER = "-----END PUBLIC KEY-----";
    var RSA_PREFIX = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA";
    var RSA_SUFFIX = "IDAQAB";
    function loadClerkJWKFromLocal(localKey) {
      if (!getFromCache(LocalJwkKid)) {
        if (!localKey) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.SetClerkJWTKey,
            message: "Missing local JWK.",
            reason: TokenVerificationErrorReason.LocalJWKMissing
          });
        }
        const modulus = localKey.replace(/\r\n|\n|\r/g, "").replace(PEM_HEADER, "").replace(PEM_TRAILER, "").replace(RSA_PREFIX, "").replace(RSA_SUFFIX, "").replace(/\+/g, "-").replace(/\//g, "_");
        setInCache(
          {
            kid: "local",
            kty: "RSA",
            alg: "RS256",
            n: modulus,
            e: "AQAB"
          },
          false
          // local key never expires in cache
        );
      }
      return getFromCache(LocalJwkKid);
    }
    async function loadClerkJWKFromRemote({
      secretKey,
      apiUrl = API_URL,
      apiVersion = API_VERSION,
      kid,
      skipJwksCache
    }) {
      if (skipJwksCache || cacheHasExpired() || !getFromCache(kid)) {
        if (!secretKey) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.ContactSupport,
            message: "Failed to load JWKS from Clerk Backend or Frontend API.",
            reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
          });
        }
        const fetcher = () => fetchJWKSFromBAPI(apiUrl, secretKey, apiVersion);
        const { keys } = await (0, import_retry.retry)(fetcher);
        if (!keys || !keys.length) {
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.ContactSupport,
            message: "The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",
            reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
          });
        }
        keys.forEach((key) => setInCache(key));
      }
      const jwk = getFromCache(kid);
      if (!jwk) {
        const cacheValues = getCacheValues();
        const jwkKeys = cacheValues.map((jwk2) => jwk2.kid).sort().join(", ");
        throw new TokenVerificationError({
          action: `Go to your Dashboard and validate your secret and public keys are correct. ${TokenVerificationErrorAction.ContactSupport} if the issue persists.`,
          message: `Unable to find a signing key in JWKS that matches the kid='${kid}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${jwkKeys}`,
          reason: TokenVerificationErrorReason.JWKKidMismatch
        });
      }
      return jwk;
    }
    async function fetchJWKSFromBAPI(apiUrl, key, apiVersion) {
      if (!key) {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.SetClerkSecretKey,
          message: "Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",
          reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
        });
      }
      const url = new URL(apiUrl);
      url.pathname = joinPaths(url.pathname, apiVersion, "/jwks");
      const response = await runtime.fetch(url.href, {
        headers: {
          Authorization: `Bearer ${key}`,
          "Clerk-API-Version": SUPPORTED_BAPI_VERSION,
          "Content-Type": "application/json",
          "User-Agent": USER_AGENT
        }
      });
      if (!response.ok) {
        const json = await response.json();
        const invalidSecretKeyError = getErrorObjectByCode(json == null ? void 0 : json.errors, TokenVerificationErrorCode.InvalidSecretKey);
        if (invalidSecretKeyError) {
          const reason = TokenVerificationErrorReason.InvalidSecretKey;
          throw new TokenVerificationError({
            action: TokenVerificationErrorAction.ContactSupport,
            message: invalidSecretKeyError.message,
            reason
          });
        }
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.ContactSupport,
          message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,
          reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad
        });
      }
      return response.json();
    }
    function cacheHasExpired() {
      if (lastUpdatedAt === -1) {
        return false;
      }
      const isExpired = Date.now() - lastUpdatedAt >= MAX_CACHE_LAST_UPDATED_AT_SECONDS * 1e3;
      if (isExpired) {
        cache = {};
      }
      return isExpired;
    }
    var getErrorObjectByCode = (errors, code) => {
      if (!errors) {
        return null;
      }
      return errors.find((err) => err.code === code);
    };
    var M2M_TOKEN_PREFIX = "mt_";
    var OAUTH_TOKEN_PREFIX = "oat_";
    var API_KEY_PREFIX = "ak_";
    var MACHINE_TOKEN_PREFIXES = [M2M_TOKEN_PREFIX, OAUTH_TOKEN_PREFIX, API_KEY_PREFIX];
    function isMachineTokenByPrefix(token) {
      return MACHINE_TOKEN_PREFIXES.some((prefix) => token.startsWith(prefix));
    }
    function getMachineTokenType(token) {
      if (token.startsWith(M2M_TOKEN_PREFIX)) {
        return TokenType.MachineToken;
      }
      if (token.startsWith(OAUTH_TOKEN_PREFIX)) {
        return TokenType.OAuthToken;
      }
      if (token.startsWith(API_KEY_PREFIX)) {
        return TokenType.ApiKey;
      }
      throw new Error("Unknown machine token type");
    }
    var isTokenTypeAccepted = (tokenType, acceptsToken) => {
      if (!tokenType) {
        return false;
      }
      if (acceptsToken === "any") {
        return true;
      }
      const tokenTypes = Array.isArray(acceptsToken) ? acceptsToken : [acceptsToken];
      return tokenTypes.includes(tokenType);
    };
    async function verifyToken(token, options) {
      const { data: decodedResult, errors } = decodeJwt(token);
      if (errors) {
        return { errors };
      }
      const { header } = decodedResult;
      const { kid } = header;
      try {
        let key;
        if (options.jwtKey) {
          key = loadClerkJWKFromLocal(options.jwtKey);
        } else if (options.secretKey) {
          key = await loadClerkJWKFromRemote({ ...options, kid });
        } else {
          return {
            errors: [
              new TokenVerificationError({
                action: TokenVerificationErrorAction.SetClerkJWTKey,
                message: "Failed to resolve JWK during verification.",
                reason: TokenVerificationErrorReason.JWKFailedToResolve
              })
            ]
          };
        }
        return await verifyJwt(token, { ...options, key });
      } catch (error) {
        return { errors: [error] };
      }
    }
    function handleClerkAPIError(tokenType, err, notFoundMessage) {
      var _a;
      if ((0, import_error3.isClerkAPIResponseError)(err)) {
        let code;
        let message;
        switch (err.status) {
          case 401:
            code = MachineTokenVerificationErrorCode.InvalidSecretKey;
            message = ((_a = err.errors[0]) == null ? void 0 : _a.message) || "Invalid secret key";
            break;
          case 404:
            code = MachineTokenVerificationErrorCode.TokenInvalid;
            message = notFoundMessage;
            break;
          default:
            code = MachineTokenVerificationErrorCode.UnexpectedError;
            message = "Unexpected error";
        }
        return {
          data: void 0,
          tokenType,
          errors: [
            new MachineTokenVerificationError({
              message,
              code,
              status: err.status
            })
          ]
        };
      }
      return {
        data: void 0,
        tokenType,
        errors: [
          new MachineTokenVerificationError({
            message: "Unexpected error",
            code: MachineTokenVerificationErrorCode.UnexpectedError,
            status: err.status
          })
        ]
      };
    }
    async function verifyMachineToken(secret, options) {
      try {
        const client = createBackendApiClient(options);
        const verifiedToken = await client.machineTokens.verifySecret(secret);
        return { data: verifiedToken, tokenType: TokenType.MachineToken, errors: void 0 };
      } catch (err) {
        return handleClerkAPIError(TokenType.MachineToken, err, "Machine token not found");
      }
    }
    async function verifyOAuthToken(accessToken, options) {
      try {
        const client = createBackendApiClient(options);
        const verifiedToken = await client.idPOAuthAccessToken.verifyAccessToken(accessToken);
        return { data: verifiedToken, tokenType: TokenType.OAuthToken, errors: void 0 };
      } catch (err) {
        return handleClerkAPIError(TokenType.OAuthToken, err, "OAuth token not found");
      }
    }
    async function verifyAPIKey(secret, options) {
      try {
        const client = createBackendApiClient(options);
        const verifiedToken = await client.apiKeys.verifySecret(secret);
        return { data: verifiedToken, tokenType: TokenType.ApiKey, errors: void 0 };
      } catch (err) {
        return handleClerkAPIError(TokenType.ApiKey, err, "API key not found");
      }
    }
    async function verifyMachineAuthToken(token, options) {
      if (token.startsWith(M2M_TOKEN_PREFIX)) {
        return verifyMachineToken(token, options);
      }
      if (token.startsWith(OAUTH_TOKEN_PREFIX)) {
        return verifyOAuthToken(token, options);
      }
      if (token.startsWith(API_KEY_PREFIX)) {
        return verifyAPIKey(token, options);
      }
      throw new Error("Unknown machine token type");
    }
    var createDebug = (data) => {
      return () => {
        const res = { ...data };
        res.secretKey = (res.secretKey || "").substring(0, 7);
        res.jwtKey = (res.jwtKey || "").substring(0, 7);
        return { ...res };
      };
    };
    function signedInAuthObject(authenticateContext, sessionToken, sessionClaims) {
      const { actor, sessionId, sessionStatus, userId, orgId, orgRole, orgSlug, orgPermissions, factorVerificationAge } = (0, import_jwtPayloadParser.__experimental_JWTPayloadToAuthObjectProperties)(sessionClaims);
      const apiClient = createBackendApiClient(authenticateContext);
      const getToken = createGetToken({
        sessionId,
        sessionToken,
        fetcher: async (sessionId2, template, expiresInSeconds) => (await apiClient.sessions.getToken(sessionId2, template || "", expiresInSeconds)).jwt
      });
      return {
        tokenType: TokenType.SessionToken,
        actor,
        sessionClaims,
        sessionId,
        sessionStatus,
        userId,
        orgId,
        orgRole,
        orgSlug,
        orgPermissions,
        factorVerificationAge,
        getToken,
        has: (0, import_authorization.createCheckAuthorization)({
          orgId,
          orgRole,
          orgPermissions,
          userId,
          factorVerificationAge,
          features: sessionClaims.fea || "",
          plans: sessionClaims.pla || ""
        }),
        debug: createDebug({ ...authenticateContext, sessionToken }),
        isAuthenticated: true
      };
    }
    function signedOutAuthObject(debugData, initialSessionStatus) {
      return {
        tokenType: TokenType.SessionToken,
        sessionClaims: null,
        sessionId: null,
        sessionStatus: initialSessionStatus ?? null,
        userId: null,
        actor: null,
        orgId: null,
        orgRole: null,
        orgSlug: null,
        orgPermissions: null,
        factorVerificationAge: null,
        getToken: () => Promise.resolve(null),
        has: () => false,
        debug: createDebug(debugData),
        isAuthenticated: false
      };
    }
    function authenticatedMachineObject(tokenType, token, verificationResult, debugData) {
      const baseObject = {
        id: verificationResult.id,
        subject: verificationResult.subject,
        getToken: () => Promise.resolve(token),
        has: () => false,
        debug: createDebug(debugData),
        isAuthenticated: true
      };
      switch (tokenType) {
        case TokenType.ApiKey: {
          const result = verificationResult;
          return {
            ...baseObject,
            tokenType,
            name: result.name,
            claims: result.claims,
            scopes: result.scopes,
            userId: result.subject.startsWith("user_") ? result.subject : null,
            orgId: result.subject.startsWith("org_") ? result.subject : null
          };
        }
        case TokenType.MachineToken: {
          const result = verificationResult;
          return {
            ...baseObject,
            tokenType,
            name: result.name,
            claims: result.claims,
            scopes: result.scopes,
            machineId: result.subject
          };
        }
        case TokenType.OAuthToken: {
          const result = verificationResult;
          return {
            ...baseObject,
            tokenType,
            scopes: result.scopes,
            userId: result.subject,
            clientId: result.clientId
          };
        }
        default:
          throw new Error(`Invalid token type: ${tokenType}`);
      }
    }
    function unauthenticatedMachineObject(tokenType, debugData) {
      const baseObject = {
        id: null,
        subject: null,
        scopes: null,
        has: () => false,
        getToken: () => Promise.resolve(null),
        debug: createDebug(debugData),
        isAuthenticated: false
      };
      switch (tokenType) {
        case TokenType.ApiKey: {
          return {
            ...baseObject,
            tokenType,
            name: null,
            claims: null,
            scopes: null,
            userId: null,
            orgId: null
          };
        }
        case TokenType.MachineToken: {
          return {
            ...baseObject,
            tokenType,
            name: null,
            claims: null,
            scopes: null,
            machineId: null
          };
        }
        case TokenType.OAuthToken: {
          return {
            ...baseObject,
            tokenType,
            scopes: null,
            userId: null,
            clientId: null
          };
        }
        default:
          throw new Error(`Invalid token type: ${tokenType}`);
      }
    }
    function invalidTokenAuthObject() {
      return {
        isAuthenticated: false,
        tokenType: null,
        getToken: () => Promise.resolve(null),
        has: () => false,
        debug: () => ({})
      };
    }
    var createGetToken = (params) => {
      const { fetcher, sessionToken, sessionId } = params || {};
      return async (options = {}) => {
        if (!sessionId) {
          return null;
        }
        if (options.template || options.expiresInSeconds !== void 0) {
          return fetcher(sessionId, options.template, options.expiresInSeconds);
        }
        return sessionToken;
      };
    };
    var AuthStatus = {
      SignedIn: "signed-in",
      SignedOut: "signed-out",
      Handshake: "handshake"
    };
    var AuthErrorReason = {
      ClientUATWithoutSessionToken: "client-uat-but-no-session-token",
      DevBrowserMissing: "dev-browser-missing",
      DevBrowserSync: "dev-browser-sync",
      PrimaryRespondsToSyncing: "primary-responds-to-syncing",
      SatelliteCookieNeedsSyncing: "satellite-needs-syncing",
      SessionTokenAndUATMissing: "session-token-and-uat-missing",
      SessionTokenMissing: "session-token-missing",
      SessionTokenExpired: "session-token-expired",
      SessionTokenIATBeforeClientUAT: "session-token-iat-before-client-uat",
      SessionTokenNBF: "session-token-nbf",
      SessionTokenIatInTheFuture: "session-token-iat-in-the-future",
      SessionTokenWithoutClientUAT: "session-token-but-no-client-uat",
      ActiveOrganizationMismatch: "active-organization-mismatch",
      TokenTypeMismatch: "token-type-mismatch",
      UnexpectedError: "unexpected-error"
    };
    function signedIn(params) {
      const { authenticateContext, headers = new Headers(), token } = params;
      const toAuth = ({ treatPendingAsSignedOut = true } = {}) => {
        if (params.tokenType === TokenType.SessionToken) {
          const { sessionClaims } = params;
          const authObject = signedInAuthObject(authenticateContext, token, sessionClaims);
          if (treatPendingAsSignedOut && authObject.sessionStatus === "pending") {
            return signedOutAuthObject(void 0, authObject.sessionStatus);
          }
          return authObject;
        }
        const { machineData } = params;
        return authenticatedMachineObject(params.tokenType, token, machineData, authenticateContext);
      };
      return {
        status: AuthStatus.SignedIn,
        reason: null,
        message: null,
        proxyUrl: authenticateContext.proxyUrl || "",
        publishableKey: authenticateContext.publishableKey || "",
        isSatellite: authenticateContext.isSatellite || false,
        domain: authenticateContext.domain || "",
        signInUrl: authenticateContext.signInUrl || "",
        signUpUrl: authenticateContext.signUpUrl || "",
        afterSignInUrl: authenticateContext.afterSignInUrl || "",
        afterSignUpUrl: authenticateContext.afterSignUpUrl || "",
        isSignedIn: true,
        isAuthenticated: true,
        tokenType: params.tokenType,
        toAuth,
        headers,
        token
      };
    }
    function signedOut(params) {
      const { authenticateContext, headers = new Headers(), reason, message = "", tokenType } = params;
      const toAuth = () => {
        if (tokenType === TokenType.SessionToken) {
          return signedOutAuthObject({ ...authenticateContext, status: AuthStatus.SignedOut, reason, message });
        }
        return unauthenticatedMachineObject(tokenType, { reason, message, headers });
      };
      return withDebugHeaders({
        status: AuthStatus.SignedOut,
        reason,
        message,
        proxyUrl: authenticateContext.proxyUrl || "",
        publishableKey: authenticateContext.publishableKey || "",
        isSatellite: authenticateContext.isSatellite || false,
        domain: authenticateContext.domain || "",
        signInUrl: authenticateContext.signInUrl || "",
        signUpUrl: authenticateContext.signUpUrl || "",
        afterSignInUrl: authenticateContext.afterSignInUrl || "",
        afterSignUpUrl: authenticateContext.afterSignUpUrl || "",
        isSignedIn: false,
        isAuthenticated: false,
        tokenType,
        toAuth,
        headers,
        token: null
      });
    }
    function handshake(authenticateContext, reason, message = "", headers) {
      return withDebugHeaders({
        status: AuthStatus.Handshake,
        reason,
        message,
        publishableKey: authenticateContext.publishableKey || "",
        isSatellite: authenticateContext.isSatellite || false,
        domain: authenticateContext.domain || "",
        proxyUrl: authenticateContext.proxyUrl || "",
        signInUrl: authenticateContext.signInUrl || "",
        signUpUrl: authenticateContext.signUpUrl || "",
        afterSignInUrl: authenticateContext.afterSignInUrl || "",
        afterSignUpUrl: authenticateContext.afterSignUpUrl || "",
        isSignedIn: false,
        isAuthenticated: false,
        tokenType: TokenType.SessionToken,
        toAuth: () => null,
        headers,
        token: null
      });
    }
    function signedOutInvalidToken() {
      const authObject = invalidTokenAuthObject();
      return withDebugHeaders({
        status: AuthStatus.SignedOut,
        reason: AuthErrorReason.TokenTypeMismatch,
        message: "",
        proxyUrl: "",
        publishableKey: "",
        isSatellite: false,
        domain: "",
        signInUrl: "",
        signUpUrl: "",
        afterSignInUrl: "",
        afterSignUpUrl: "",
        isSignedIn: false,
        isAuthenticated: false,
        tokenType: null,
        toAuth: () => authObject,
        headers: new Headers(),
        token: null
      });
    }
    var withDebugHeaders = (requestState) => {
      const headers = new Headers(requestState.headers || {});
      if (requestState.message) {
        try {
          headers.set(constants.Headers.AuthMessage, requestState.message);
        } catch {
        }
      }
      if (requestState.reason) {
        try {
          headers.set(constants.Headers.AuthReason, requestState.reason);
        } catch {
        }
      }
      if (requestState.status) {
        try {
          headers.set(constants.Headers.AuthStatus, requestState.status);
        } catch {
        }
      }
      requestState.headers = headers;
      return requestState;
    };
    var getCookieName = (cookieDirective) => {
      var _a;
      return (_a = cookieDirective.split(";")[0]) == null ? void 0 : _a.split("=")[0];
    };
    var getCookieValue = (cookieDirective) => {
      var _a;
      return (_a = cookieDirective.split(";")[0]) == null ? void 0 : _a.split("=")[1];
    };
    async function verifyHandshakeJwt(token, { key }) {
      const { data: decoded, errors } = decodeJwt(token);
      if (errors) {
        throw errors[0];
      }
      const { header, payload } = decoded;
      const { typ, alg } = header;
      assertHeaderType(typ);
      assertHeaderAlgorithm(alg);
      const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);
      if (signatureErrors) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenVerificationFailed,
          message: `Error verifying handshake token. ${signatureErrors[0]}`
        });
      }
      if (!signatureValid) {
        throw new TokenVerificationError({
          reason: TokenVerificationErrorReason.TokenInvalidSignature,
          message: "Handshake signature is invalid."
        });
      }
      return payload;
    }
    async function verifyHandshakeToken(token, options) {
      const { secretKey, apiUrl, apiVersion, jwksCacheTtlInMs, jwtKey, skipJwksCache } = options;
      const { data, errors } = decodeJwt(token);
      if (errors) {
        throw errors[0];
      }
      const { kid } = data.header;
      let key;
      if (jwtKey) {
        key = loadClerkJWKFromLocal(jwtKey);
      } else if (secretKey) {
        key = await loadClerkJWKFromRemote({ secretKey, apiUrl, apiVersion, kid, jwksCacheTtlInMs, skipJwksCache });
      } else {
        throw new TokenVerificationError({
          action: TokenVerificationErrorAction.SetClerkJWTKey,
          message: "Failed to resolve JWK during handshake verification.",
          reason: TokenVerificationErrorReason.JWKFailedToResolve
        });
      }
      return await verifyHandshakeJwt(token, {
        key
      });
    }
    var HandshakeService = class {
      constructor(authenticateContext, options, organizationMatcher) {
        this.authenticateContext = authenticateContext;
        this.options = options;
        this.organizationMatcher = organizationMatcher;
      }
      /**
       * Determines if a request is eligible for handshake based on its headers
       *
       * Currently, a request is only eligible for a handshake if we can say it's *probably* a request for a document, not a fetch or some other exotic request.
       * This heuristic should give us a reliable enough signal for browsers that support `Sec-Fetch-Dest` and for those that don't.
       *
       * @returns boolean indicating if the request is eligible for handshake
       */
      isRequestEligibleForHandshake() {
        const { accept, secFetchDest } = this.authenticateContext;
        if (secFetchDest === "document" || secFetchDest === "iframe") {
          return true;
        }
        if (!secFetchDest && (accept == null ? void 0 : accept.startsWith("text/html"))) {
          return true;
        }
        return false;
      }
      /**
       * Builds the redirect headers for a handshake request
       * @param reason - The reason for the handshake (e.g. 'session-token-expired')
       * @returns Headers object containing the Location header for redirect
       * @throws Error if clerkUrl is missing in authenticateContext
       */
      buildRedirectToHandshake(reason) {
        var _a;
        if (!((_a = this.authenticateContext) == null ? void 0 : _a.clerkUrl)) {
          throw new Error("Missing clerkUrl in authenticateContext");
        }
        const redirectUrl = this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl);
        let baseUrl = this.authenticateContext.frontendApi.startsWith("http") ? this.authenticateContext.frontendApi : `https://${this.authenticateContext.frontendApi}`;
        baseUrl = baseUrl.replace(/\/+$/, "") + "/";
        const url = new URL("v1/client/handshake", baseUrl);
        url.searchParams.append("redirect_url", (redirectUrl == null ? void 0 : redirectUrl.href) || "");
        url.searchParams.append("__clerk_api_version", SUPPORTED_BAPI_VERSION);
        url.searchParams.append(
          constants.QueryParameters.SuffixedCookies,
          this.authenticateContext.usesSuffixedCookies().toString()
        );
        url.searchParams.append(constants.QueryParameters.HandshakeReason, reason);
        url.searchParams.append(constants.QueryParameters.HandshakeFormat, "nonce");
        if (this.authenticateContext.instanceType === "development" && this.authenticateContext.devBrowserToken) {
          url.searchParams.append(constants.QueryParameters.DevBrowser, this.authenticateContext.devBrowserToken);
        }
        const toActivate = this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl, this.organizationMatcher);
        if (toActivate) {
          const params = this.getOrganizationSyncQueryParams(toActivate);
          params.forEach((value, key) => {
            url.searchParams.append(key, value);
          });
        }
        return new Headers({ [constants.Headers.Location]: url.href });
      }
      /**
       * Gets cookies from either a handshake nonce or a handshake token
       * @returns Promise resolving to string array of cookie directives
       */
      async getCookiesFromHandshake() {
        var _a;
        const cookiesToSet = [];
        if (this.authenticateContext.handshakeNonce) {
          try {
            const handshakePayload = await ((_a = this.authenticateContext.apiClient) == null ? void 0 : _a.clients.getHandshakePayload({
              nonce: this.authenticateContext.handshakeNonce
            }));
            if (handshakePayload) {
              cookiesToSet.push(...handshakePayload.directives);
            }
          } catch (error) {
            console.error("Clerk: HandshakeService: error getting handshake payload:", error);
          }
        } else if (this.authenticateContext.handshakeToken) {
          const handshakePayload = await verifyHandshakeToken(
            this.authenticateContext.handshakeToken,
            this.authenticateContext
          );
          if (handshakePayload && Array.isArray(handshakePayload.handshake)) {
            cookiesToSet.push(...handshakePayload.handshake);
          }
        }
        return cookiesToSet;
      }
      /**
       * Resolves a handshake request by verifying the handshake token and setting appropriate cookies
       * @returns Promise resolving to either a SignedInState or SignedOutState
       * @throws Error if handshake verification fails or if there are issues with the session token
       */
      async resolveHandshake() {
        const headers = new Headers({
          "Access-Control-Allow-Origin": "null",
          "Access-Control-Allow-Credentials": "true"
        });
        const cookiesToSet = await this.getCookiesFromHandshake();
        let sessionToken = "";
        cookiesToSet.forEach((x) => {
          headers.append("Set-Cookie", x);
          if (getCookieName(x).startsWith(constants.Cookies.Session)) {
            sessionToken = getCookieValue(x);
          }
        });
        if (this.authenticateContext.instanceType === "development") {
          const newUrl = new URL(this.authenticateContext.clerkUrl);
          newUrl.searchParams.delete(constants.QueryParameters.Handshake);
          newUrl.searchParams.delete(constants.QueryParameters.HandshakeHelp);
          headers.append(constants.Headers.Location, newUrl.toString());
          headers.set(constants.Headers.CacheControl, "no-store");
        }
        if (sessionToken === "") {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext: this.authenticateContext,
            reason: AuthErrorReason.SessionTokenMissing,
            message: "",
            headers
          });
        }
        const { data, errors: [error] = [] } = await verifyToken(sessionToken, this.authenticateContext);
        if (data) {
          return signedIn({
            tokenType: TokenType.SessionToken,
            authenticateContext: this.authenticateContext,
            sessionClaims: data,
            headers,
            token: sessionToken
          });
        }
        if (this.authenticateContext.instanceType === "development" && ((error == null ? void 0 : error.reason) === TokenVerificationErrorReason.TokenExpired || (error == null ? void 0 : error.reason) === TokenVerificationErrorReason.TokenNotActiveYet || (error == null ? void 0 : error.reason) === TokenVerificationErrorReason.TokenIatInTheFuture)) {
          const developmentError = new TokenVerificationError({
            action: error.action,
            message: error.message,
            reason: error.reason
          });
          developmentError.tokenCarrier = "cookie";
          console.error(
            `Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${developmentError.getFullMessage()}`
          );
          const { data: retryResult, errors: [retryError] = [] } = await verifyToken(sessionToken, {
            ...this.authenticateContext,
            clockSkewInMs: 864e5
          });
          if (retryResult) {
            return signedIn({
              tokenType: TokenType.SessionToken,
              authenticateContext: this.authenticateContext,
              sessionClaims: retryResult,
              headers,
              token: sessionToken
            });
          }
          throw new Error((retryError == null ? void 0 : retryError.message) || "Clerk: Handshake retry failed.");
        }
        throw new Error((error == null ? void 0 : error.message) || "Clerk: Handshake failed.");
      }
      /**
       * Handles handshake token verification errors in development mode
       * @param error - The TokenVerificationError that occurred
       * @throws Error with a descriptive message about the verification failure
       */
      handleTokenVerificationErrorInDevelopment(error) {
        if (error.reason === TokenVerificationErrorReason.TokenInvalidSignature) {
          const msg = `Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.`;
          throw new Error(msg);
        }
        throw new Error(`Clerk: Handshake token verification failed: ${error.getFullMessage()}.`);
      }
      /**
       * Checks if a redirect loop is detected and sets headers to track redirect count
       * @param headers - The Headers object to modify
       * @returns boolean indicating if a redirect loop was detected (true) or if the request can proceed (false)
       */
      checkAndTrackRedirectLoop(headers) {
        if (this.authenticateContext.handshakeRedirectLoopCounter === 3) {
          return true;
        }
        const newCounterValue = this.authenticateContext.handshakeRedirectLoopCounter + 1;
        const cookieName = constants.Cookies.RedirectCount;
        headers.append("Set-Cookie", `${cookieName}=${newCounterValue}; SameSite=Lax; HttpOnly; Max-Age=3`);
        return false;
      }
      removeDevBrowserFromURL(url) {
        const updatedURL = new URL(url);
        updatedURL.searchParams.delete(constants.QueryParameters.DevBrowser);
        updatedURL.searchParams.delete(constants.QueryParameters.LegacyDevBrowser);
        return updatedURL;
      }
      getOrganizationSyncTarget(url, matchers) {
        return matchers.findTarget(url);
      }
      getOrganizationSyncQueryParams(toActivate) {
        const ret = /* @__PURE__ */ new Map();
        if (toActivate.type === "personalAccount") {
          ret.set("organization_id", "");
        }
        if (toActivate.type === "organization") {
          if (toActivate.organizationId) {
            ret.set("organization_id", toActivate.organizationId);
          }
          if (toActivate.organizationSlug) {
            ret.set("organization_id", toActivate.organizationSlug);
          }
        }
        return ret;
      }
    };
    var import_pathToRegexp = require_pathToRegexp();
    var OrganizationMatcher = class {
      constructor(options) {
        this.organizationPattern = this.createMatcher(options == null ? void 0 : options.organizationPatterns);
        this.personalAccountPattern = this.createMatcher(options == null ? void 0 : options.personalAccountPatterns);
      }
      createMatcher(pattern) {
        if (!pattern)
          return null;
        try {
          return (0, import_pathToRegexp.match)(pattern);
        } catch (e) {
          throw new Error(`Invalid pattern "${pattern}": ${e}`);
        }
      }
      findTarget(url) {
        const orgTarget = this.findOrganizationTarget(url);
        if (orgTarget)
          return orgTarget;
        return this.findPersonalAccountTarget(url);
      }
      findOrganizationTarget(url) {
        if (!this.organizationPattern)
          return null;
        try {
          const result = this.organizationPattern(url.pathname);
          if (!result || !("params" in result))
            return null;
          const params = result.params;
          if (params.id)
            return { type: "organization", organizationId: params.id };
          if (params.slug)
            return { type: "organization", organizationSlug: params.slug };
          return null;
        } catch (e) {
          console.error("Failed to match organization pattern:", e);
          return null;
        }
      }
      findPersonalAccountTarget(url) {
        if (!this.personalAccountPattern)
          return null;
        try {
          const result = this.personalAccountPattern(url.pathname);
          return result ? { type: "personalAccount" } : null;
        } catch (e) {
          console.error("Failed to match personal account pattern:", e);
          return null;
        }
      }
    };
    var RefreshTokenErrorReason = {
      NonEligibleNoCookie: "non-eligible-no-refresh-cookie",
      NonEligibleNonGet: "non-eligible-non-get",
      InvalidSessionToken: "invalid-session-token",
      MissingApiClient: "missing-api-client",
      MissingSessionToken: "missing-session-token",
      MissingRefreshToken: "missing-refresh-token",
      ExpiredSessionTokenDecodeFailed: "expired-session-token-decode-failed",
      ExpiredSessionTokenMissingSidClaim: "expired-session-token-missing-sid-claim",
      FetchError: "fetch-error",
      UnexpectedSDKError: "unexpected-sdk-error",
      UnexpectedBAPIError: "unexpected-bapi-error"
    };
    function assertSignInUrlExists(signInUrl, key) {
      if (!signInUrl && (0, import_keys.isDevelopmentFromSecretKey)(key)) {
        throw new Error(`Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite`);
      }
    }
    function assertProxyUrlOrDomain(proxyUrlOrDomain) {
      if (!proxyUrlOrDomain) {
        throw new Error(`Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`);
      }
    }
    function assertSignInUrlFormatAndOrigin(_signInUrl, origin) {
      let signInUrl;
      try {
        signInUrl = new URL(_signInUrl);
      } catch {
        throw new Error(`The signInUrl needs to have a absolute url format.`);
      }
      if (signInUrl.origin === origin) {
        throw new Error(`The signInUrl needs to be on a different origin than your satellite application.`);
      }
    }
    function isRequestEligibleForRefresh(err, authenticateContext, request) {
      return err.reason === TokenVerificationErrorReason.TokenExpired && !!authenticateContext.refreshTokenInCookie && request.method === "GET";
    }
    function checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext) {
      const mismatch = !isTokenTypeAccepted(parsedTokenType, acceptsToken);
      if (mismatch) {
        return signedOut({
          tokenType: parsedTokenType,
          authenticateContext,
          reason: AuthErrorReason.TokenTypeMismatch
        });
      }
      return null;
    }
    function isTokenTypeInAcceptedArray(acceptsToken, authenticateContext) {
      let parsedTokenType = null;
      const { tokenInHeader } = authenticateContext;
      if (tokenInHeader) {
        if (isMachineTokenByPrefix(tokenInHeader)) {
          parsedTokenType = getMachineTokenType(tokenInHeader);
        } else {
          parsedTokenType = TokenType.SessionToken;
        }
      }
      const typeToCheck = parsedTokenType ?? TokenType.SessionToken;
      return isTokenTypeAccepted(typeToCheck, acceptsToken);
    }
    var authenticateRequest = async (request, options) => {
      const authenticateContext = await createAuthenticateContext(createClerkRequest(request), options);
      assertValidSecretKey(authenticateContext.secretKey);
      const acceptsToken = options.acceptsToken ?? TokenType.SessionToken;
      if (authenticateContext.isSatellite) {
        assertSignInUrlExists(authenticateContext.signInUrl, authenticateContext.secretKey);
        if (authenticateContext.signInUrl && authenticateContext.origin) {
          assertSignInUrlFormatAndOrigin(authenticateContext.signInUrl, authenticateContext.origin);
        }
        assertProxyUrlOrDomain(authenticateContext.proxyUrl || authenticateContext.domain);
      }
      const organizationMatcher = new OrganizationMatcher(options.organizationSyncOptions);
      const handshakeService = new HandshakeService(
        authenticateContext,
        { organizationSyncOptions: options.organizationSyncOptions },
        organizationMatcher
      );
      async function refreshToken(authenticateContext2) {
        var _a, _b;
        if (!options.apiClient) {
          return {
            data: null,
            error: {
              message: "An apiClient is needed to perform token refresh.",
              cause: { reason: RefreshTokenErrorReason.MissingApiClient }
            }
          };
        }
        const { sessionToken: expiredSessionToken, refreshTokenInCookie: refreshToken2 } = authenticateContext2;
        if (!expiredSessionToken) {
          return {
            data: null,
            error: {
              message: "Session token must be provided.",
              cause: { reason: RefreshTokenErrorReason.MissingSessionToken }
            }
          };
        }
        if (!refreshToken2) {
          return {
            data: null,
            error: {
              message: "Refresh token must be provided.",
              cause: { reason: RefreshTokenErrorReason.MissingRefreshToken }
            }
          };
        }
        const { data: decodeResult, errors: decodedErrors } = decodeJwt(expiredSessionToken);
        if (!decodeResult || decodedErrors) {
          return {
            data: null,
            error: {
              message: "Unable to decode the expired session token.",
              cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenDecodeFailed, errors: decodedErrors }
            }
          };
        }
        if (!((_a = decodeResult == null ? void 0 : decodeResult.payload) == null ? void 0 : _a.sid)) {
          return {
            data: null,
            error: {
              message: "Expired session token is missing the `sid` claim.",
              cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenMissingSidClaim }
            }
          };
        }
        try {
          const response = await options.apiClient.sessions.refreshSession(decodeResult.payload.sid, {
            format: "cookie",
            suffixed_cookies: authenticateContext2.usesSuffixedCookies(),
            expired_token: expiredSessionToken || "",
            refresh_token: refreshToken2 || "",
            request_origin: authenticateContext2.clerkUrl.origin,
            // The refresh endpoint expects headers as Record<string, string[]>, so we need to transform it.
            request_headers: Object.fromEntries(Array.from(request.headers.entries()).map(([k, v]) => [k, [v]]))
          });
          return { data: response.cookies, error: null };
        } catch (err) {
          if ((_b = err == null ? void 0 : err.errors) == null ? void 0 : _b.length) {
            if (err.errors[0].code === "unexpected_error") {
              return {
                data: null,
                error: {
                  message: `Fetch unexpected error`,
                  cause: { reason: RefreshTokenErrorReason.FetchError, errors: err.errors }
                }
              };
            }
            return {
              data: null,
              error: {
                message: err.errors[0].code,
                cause: { reason: err.errors[0].code, errors: err.errors }
              }
            };
          } else {
            return {
              data: null,
              error: {
                message: `Unexpected Server/BAPI error`,
                cause: { reason: RefreshTokenErrorReason.UnexpectedBAPIError, errors: [err] }
              }
            };
          }
        }
      }
      async function attemptRefresh(authenticateContext2) {
        const { data: cookiesToSet, error } = await refreshToken(authenticateContext2);
        if (!cookiesToSet || cookiesToSet.length === 0) {
          return { data: null, error };
        }
        const headers = new Headers();
        let sessionToken = "";
        cookiesToSet.forEach((x) => {
          headers.append("Set-Cookie", x);
          if (getCookieName(x).startsWith(constants.Cookies.Session)) {
            sessionToken = getCookieValue(x);
          }
        });
        const { data: jwtPayload, errors } = await verifyToken(sessionToken, authenticateContext2);
        if (errors) {
          return {
            data: null,
            error: {
              message: `Clerk: unable to verify refreshed session token.`,
              cause: { reason: RefreshTokenErrorReason.InvalidSessionToken, errors }
            }
          };
        }
        return { data: { jwtPayload, sessionToken, headers }, error: null };
      }
      function handleMaybeHandshakeStatus(authenticateContext2, reason, message, headers) {
        if (!handshakeService.isRequestEligibleForHandshake()) {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext: authenticateContext2,
            reason,
            message
          });
        }
        const handshakeHeaders = headers ?? handshakeService.buildRedirectToHandshake(reason);
        if (handshakeHeaders.get(constants.Headers.Location)) {
          handshakeHeaders.set(constants.Headers.CacheControl, "no-store");
        }
        const isRedirectLoop = handshakeService.checkAndTrackRedirectLoop(handshakeHeaders);
        if (isRedirectLoop) {
          const msg = `Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard.`;
          console.log(msg);
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext: authenticateContext2,
            reason,
            message
          });
        }
        return handshake(authenticateContext2, reason, message, handshakeHeaders);
      }
      function handleMaybeOrganizationSyncHandshake(authenticateContext2, auth) {
        const organizationSyncTarget = organizationMatcher.findTarget(authenticateContext2.clerkUrl);
        if (!organizationSyncTarget) {
          return null;
        }
        let mustActivate = false;
        if (organizationSyncTarget.type === "organization") {
          if (organizationSyncTarget.organizationSlug && organizationSyncTarget.organizationSlug !== auth.orgSlug) {
            mustActivate = true;
          }
          if (organizationSyncTarget.organizationId && organizationSyncTarget.organizationId !== auth.orgId) {
            mustActivate = true;
          }
        }
        if (organizationSyncTarget.type === "personalAccount" && auth.orgId) {
          mustActivate = true;
        }
        if (!mustActivate) {
          return null;
        }
        if (authenticateContext2.handshakeRedirectLoopCounter > 0) {
          console.warn(
            "Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."
          );
          return null;
        }
        const handshakeState = handleMaybeHandshakeStatus(
          authenticateContext2,
          AuthErrorReason.ActiveOrganizationMismatch,
          ""
        );
        if (handshakeState.status !== "handshake") {
          return null;
        }
        return handshakeState;
      }
      async function authenticateRequestWithTokenInHeader() {
        const { tokenInHeader } = authenticateContext;
        try {
          const { data, errors } = await verifyToken(tokenInHeader, authenticateContext);
          if (errors) {
            throw errors[0];
          }
          return signedIn({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            sessionClaims: data,
            headers: new Headers(),
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            token: tokenInHeader
          });
        } catch (err) {
          return handleSessionTokenError(err, "header");
        }
      }
      async function authenticateRequestWithTokenInCookie() {
        const hasActiveClient = authenticateContext.clientUat;
        const hasSessionToken = !!authenticateContext.sessionTokenInCookie;
        const hasDevBrowserToken = !!authenticateContext.devBrowserToken;
        if (authenticateContext.handshakeNonce || authenticateContext.handshakeToken) {
          try {
            return await handshakeService.resolveHandshake();
          } catch (error) {
            if (error instanceof TokenVerificationError && authenticateContext.instanceType === "development") {
              handshakeService.handleTokenVerificationErrorInDevelopment(error);
            } else {
              console.error("Clerk: unable to resolve handshake:", error);
            }
          }
        }
        if (authenticateContext.instanceType === "development" && authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.DevBrowser)) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserSync, "");
        }
        const isRequestEligibleForMultiDomainSync = authenticateContext.isSatellite && authenticateContext.secFetchDest === "document";
        if (authenticateContext.instanceType === "production" && isRequestEligibleForMultiDomainSync) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, "");
        }
        if (authenticateContext.instanceType === "development" && isRequestEligibleForMultiDomainSync && !authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.ClerkSynced)) {
          const redirectURL = new URL(authenticateContext.signInUrl);
          redirectURL.searchParams.append(
            constants.QueryParameters.ClerkRedirectUrl,
            authenticateContext.clerkUrl.toString()
          );
          const headers = new Headers({ [constants.Headers.Location]: redirectURL.toString() });
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, "", headers);
        }
        const redirectUrl = new URL(authenticateContext.clerkUrl).searchParams.get(
          constants.QueryParameters.ClerkRedirectUrl
        );
        if (authenticateContext.instanceType === "development" && !authenticateContext.isSatellite && redirectUrl) {
          const redirectBackToSatelliteUrl = new URL(redirectUrl);
          if (authenticateContext.devBrowserToken) {
            redirectBackToSatelliteUrl.searchParams.append(
              constants.QueryParameters.DevBrowser,
              authenticateContext.devBrowserToken
            );
          }
          redirectBackToSatelliteUrl.searchParams.append(constants.QueryParameters.ClerkSynced, "true");
          const headers = new Headers({ [constants.Headers.Location]: redirectBackToSatelliteUrl.toString() });
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.PrimaryRespondsToSyncing, "", headers);
        }
        if (authenticateContext.instanceType === "development" && !hasDevBrowserToken) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserMissing, "");
        }
        if (!hasActiveClient && !hasSessionToken) {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            reason: AuthErrorReason.SessionTokenAndUATMissing
          });
        }
        if (!hasActiveClient && hasSessionToken) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenWithoutClientUAT, "");
        }
        if (hasActiveClient && !hasSessionToken) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.ClientUATWithoutSessionToken, "");
        }
        const { data: decodeResult, errors: decodedErrors } = decodeJwt(authenticateContext.sessionTokenInCookie);
        if (decodedErrors) {
          return handleSessionTokenError(decodedErrors[0], "cookie");
        }
        if (decodeResult.payload.iat < authenticateContext.clientUat) {
          return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenIATBeforeClientUAT, "");
        }
        try {
          const { data, errors } = await verifyToken(authenticateContext.sessionTokenInCookie, authenticateContext);
          if (errors) {
            throw errors[0];
          }
          const signedInRequestState = signedIn({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            sessionClaims: data,
            headers: new Headers(),
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            token: authenticateContext.sessionTokenInCookie
          });
          const authObject = signedInRequestState.toAuth();
          if (authObject.userId) {
            const handshakeRequestState = handleMaybeOrganizationSyncHandshake(authenticateContext, authObject);
            if (handshakeRequestState) {
              return handshakeRequestState;
            }
          }
          return signedInRequestState;
        } catch (err) {
          return handleSessionTokenError(err, "cookie");
        }
        return signedOut({
          tokenType: TokenType.SessionToken,
          authenticateContext,
          reason: AuthErrorReason.UnexpectedError
        });
      }
      async function handleSessionTokenError(err, tokenCarrier) {
        var _a;
        if (!(err instanceof TokenVerificationError)) {
          return signedOut({
            tokenType: TokenType.SessionToken,
            authenticateContext,
            reason: AuthErrorReason.UnexpectedError
          });
        }
        let refreshError;
        if (isRequestEligibleForRefresh(err, authenticateContext, request)) {
          const { data, error } = await attemptRefresh(authenticateContext);
          if (data) {
            return signedIn({
              tokenType: TokenType.SessionToken,
              authenticateContext,
              sessionClaims: data.jwtPayload,
              headers: data.headers,
              token: data.sessionToken
            });
          }
          if ((_a = error == null ? void 0 : error.cause) == null ? void 0 : _a.reason) {
            refreshError = error.cause.reason;
          } else {
            refreshError = RefreshTokenErrorReason.UnexpectedSDKError;
          }
        } else {
          if (request.method !== "GET") {
            refreshError = RefreshTokenErrorReason.NonEligibleNonGet;
          } else if (!authenticateContext.refreshTokenInCookie) {
            refreshError = RefreshTokenErrorReason.NonEligibleNoCookie;
          } else {
            refreshError = null;
          }
        }
        err.tokenCarrier = tokenCarrier;
        const reasonToHandshake = [
          TokenVerificationErrorReason.TokenExpired,
          TokenVerificationErrorReason.TokenNotActiveYet,
          TokenVerificationErrorReason.TokenIatInTheFuture
        ].includes(err.reason);
        if (reasonToHandshake) {
          return handleMaybeHandshakeStatus(
            authenticateContext,
            convertTokenVerificationErrorReasonToAuthErrorReason({ tokenError: err.reason, refreshError }),
            err.getFullMessage()
          );
        }
        return signedOut({
          tokenType: TokenType.SessionToken,
          authenticateContext,
          reason: err.reason,
          message: err.getFullMessage()
        });
      }
      function handleMachineError(tokenType, err) {
        if (!(err instanceof MachineTokenVerificationError)) {
          return signedOut({
            tokenType,
            authenticateContext,
            reason: AuthErrorReason.UnexpectedError
          });
        }
        return signedOut({
          tokenType,
          authenticateContext,
          reason: err.code,
          message: err.getFullMessage()
        });
      }
      async function authenticateMachineRequestWithTokenInHeader() {
        const { tokenInHeader } = authenticateContext;
        if (!tokenInHeader) {
          return handleSessionTokenError(new Error("Missing token in header"), "header");
        }
        if (!isMachineTokenByPrefix(tokenInHeader)) {
          return signedOut({
            tokenType: acceptsToken,
            authenticateContext,
            reason: AuthErrorReason.TokenTypeMismatch,
            message: ""
          });
        }
        const parsedTokenType = getMachineTokenType(tokenInHeader);
        const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);
        if (mismatchState) {
          return mismatchState;
        }
        const { data, tokenType, errors } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);
        if (errors) {
          return handleMachineError(tokenType, errors[0]);
        }
        return signedIn({
          tokenType,
          authenticateContext,
          machineData: data,
          token: tokenInHeader
        });
      }
      async function authenticateAnyRequestWithTokenInHeader() {
        const { tokenInHeader } = authenticateContext;
        if (!tokenInHeader) {
          return handleSessionTokenError(new Error("Missing token in header"), "header");
        }
        if (isMachineTokenByPrefix(tokenInHeader)) {
          const parsedTokenType = getMachineTokenType(tokenInHeader);
          const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);
          if (mismatchState) {
            return mismatchState;
          }
          const { data: data2, tokenType, errors: errors2 } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);
          if (errors2) {
            return handleMachineError(tokenType, errors2[0]);
          }
          return signedIn({
            tokenType,
            authenticateContext,
            machineData: data2,
            token: tokenInHeader
          });
        }
        const { data, errors } = await verifyToken(tokenInHeader, authenticateContext);
        if (errors) {
          return handleSessionTokenError(errors[0], "header");
        }
        return signedIn({
          tokenType: TokenType.SessionToken,
          authenticateContext,
          sessionClaims: data,
          token: tokenInHeader
        });
      }
      if (Array.isArray(acceptsToken)) {
        if (!isTokenTypeInAcceptedArray(acceptsToken, authenticateContext)) {
          return signedOutInvalidToken();
        }
      }
      if (authenticateContext.tokenInHeader) {
        if (acceptsToken === "any") {
          return authenticateAnyRequestWithTokenInHeader();
        }
        if (acceptsToken === TokenType.SessionToken) {
          return authenticateRequestWithTokenInHeader();
        }
        return authenticateMachineRequestWithTokenInHeader();
      }
      if (acceptsToken === TokenType.OAuthToken || acceptsToken === TokenType.ApiKey || acceptsToken === TokenType.MachineToken) {
        return signedOut({
          tokenType: acceptsToken,
          authenticateContext,
          reason: "No token in header"
        });
      }
      return authenticateRequestWithTokenInCookie();
    };
    var debugRequestState = (params) => {
      const { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain } = params;
      return { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain };
    };
    var convertTokenVerificationErrorReasonToAuthErrorReason = ({
      tokenError,
      refreshError
    }) => {
      switch (tokenError) {
        case TokenVerificationErrorReason.TokenExpired:
          return `${AuthErrorReason.SessionTokenExpired}-refresh-${refreshError}`;
        case TokenVerificationErrorReason.TokenNotActiveYet:
          return AuthErrorReason.SessionTokenNBF;
        case TokenVerificationErrorReason.TokenIatInTheFuture:
          return AuthErrorReason.SessionTokenIatInTheFuture;
        default:
          return AuthErrorReason.UnexpectedError;
      }
    };
    var defaultOptions = {
      secretKey: "",
      jwtKey: "",
      apiUrl: void 0,
      apiVersion: void 0,
      proxyUrl: "",
      publishableKey: "",
      isSatellite: false,
      domain: "",
      audience: ""
    };
    function createAuthenticateRequest(params) {
      const buildTimeOptions = mergePreDefinedOptions(defaultOptions, params.options);
      const apiClient = params.apiClient;
      const authenticateRequest2 = (request, options = {}) => {
        const { apiUrl, apiVersion } = buildTimeOptions;
        const runTimeOptions = mergePreDefinedOptions(buildTimeOptions, options);
        return authenticateRequest(request, {
          ...options,
          ...runTimeOptions,
          // We should add all the omitted props from options here (eg apiUrl / apiVersion)
          // to avoid runtime options override them.
          apiUrl,
          apiVersion,
          apiClient
        });
      };
      return {
        authenticateRequest: authenticateRequest2,
        debugRequestState
      };
    }
    var verifyToken2 = withLegacyReturn(verifyToken);
    function createClerkClient(options) {
      const opts = { ...options };
      const apiClient = createBackendApiClient(opts);
      const requestState = createAuthenticateRequest({ options: opts, apiClient });
      const telemetry = new import_telemetry.TelemetryCollector({
        ...options.telemetry,
        publishableKey: opts.publishableKey,
        secretKey: opts.secretKey,
        samplingRate: 0.1,
        ...opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}
      });
      return {
        ...apiClient,
        ...requestState,
        telemetry
      };
    }
  }
});

// node_modules/@clerk/shared/dist/netlifyCacheHandler.js
var require_netlifyCacheHandler = __commonJS({
  "node_modules/@clerk/shared/dist/netlifyCacheHandler.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var netlifyCacheHandler_exports = {};
    __export2(netlifyCacheHandler_exports, {
      CLERK_NETLIFY_CACHE_BUST_PARAM: () => CLERK_NETLIFY_CACHE_BUST_PARAM,
      handleNetlifyCacheInDevInstance: () => handleNetlifyCacheInDevInstance
    });
    module.exports = __toCommonJS2(netlifyCacheHandler_exports);
    function isDevelopmentFromPublishableKey(apiKey) {
      return apiKey.startsWith("test_") || apiKey.startsWith("pk_test_");
    }
    var CLERK_NETLIFY_CACHE_BUST_PARAM = "__clerk_netlify_cache_bust";
    function handleNetlifyCacheInDevInstance({
      locationHeader,
      requestStateHeaders,
      publishableKey
    }) {
      var _a;
      const isOnNetlify = process.env.NETLIFY || ((_a = process.env.URL) == null ? void 0 : _a.endsWith("netlify.app")) || Boolean(process.env.NETLIFY_FUNCTIONS_TOKEN);
      const isDevelopmentInstance = isDevelopmentFromPublishableKey(publishableKey);
      if (isOnNetlify && isDevelopmentInstance) {
        const hasHandshakeQueryParam = locationHeader.includes("__clerk_handshake");
        if (!hasHandshakeQueryParam) {
          const url = new URL(locationHeader);
          url.searchParams.append(CLERK_NETLIFY_CACHE_BUST_PARAM, Date.now().toString());
          requestStateHeaders.set("Location", url.toString());
        }
      }
    }
  }
});

// node_modules/@clerk/shared/dist/underscore.js
var require_underscore = __commonJS({
  "node_modules/@clerk/shared/dist/underscore.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var underscore_exports = {};
    __export2(underscore_exports, {
      camelToSnake: () => camelToSnake,
      deepCamelToSnake: () => deepCamelToSnake,
      deepSnakeToCamel: () => deepSnakeToCamel,
      getNonUndefinedValues: () => getNonUndefinedValues,
      isIPV4Address: () => isIPV4Address,
      isTruthy: () => isTruthy,
      snakeToCamel: () => snakeToCamel,
      titleize: () => titleize,
      toSentence: () => toSentence
    });
    module.exports = __toCommonJS2(underscore_exports);
    var toSentence = (items) => {
      if (items.length == 0) {
        return "";
      }
      if (items.length == 1) {
        return items[0];
      }
      let sentence = items.slice(0, -1).join(", ");
      sentence += `, or ${items.slice(-1)}`;
      return sentence;
    };
    var IP_V4_ADDRESS_REGEX = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    function isIPV4Address(str) {
      return IP_V4_ADDRESS_REGEX.test(str || "");
    }
    function titleize(str) {
      const s = str || "";
      return s.charAt(0).toUpperCase() + s.slice(1);
    }
    function snakeToCamel(str) {
      return str ? str.replace(/([-_][a-z])/g, (match) => match.toUpperCase().replace(/-|_/, "")) : "";
    }
    function camelToSnake(str) {
      return str ? str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`) : "";
    }
    var createDeepObjectTransformer = (transform) => {
      const deepTransform = (obj) => {
        if (!obj) {
          return obj;
        }
        if (Array.isArray(obj)) {
          return obj.map((el) => {
            if (typeof el === "object" || Array.isArray(el)) {
              return deepTransform(el);
            }
            return el;
          });
        }
        const copy = { ...obj };
        const keys = Object.keys(copy);
        for (const oldName of keys) {
          const newName = transform(oldName.toString());
          if (newName !== oldName) {
            copy[newName] = copy[oldName];
            delete copy[oldName];
          }
          if (typeof copy[newName] === "object") {
            copy[newName] = deepTransform(copy[newName]);
          }
        }
        return copy;
      };
      return deepTransform;
    };
    var deepCamelToSnake = createDeepObjectTransformer(camelToSnake);
    var deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);
    function isTruthy(value) {
      if (typeof value === `boolean`) {
        return value;
      }
      if (value === void 0 || value === null) {
        return false;
      }
      if (typeof value === `string`) {
        if (value.toLowerCase() === `true`) {
          return true;
        }
        if (value.toLowerCase() === `false`) {
          return false;
        }
      }
      const number = parseInt(value, 10);
      if (isNaN(number)) {
        return false;
      }
      if (number > 0) {
        return true;
      }
      return false;
    }
    function getNonUndefinedValues(obj) {
      return Object.entries(obj).reduce((acc, [key, value]) => {
        if (value !== void 0) {
          acc[key] = value;
        }
        return acc;
      }, {});
    }
  }
});

// node_modules/@clerk/remix/dist/ssr/utils.js
var require_utils3 = __commonJS({
  "node_modules/@clerk/remix/dist/ssr/utils.js"(exports, module) {
    "use strict";
    var __create = Object.create;
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __getProtoOf = Object.getPrototypeOf;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
      // If the importer is in node compatibility mode or this is not an ESM
      // file that has been converted to a CommonJS file using a Babel-
      // compatible transform (i.e. "__esModule" has not been set), then set
      // "default" to the CommonJS "module.exports" for node compatibility.
      isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
      mod
    ));
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var utils_exports = {};
    __export2(utils_exports, {
      assertValidHandlerResult: () => assertValidHandlerResult,
      getResponseClerkState: () => getResponseClerkState,
      injectRequestStateIntoDeferredData: () => injectRequestStateIntoDeferredData,
      injectRequestStateIntoResponse: () => injectRequestStateIntoResponse,
      isRedirect: () => isRedirect,
      isResponse: () => isResponse,
      parseCookies: () => parseCookies,
      patchRequest: () => patchRequest,
      wrapWithClerkState: () => wrapWithClerkState
    });
    module.exports = __toCommonJS2(utils_exports);
    var import_internal = require_internal();
    var import_underscore = require_underscore();
    var import_server_runtime = (init_esm(), __toCommonJS(esm_exports));
    var import_cookie = __toESM(require_cookie());
    var import_utils = require_utils2();
    function isResponse(value) {
      return value != null && typeof value.status === "number" && typeof value.statusText === "string" && typeof value.headers === "object" && typeof value.body !== "undefined";
    }
    function isRedirect(res) {
      return res.status >= 300 && res.status < 400;
    }
    var parseCookies = (req) => {
      return import_cookie.default.parse(req.headers.get("cookie") || "");
    };
    function assertValidHandlerResult(val, error) {
      if (val !== null && typeof val !== "object" || Array.isArray(val)) {
        throw new Error(error || "");
      }
    }
    var injectRequestStateIntoResponse = async (response, requestState, context) => {
      const clone = new Response(response.body, response);
      const data = await clone.json();
      const { clerkState, headers } = getResponseClerkState(requestState, context);
      clone.headers.set(import_internal.constants.Headers.ContentType, import_internal.constants.ContentTypes.Json);
      headers.forEach((value, key) => {
        clone.headers.append(key, value);
      });
      return (0, import_server_runtime.json)({ ...data || {}, ...clerkState }, clone);
    };
    function injectRequestStateIntoDeferredData(data, requestState, context) {
      const { clerkState, headers } = getResponseClerkState(requestState, context);
      data.data.clerkState = clerkState.clerkState;
      if (typeof data.init !== "undefined") {
        data.init.headers = new Headers(data.init.headers);
        headers.forEach((value, key) => {
          data.init.headers.append(key, value);
        });
      }
      return data;
    }
    function getResponseClerkState(requestState, context) {
      const { reason, message, isSignedIn, ...rest } = requestState;
      const clerkState = wrapWithClerkState({
        __clerk_ssr_state: rest.toAuth(),
        __publishableKey: requestState.publishableKey,
        __proxyUrl: requestState.proxyUrl,
        __domain: requestState.domain,
        __isSatellite: requestState.isSatellite,
        __signInUrl: requestState.signInUrl,
        __signUpUrl: requestState.signUpUrl,
        __afterSignInUrl: requestState.afterSignInUrl,
        __afterSignUpUrl: requestState.afterSignUpUrl,
        __signInForceRedirectUrl: requestState.signInForceRedirectUrl,
        __signUpForceRedirectUrl: requestState.signUpForceRedirectUrl,
        __signInFallbackRedirectUrl: requestState.signInFallbackRedirectUrl,
        __signUpFallbackRedirectUrl: requestState.signUpFallbackRedirectUrl,
        newSubscriptionRedirectUrl: requestState.newSubscriptionRedirectUrl,
        __clerk_debug: (0, import_internal.debugRequestState)(requestState),
        __clerkJSUrl: (0, import_utils.getEnvVariable)("CLERK_JS", context),
        __clerkJSVersion: (0, import_utils.getEnvVariable)("CLERK_JS_VERSION", context),
        __telemetryDisabled: (0, import_underscore.isTruthy)((0, import_utils.getEnvVariable)("CLERK_TELEMETRY_DISABLED", context)),
        __telemetryDebug: (0, import_underscore.isTruthy)((0, import_utils.getEnvVariable)("CLERK_TELEMETRY_DEBUG", context))
      });
      return {
        clerkState,
        headers: requestState.headers
      };
    }
    var wrapWithClerkState = (data) => {
      return { clerkState: { __internal_clerk_state: { ...data } } };
    };
    var patchRequest = (request) => {
      const clonedRequest = new Request(request.url, {
        headers: request.headers,
        method: request.method,
        redirect: request.redirect,
        cache: request.cache,
        signal: request.signal
      });
      if (clonedRequest.method !== "GET" && clonedRequest.body !== null && !("duplex" in clonedRequest)) {
        clonedRequest.duplex = "half";
      }
      return clonedRequest;
    };
  }
});

// node_modules/@clerk/remix/dist/ssr/authenticateRequest.js
var require_authenticateRequest = __commonJS({
  "node_modules/@clerk/remix/dist/ssr/authenticateRequest.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var authenticateRequest_exports = {};
    __export2(authenticateRequest_exports, {
      authenticateRequest: () => authenticateRequest
    });
    module.exports = __toCommonJS2(authenticateRequest_exports);
    var import_backend = require_dist2();
    var import_internal = require_internal();
    var import_netlifyCacheHandler = require_netlifyCacheHandler();
    var import_utils = require_utils3();
    async function authenticateRequest(args, opts) {
      const { request } = args;
      const { audience, authorizedParties } = opts;
      const { apiUrl, secretKey, jwtKey, proxyUrl, isSatellite, domain, publishableKey } = opts;
      const { signInUrl, signUpUrl, afterSignInUrl, afterSignUpUrl } = opts;
      const requestState = await (0, import_backend.createClerkClient)({
        apiUrl,
        secretKey,
        jwtKey,
        proxyUrl,
        isSatellite,
        domain,
        publishableKey,
        userAgent: `${"@clerk/remix"}@${"4.8.4"}`
      }).authenticateRequest((0, import_utils.patchRequest)(request), {
        audience,
        authorizedParties,
        signInUrl,
        signUpUrl,
        afterSignInUrl,
        afterSignUpUrl
      });
      const locationHeader = requestState.headers.get(import_internal.constants.Headers.Location);
      if (locationHeader) {
        (0, import_netlifyCacheHandler.handleNetlifyCacheInDevInstance)({
          locationHeader,
          requestStateHeaders: requestState.headers,
          publishableKey: requestState.publishableKey
        });
        throw new Response(null, { status: 307, headers: requestState.headers });
      }
      if (requestState.status === import_internal.AuthStatus.Handshake) {
        throw new Error("Clerk: unexpected handshake without redirect");
      }
      return requestState;
    }
  }
});

// node_modules/@clerk/shared/dist/apiUrlFromPublishableKey.js
var require_apiUrlFromPublishableKey = __commonJS({
  "node_modules/@clerk/shared/dist/apiUrlFromPublishableKey.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var apiUrlFromPublishableKey_exports = {};
    __export2(apiUrlFromPublishableKey_exports, {
      apiUrlFromPublishableKey: () => apiUrlFromPublishableKey
    });
    module.exports = __toCommonJS2(apiUrlFromPublishableKey_exports);
    var LEGACY_DEV_INSTANCE_SUFFIXES = [".lcl.dev", ".lclstage.dev", ".lclclerk.com"];
    var LOCAL_ENV_SUFFIXES = [".lcl.dev", "lclstage.dev", ".lclclerk.com", ".accounts.lclclerk.com"];
    var STAGING_ENV_SUFFIXES = [".accountsstage.dev"];
    var LOCAL_API_URL = "https://api.lclclerk.com";
    var STAGING_API_URL = "https://api.clerkstage.dev";
    var PROD_API_URL = "https://api.clerk.com";
    var isomorphicAtob = (data) => {
      if (typeof atob !== "undefined" && typeof atob === "function") {
        return atob(data);
      } else if (typeof global !== "undefined" && global.Buffer) {
        return new global.Buffer(data, "base64").toString();
      }
      return data;
    };
    var PUBLISHABLE_KEY_LIVE_PREFIX = "pk_live_";
    var PUBLISHABLE_KEY_TEST_PREFIX = "pk_test_";
    function parsePublishableKey(key, options = {}) {
      key = key || "";
      if (!key || !isPublishableKey(key)) {
        if (options.fatal && !key) {
          throw new Error(
            "Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys"
          );
        }
        if (options.fatal && !isPublishableKey(key)) {
          throw new Error("Publishable key not valid.");
        }
        return null;
      }
      const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? "production" : "development";
      let frontendApi = isomorphicAtob(key.split("_")[2]);
      frontendApi = frontendApi.slice(0, -1);
      if (options.proxyUrl) {
        frontendApi = options.proxyUrl;
      } else if (instanceType !== "development" && options.domain && options.isSatellite) {
        frontendApi = `clerk.${options.domain}`;
      }
      return {
        instanceType,
        frontendApi
      };
    }
    function isPublishableKey(key = "") {
      try {
        const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);
        const hasValidFrontendApiPostfix = isomorphicAtob(key.split("_")[2] || "").endsWith("$");
        return hasValidPrefix && hasValidFrontendApiPostfix;
      } catch {
        return false;
      }
    }
    var apiUrlFromPublishableKey = (publishableKey) => {
      var _a;
      const frontendApi = (_a = parsePublishableKey(publishableKey)) == null ? void 0 : _a.frontendApi;
      if ((frontendApi == null ? void 0 : frontendApi.startsWith("clerk.")) && LEGACY_DEV_INSTANCE_SUFFIXES.some((suffix) => frontendApi == null ? void 0 : frontendApi.endsWith(suffix))) {
        return PROD_API_URL;
      }
      if (LOCAL_ENV_SUFFIXES.some((suffix) => frontendApi == null ? void 0 : frontendApi.endsWith(suffix))) {
        return LOCAL_API_URL;
      }
      if (STAGING_ENV_SUFFIXES.some((suffix) => frontendApi == null ? void 0 : frontendApi.endsWith(suffix))) {
        return STAGING_API_URL;
      }
      return PROD_API_URL;
    };
  }
});

// node_modules/@clerk/shared/dist/proxy.js
var require_proxy = __commonJS({
  "node_modules/@clerk/shared/dist/proxy.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var proxy_exports = {};
    __export2(proxy_exports, {
      isHttpOrHttps: () => isHttpOrHttps,
      isProxyUrlRelative: () => isProxyUrlRelative,
      isValidProxyUrl: () => isValidProxyUrl,
      proxyUrlToAbsoluteURL: () => proxyUrlToAbsoluteURL
    });
    module.exports = __toCommonJS2(proxy_exports);
    function isValidProxyUrl(key) {
      if (!key) {
        return true;
      }
      return isHttpOrHttps(key) || isProxyUrlRelative(key);
    }
    function isHttpOrHttps(key) {
      return /^http(s)?:\/\//.test(key || "");
    }
    function isProxyUrlRelative(key) {
      return key.startsWith("/");
    }
    function proxyUrlToAbsoluteURL(url) {
      if (!url) {
        return "";
      }
      return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;
    }
  }
});

// node_modules/@clerk/remix/dist/ssr/loadOptions.js
var require_loadOptions = __commonJS({
  "node_modules/@clerk/remix/dist/ssr/loadOptions.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var loadOptions_exports = {};
    __export2(loadOptions_exports, {
      loadOptions: () => loadOptions
    });
    module.exports = __toCommonJS2(loadOptions_exports);
    var import_internal = require_internal();
    var import_apiUrlFromPublishableKey = require_apiUrlFromPublishableKey();
    var import_keys = require_keys();
    var import_proxy = require_proxy();
    var import_underscore = require_underscore();
    var import_utils = require_utils();
    var import_errors = require_errors();
    var import_utils2 = require_utils2();
    var import_utils3 = require_utils3();
    var loadOptions = (args, overrides = {}) => {
      const { request, context } = args;
      const clerkRequest = (0, import_internal.createClerkRequest)((0, import_utils3.patchRequest)(request));
      const secretKey = overrides.secretKey || (0, import_utils2.getEnvVariable)("CLERK_SECRET_KEY", context) || "";
      const publishableKey = overrides.publishableKey || (0, import_utils2.getEnvVariable)("CLERK_PUBLISHABLE_KEY", context) || "";
      const jwtKey = overrides.jwtKey || (0, import_utils2.getEnvVariable)("CLERK_JWT_KEY", context);
      const apiUrl = (0, import_utils2.getEnvVariable)("CLERK_API_URL", context) || (0, import_apiUrlFromPublishableKey.apiUrlFromPublishableKey)(publishableKey);
      const domain = (0, import_utils.handleValueOrFn)(overrides.domain, new URL(request.url)) || (0, import_utils2.getEnvVariable)("CLERK_DOMAIN", context) || "";
      const isSatellite = (0, import_utils.handleValueOrFn)(overrides.isSatellite, new URL(request.url)) || (0, import_underscore.isTruthy)((0, import_utils2.getEnvVariable)("CLERK_IS_SATELLITE", context));
      const relativeOrAbsoluteProxyUrl = (0, import_utils.handleValueOrFn)(
        overrides == null ? void 0 : overrides.proxyUrl,
        clerkRequest.clerkUrl,
        (0, import_utils2.getEnvVariable)("CLERK_PROXY_URL", context)
      );
      const signInUrl = overrides.signInUrl || (0, import_utils2.getEnvVariable)("CLERK_SIGN_IN_URL", context) || "";
      const signUpUrl = overrides.signUpUrl || (0, import_utils2.getEnvVariable)("CLERK_SIGN_UP_URL", context) || "";
      const signInForceRedirectUrl = overrides.signInForceRedirectUrl || (0, import_utils2.getEnvVariable)("CLERK_SIGN_IN_FORCE_REDIRECT_URL", context) || "";
      const signUpForceRedirectUrl = overrides.signUpForceRedirectUrl || (0, import_utils2.getEnvVariable)("CLERK_SIGN_UP_FORCE_REDIRECT_URL", context) || "";
      const signInFallbackRedirectUrl = overrides.signInFallbackRedirectUrl || (0, import_utils2.getEnvVariable)("CLERK_SIGN_IN_FALLBACK_REDIRECT_URL", context) || "";
      const signUpFallbackRedirectUrl = overrides.signUpFallbackRedirectUrl || (0, import_utils2.getEnvVariable)("CLERK_SIGN_UP_FALLBACK_REDIRECT_URL", context) || "";
      const afterSignInUrl = overrides.afterSignInUrl || (0, import_utils2.getEnvVariable)("CLERK_AFTER_SIGN_IN_URL", context) || "";
      const afterSignUpUrl = overrides.afterSignUpUrl || (0, import_utils2.getEnvVariable)("CLERK_AFTER_SIGN_UP_URL", context) || "";
      const newSubscriptionRedirectUrl = overrides.newSubscriptionRedirectUrl || (0, import_utils2.getEnvVariable)("CLERK_CHECKOUT_CONTINUE_URL", context) || "";
      let proxyUrl;
      if (!!relativeOrAbsoluteProxyUrl && (0, import_proxy.isProxyUrlRelative)(relativeOrAbsoluteProxyUrl)) {
        proxyUrl = new URL(relativeOrAbsoluteProxyUrl, clerkRequest.clerkUrl).toString();
      } else {
        proxyUrl = relativeOrAbsoluteProxyUrl;
      }
      if (!secretKey) {
        throw new Error(import_errors.noSecretKeyError);
      }
      if (isSatellite && !proxyUrl && !domain) {
        throw new Error(import_errors.satelliteAndMissingProxyUrlAndDomain);
      }
      if (isSatellite && !(0, import_proxy.isHttpOrHttps)(signInUrl) && (0, import_keys.isDevelopmentFromSecretKey)(secretKey)) {
        throw new Error(import_errors.satelliteAndMissingSignInUrl);
      }
      return {
        // used to append options that are not initialized from env
        ...overrides,
        secretKey,
        publishableKey,
        jwtKey,
        apiUrl,
        domain,
        isSatellite,
        proxyUrl,
        signInUrl,
        signUpUrl,
        afterSignInUrl,
        afterSignUpUrl,
        signInForceRedirectUrl,
        signUpForceRedirectUrl,
        signInFallbackRedirectUrl,
        signUpFallbackRedirectUrl,
        newSubscriptionRedirectUrl
      };
    };
  }
});

// node_modules/@clerk/remix/dist/ssr/rootAuthLoader.js
var require_rootAuthLoader = __commonJS({
  "node_modules/@clerk/remix/dist/ssr/rootAuthLoader.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var rootAuthLoader_exports = {};
    __export2(rootAuthLoader_exports, {
      rootAuthLoader: () => rootAuthLoader
    });
    module.exports = __toCommonJS2(rootAuthLoader_exports);
    var import_internal = require_internal();
    var import_responses = require_responses();
    var import_errors = require_errors();
    var import_authenticateRequest = require_authenticateRequest();
    var import_loadOptions = require_loadOptions();
    var import_utils = require_utils3();
    var rootAuthLoader = async (args, handlerOrOptions, options) => {
      const handler = typeof handlerOrOptions === "function" ? handlerOrOptions : void 0;
      const opts = options ? options : !!handlerOrOptions && typeof handlerOrOptions !== "function" ? handlerOrOptions : {};
      const loadedOptions = (0, import_loadOptions.loadOptions)(args, opts);
      const _requestState = await (0, import_authenticateRequest.authenticateRequest)(args, loadedOptions);
      const requestState = { ...loadedOptions, ..._requestState };
      if (!handler) {
        return (0, import_utils.injectRequestStateIntoResponse)(new Response(JSON.stringify({})), requestState, args.context);
      }
      const authObj = requestState.toAuth();
      const requestWithAuth = Object.assign(args.request, { auth: authObj });
      await (0, import_internal.decorateObjectWithResources)(requestWithAuth, authObj, loadedOptions);
      const handlerResult = await handler(args);
      (0, import_utils.assertValidHandlerResult)(handlerResult, import_errors.invalidRootLoaderCallbackReturn);
      if ((0, import_responses.isDeferredData)(handlerResult)) {
        return (0, import_utils.injectRequestStateIntoDeferredData)(
          // This is necessary because the DeferredData type is not exported from remix.
          handlerResult,
          requestState,
          args.context
        );
      }
      if ((0, import_utils.isResponse)(handlerResult)) {
        try {
          if ((0, import_utils.isRedirect)(handlerResult)) {
            return handlerResult;
          }
          return (0, import_utils.injectRequestStateIntoResponse)(handlerResult, requestState, args.context);
        } catch {
          throw new Error(import_errors.invalidRootLoaderCallbackReturn);
        }
      }
      const responseBody = JSON.stringify(handlerResult != null ? handlerResult : {});
      return (0, import_utils.injectRequestStateIntoResponse)(new Response(responseBody), requestState, args.context);
    };
  }
});

// node_modules/@clerk/remix/dist/ssr/getAuth.js
var require_getAuth = __commonJS({
  "node_modules/@clerk/remix/dist/ssr/getAuth.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export2 = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var getAuth_exports = {};
    __export2(getAuth_exports, {
      getAuth: () => getAuth
    });
    module.exports = __toCommonJS2(getAuth_exports);
    var import_internal = require_internal();
    var import_errors = require_errors();
    var import_authenticateRequest = require_authenticateRequest();
    var import_loadOptions = require_loadOptions();
    async function getAuth(args, opts) {
      if (!args || args && (!args.request || !args.context)) {
        throw new Error(import_errors.noLoaderArgsPassedInGetAuth);
      }
      const loadedOptions = (0, import_loadOptions.loadOptions)(args, opts);
      const requestState = await (0, import_authenticateRequest.authenticateRequest)(args, loadedOptions);
      return (0, import_internal.stripPrivateDataFromObject)(requestState.toAuth());
    }
  }
});

// node_modules/@clerk/remix/dist/ssr/index.js
var require_ssr = __commonJS({
  "node_modules/@clerk/remix/dist/ssr/index.js"(exports, module) {
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
    var __toCommonJS2 = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var ssr_exports = {};
    module.exports = __toCommonJS2(ssr_exports);
    __reExport(ssr_exports, require_rootAuthLoader(), module.exports);
    __reExport(ssr_exports, require_getAuth(), module.exports);
  }
});
export default require_ssr();
/*! Bundled license information:

@remix-run/server-runtime/dist/mode.js:
  (**
   * @remix-run/server-runtime v2.16.8
   *
   * Copyright (c) Remix Software Inc.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@remix-run/server-runtime/dist/errors.js:
  (**
   * @remix-run/server-runtime v2.16.8
   *
   * Copyright (c) Remix Software Inc.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@remix-run/server-runtime/dist/responses.js:
  (**
   * @remix-run/server-runtime v2.16.8
   *
   * Copyright (c) Remix Software Inc.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)
*/
//# sourceMappingURL=@clerk_remix_ssr__server.js.map
