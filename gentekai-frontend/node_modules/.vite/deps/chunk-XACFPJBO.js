import {
  __commonJS
} from "./chunk-QGSYD46Z.js";

// node_modules/@clerk/shared/dist/error.js
var require_error = __commonJS({
  "node_modules/@clerk/shared/dist/error.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var error_exports = {};
    __export(error_exports, {
      ClerkAPIResponseError: () => ClerkAPIResponseError,
      ClerkRuntimeError: () => ClerkRuntimeError,
      ClerkWebAuthnError: () => ClerkWebAuthnError,
      EmailLinkError: () => EmailLinkError,
      EmailLinkErrorCode: () => EmailLinkErrorCode,
      EmailLinkErrorCodeStatus: () => EmailLinkErrorCodeStatus,
      buildErrorThrower: () => buildErrorThrower,
      errorToJSON: () => errorToJSON,
      is4xxError: () => is4xxError,
      isCaptchaError: () => isCaptchaError,
      isClerkAPIResponseError: () => isClerkAPIResponseError,
      isClerkRuntimeError: () => isClerkRuntimeError,
      isEmailLinkError: () => isEmailLinkError,
      isKnownError: () => isKnownError,
      isMetamaskError: () => isMetamaskError,
      isNetworkError: () => isNetworkError,
      isPasswordPwnedError: () => isPasswordPwnedError,
      isReverificationCancelledError: () => isReverificationCancelledError,
      isUnauthorizedError: () => isUnauthorizedError,
      isUserLockedError: () => isUserLockedError,
      parseError: () => parseError,
      parseErrors: () => parseErrors
    });
    module.exports = __toCommonJS(error_exports);
    function isUnauthorizedError(e) {
      var _a, _b;
      const status = e == null ? void 0 : e.status;
      const code = (_b = (_a = e == null ? void 0 : e.errors) == null ? void 0 : _a[0]) == null ? void 0 : _b.code;
      return code === "authentication_invalid" && status === 401;
    }
    function isCaptchaError(e) {
      return ["captcha_invalid", "captcha_not_enabled", "captcha_missing_token"].includes(e.errors[0].code);
    }
    function is4xxError(e) {
      const status = e == null ? void 0 : e.status;
      return !!status && status >= 400 && status < 500;
    }
    function isNetworkError(e) {
      const message = (`${e.message}${e.name}` || "").toLowerCase().replace(/\s+/g, "");
      return message.includes("networkerror");
    }
    function isKnownError(error) {
      return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);
    }
    function isClerkAPIResponseError(err) {
      return "clerkError" in err;
    }
    function isClerkRuntimeError(err) {
      return "clerkRuntimeError" in err;
    }
    function isReverificationCancelledError(err) {
      return isClerkRuntimeError(err) && err.code === "reverification_cancelled";
    }
    function isMetamaskError(err) {
      return "code" in err && [4001, 32602, 32603].includes(err.code) && "message" in err;
    }
    function isUserLockedError(err) {
      var _a, _b;
      return isClerkAPIResponseError(err) && ((_b = (_a = err.errors) == null ? void 0 : _a[0]) == null ? void 0 : _b.code) === "user_locked";
    }
    function isPasswordPwnedError(err) {
      var _a, _b;
      return isClerkAPIResponseError(err) && ((_b = (_a = err.errors) == null ? void 0 : _a[0]) == null ? void 0 : _b.code) === "form_password_pwned";
    }
    function parseErrors(data = []) {
      return data.length > 0 ? data.map(parseError) : [];
    }
    function parseError(error) {
      var _a, _b, _c, _d, _e, _f;
      return {
        code: error.code,
        message: error.message,
        longMessage: error.long_message,
        meta: {
          paramName: (_a = error == null ? void 0 : error.meta) == null ? void 0 : _a.param_name,
          sessionId: (_b = error == null ? void 0 : error.meta) == null ? void 0 : _b.session_id,
          emailAddresses: (_c = error == null ? void 0 : error.meta) == null ? void 0 : _c.email_addresses,
          identifiers: (_d = error == null ? void 0 : error.meta) == null ? void 0 : _d.identifiers,
          zxcvbn: (_e = error == null ? void 0 : error.meta) == null ? void 0 : _e.zxcvbn,
          plan: (_f = error == null ? void 0 : error.meta) == null ? void 0 : _f.plan
        }
      };
    }
    function errorToJSON(error) {
      var _a, _b, _c, _d, _e, _f;
      return {
        code: (error == null ? void 0 : error.code) || "",
        message: (error == null ? void 0 : error.message) || "",
        long_message: error == null ? void 0 : error.longMessage,
        meta: {
          param_name: (_a = error == null ? void 0 : error.meta) == null ? void 0 : _a.paramName,
          session_id: (_b = error == null ? void 0 : error.meta) == null ? void 0 : _b.sessionId,
          email_addresses: (_c = error == null ? void 0 : error.meta) == null ? void 0 : _c.emailAddresses,
          identifiers: (_d = error == null ? void 0 : error.meta) == null ? void 0 : _d.identifiers,
          zxcvbn: (_e = error == null ? void 0 : error.meta) == null ? void 0 : _e.zxcvbn,
          plan: (_f = error == null ? void 0 : error.meta) == null ? void 0 : _f.plan
        }
      };
    }
    var ClerkAPIResponseError = class _ClerkAPIResponseError extends Error {
      constructor(message, { data, status, clerkTraceId, retryAfter }) {
        super(message);
        this.toString = () => {
          let message2 = `[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(
            (e) => JSON.stringify(e)
          )}`;
          if (this.clerkTraceId) {
            message2 += `
Clerk Trace ID: ${this.clerkTraceId}`;
          }
          return message2;
        };
        Object.setPrototypeOf(this, _ClerkAPIResponseError.prototype);
        this.status = status;
        this.message = message;
        this.clerkTraceId = clerkTraceId;
        this.retryAfter = retryAfter;
        this.clerkError = true;
        this.errors = parseErrors(data);
      }
    };
    var ClerkRuntimeError = class _ClerkRuntimeError extends Error {
      constructor(message, { code }) {
        const prefix = "🔒 Clerk:";
        const regex = new RegExp(prefix.replace(" ", "\\s*"), "i");
        const sanitized = message.replace(regex, "");
        const _message = `${prefix} ${sanitized.trim()}

(code="${code}")
`;
        super(_message);
        this.toString = () => {
          return `[${this.name}]
Message:${this.message}`;
        };
        Object.setPrototypeOf(this, _ClerkRuntimeError.prototype);
        this.code = code;
        this.message = _message;
        this.clerkRuntimeError = true;
        this.name = "ClerkRuntimeError";
      }
    };
    var EmailLinkError = class _EmailLinkError extends Error {
      constructor(code) {
        super(code);
        this.code = code;
        this.name = "EmailLinkError";
        Object.setPrototypeOf(this, _EmailLinkError.prototype);
      }
    };
    function isEmailLinkError(err) {
      return err.name === "EmailLinkError";
    }
    var EmailLinkErrorCode = {
      Expired: "expired",
      Failed: "failed",
      ClientMismatch: "client_mismatch"
    };
    var EmailLinkErrorCodeStatus = {
      Expired: "expired",
      Failed: "failed",
      ClientMismatch: "client_mismatch"
    };
    var DefaultMessages = Object.freeze({
      InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,
      InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,
      MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,
      MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,
      MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`
    });
    function buildErrorThrower({ packageName, customMessages }) {
      let pkg = packageName;
      const messages = {
        ...DefaultMessages,
        ...customMessages
      };
      function buildMessage(rawMessage, replacements) {
        if (!replacements) {
          return `${pkg}: ${rawMessage}`;
        }
        let msg = rawMessage;
        const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);
        for (const match of matches) {
          const replacement = (replacements[match[1]] || "").toString();
          msg = msg.replace(`{{${match[1]}}}`, replacement);
        }
        return `${pkg}: ${msg}`;
      }
      return {
        setPackageName({ packageName: packageName2 }) {
          if (typeof packageName2 === "string") {
            pkg = packageName2;
          }
          return this;
        },
        setMessages({ customMessages: customMessages2 }) {
          Object.assign(messages, customMessages2 || {});
          return this;
        },
        throwInvalidPublishableKeyError(params) {
          throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));
        },
        throwInvalidProxyUrl(params) {
          throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));
        },
        throwMissingPublishableKeyError() {
          throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));
        },
        throwMissingSecretKeyError() {
          throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));
        },
        throwMissingClerkProviderError(params) {
          throw new Error(buildMessage(messages.MissingClerkProvider, params));
        },
        throw(message) {
          throw new Error(buildMessage(message));
        }
      };
    }
    var ClerkWebAuthnError = class extends ClerkRuntimeError {
      constructor(message, { code }) {
        super(message, { code });
        this.code = code;
      }
    };
  }
});

// node_modules/@clerk/shared/dist/deprecated.js
var require_deprecated = __commonJS({
  "node_modules/@clerk/shared/dist/deprecated.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var deprecated_exports = {};
    __export(deprecated_exports, {
      deprecated: () => deprecated,
      deprecatedObjectProperty: () => deprecatedObjectProperty,
      deprecatedProperty: () => deprecatedProperty
    });
    module.exports = __toCommonJS(deprecated_exports);
    var isTestEnvironment = () => {
      try {
        return false;
      } catch {
      }
      return false;
    };
    var isProductionEnvironment = () => {
      try {
        return false;
      } catch {
      }
      return false;
    };
    var displayedWarnings = /* @__PURE__ */ new Set();
    var deprecated = (fnName, warning, key) => {
      const hideWarning = isTestEnvironment() || isProductionEnvironment();
      const messageId = key ?? fnName;
      if (displayedWarnings.has(messageId) || hideWarning) {
        return;
      }
      displayedWarnings.add(messageId);
      console.warn(
        `Clerk - DEPRECATION WARNING: "${fnName}" is deprecated and will be removed in the next major release.
${warning}`
      );
    };
    var deprecatedProperty = (cls, propName, warning, isStatic = false) => {
      const target = isStatic ? cls : cls.prototype;
      let value = target[propName];
      Object.defineProperty(target, propName, {
        get() {
          deprecated(propName, warning, `${cls.name}:${propName}`);
          return value;
        },
        set(v) {
          value = v;
        }
      });
    };
    var deprecatedObjectProperty = (obj, propName, warning, key) => {
      let value = obj[propName];
      Object.defineProperty(obj, propName, {
        get() {
          deprecated(propName, warning, key);
          return value;
        },
        set(v) {
          value = v;
        }
      });
    };
  }
});

// node_modules/@clerk/shared/dist/authorization.js
var require_authorization = __commonJS({
  "node_modules/@clerk/shared/dist/authorization.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var authorization_exports = {};
    __export(authorization_exports, {
      createCheckAuthorization: () => createCheckAuthorization,
      resolveAuthState: () => resolveAuthState,
      splitByScope: () => splitByScope,
      validateReverificationConfig: () => validateReverificationConfig
    });
    module.exports = __toCommonJS(authorization_exports);
    var TYPES_TO_OBJECTS = {
      strict_mfa: {
        afterMinutes: 10,
        level: "multi_factor"
      },
      strict: {
        afterMinutes: 10,
        level: "second_factor"
      },
      moderate: {
        afterMinutes: 60,
        level: "second_factor"
      },
      lax: {
        afterMinutes: 1440,
        level: "second_factor"
      }
    };
    var ALLOWED_LEVELS = /* @__PURE__ */ new Set(["first_factor", "second_factor", "multi_factor"]);
    var ALLOWED_TYPES = /* @__PURE__ */ new Set(["strict_mfa", "strict", "moderate", "lax"]);
    var isValidMaxAge = (maxAge) => typeof maxAge === "number" && maxAge > 0;
    var isValidLevel = (level) => ALLOWED_LEVELS.has(level);
    var isValidVerificationType = (type) => ALLOWED_TYPES.has(type);
    var prefixWithOrg = (value) => value.replace(/^(org:)*/, "org:");
    var checkOrgAuthorization = (params, options) => {
      const { orgId, orgRole, orgPermissions } = options;
      if (!params.role && !params.permission) {
        return null;
      }
      if (!orgId || !orgRole || !orgPermissions) {
        return null;
      }
      if (params.permission) {
        return orgPermissions.includes(prefixWithOrg(params.permission));
      }
      if (params.role) {
        return prefixWithOrg(orgRole) === prefixWithOrg(params.role);
      }
      return null;
    };
    var checkForFeatureOrPlan = (claim, featureOrPlan) => {
      const { org: orgFeatures, user: userFeatures } = splitByScope(claim);
      const [scope, _id] = featureOrPlan.split(":");
      const id = _id || scope;
      if (scope === "org") {
        return orgFeatures.includes(id);
      } else if (scope === "user") {
        return userFeatures.includes(id);
      } else {
        return [...orgFeatures, ...userFeatures].includes(id);
      }
    };
    var checkBillingAuthorization = (params, options) => {
      const { features, plans } = options;
      if (params.feature && features) {
        return checkForFeatureOrPlan(features, params.feature);
      }
      if (params.plan && plans) {
        return checkForFeatureOrPlan(plans, params.plan);
      }
      return null;
    };
    var splitByScope = (fea) => {
      const features = fea ? fea.split(",").map((f) => f.trim()) : [];
      return {
        org: features.filter((f) => f.split(":")[0].includes("o")).map((f) => f.split(":")[1]),
        user: features.filter((f) => f.split(":")[0].includes("u")).map((f) => f.split(":")[1])
      };
    };
    var validateReverificationConfig = (config) => {
      if (!config) {
        return false;
      }
      const convertConfigToObject = (config2) => {
        if (typeof config2 === "string") {
          return TYPES_TO_OBJECTS[config2];
        }
        return config2;
      };
      const isValidStringValue = typeof config === "string" && isValidVerificationType(config);
      const isValidObjectValue = typeof config === "object" && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);
      if (isValidStringValue || isValidObjectValue) {
        return convertConfigToObject.bind(null, config);
      }
      return false;
    };
    var checkReverificationAuthorization = (params, { factorVerificationAge }) => {
      if (!params.reverification || !factorVerificationAge) {
        return null;
      }
      const isValidReverification = validateReverificationConfig(params.reverification);
      if (!isValidReverification) {
        return null;
      }
      const { level, afterMinutes } = isValidReverification();
      const [factor1Age, factor2Age] = factorVerificationAge;
      const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;
      const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;
      switch (level) {
        case "first_factor":
          return isValidFactor1;
        case "second_factor":
          return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;
        case "multi_factor":
          return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;
      }
    };
    var createCheckAuthorization = (options) => {
      return (params) => {
        if (!options.userId) {
          return false;
        }
        const billingAuthorization = checkBillingAuthorization(params, options);
        const orgAuthorization = checkOrgAuthorization(params, options);
        const reverificationAuthorization = checkReverificationAuthorization(params, options);
        if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some((a) => a === null)) {
          return [billingAuthorization || orgAuthorization, reverificationAuthorization].some((a) => a === true);
        }
        return [billingAuthorization || orgAuthorization, reverificationAuthorization].every((a) => a === true);
      };
    };
    var resolveAuthState = ({
      authObject: {
        sessionId,
        sessionStatus,
        userId,
        actor,
        orgId,
        orgRole,
        orgSlug,
        signOut,
        getToken,
        has,
        sessionClaims
      },
      options: { treatPendingAsSignedOut = true }
    }) => {
      if (sessionId === void 0 && userId === void 0) {
        return {
          isLoaded: false,
          isSignedIn: void 0,
          sessionId,
          sessionClaims: void 0,
          userId,
          actor: void 0,
          orgId: void 0,
          orgRole: void 0,
          orgSlug: void 0,
          has: void 0,
          signOut,
          getToken
        };
      }
      if (sessionId === null && userId === null) {
        return {
          isLoaded: true,
          isSignedIn: false,
          sessionId,
          userId,
          sessionClaims: null,
          actor: null,
          orgId: null,
          orgRole: null,
          orgSlug: null,
          has: () => false,
          signOut,
          getToken
        };
      }
      if (treatPendingAsSignedOut && sessionStatus === "pending") {
        return {
          isLoaded: true,
          isSignedIn: false,
          sessionId: null,
          userId: null,
          sessionClaims: null,
          actor: null,
          orgId: null,
          orgRole: null,
          orgSlug: null,
          has: () => false,
          signOut,
          getToken
        };
      }
      if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {
        return {
          isLoaded: true,
          isSignedIn: true,
          sessionId,
          sessionClaims,
          userId,
          actor: actor || null,
          orgId,
          orgRole,
          orgSlug: orgSlug || null,
          has,
          signOut,
          getToken
        };
      }
      if (!!sessionId && !!sessionClaims && !!userId && !orgId) {
        return {
          isLoaded: true,
          isSignedIn: true,
          sessionId,
          sessionClaims,
          userId,
          actor: actor || null,
          orgId: null,
          orgRole: null,
          orgSlug: null,
          has,
          signOut,
          getToken
        };
      }
    };
  }
});

// node_modules/@clerk/shared/dist/telemetry.js
var require_telemetry = __commonJS({
  "node_modules/@clerk/shared/dist/telemetry.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __typeError = (msg) => {
      throw TypeError(msg);
    };
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var __accessCheck = (obj, member, msg) => member.has(obj) || __typeError("Cannot " + msg);
    var __privateGet = (obj, member, getter) => (__accessCheck(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
    var __privateAdd = (obj, member, value) => member.has(obj) ? __typeError("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
    var __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, "write to private field"), setter ? setter.call(obj, value) : member.set(obj, value), value);
    var __privateMethod = (obj, member, method) => (__accessCheck(obj, member, "access private method"), method);
    var telemetry_exports = {};
    __export(telemetry_exports, {
      TelemetryCollector: () => TelemetryCollector,
      eventComponentMounted: () => eventComponentMounted,
      eventFrameworkMetadata: () => eventFrameworkMetadata,
      eventMethodCalled: () => eventMethodCalled,
      eventPrebuiltComponentMounted: () => eventPrebuiltComponentMounted,
      eventPrebuiltComponentOpened: () => eventPrebuiltComponentOpened
    });
    module.exports = __toCommonJS(telemetry_exports);
    var isomorphicAtob = (data) => {
      if (typeof atob !== "undefined" && typeof atob === "function") {
        return atob(data);
      } else if (typeof global !== "undefined" && global.Buffer) {
        return new global.Buffer(data, "base64").toString();
      }
      return data;
    };
    var PUBLISHABLE_KEY_LIVE_PREFIX = "pk_live_";
    var PUBLISHABLE_KEY_TEST_PREFIX = "pk_test_";
    function parsePublishableKey(key, options = {}) {
      key = key || "";
      if (!key || !isPublishableKey(key)) {
        if (options.fatal && !key) {
          throw new Error(
            "Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys"
          );
        }
        if (options.fatal && !isPublishableKey(key)) {
          throw new Error("Publishable key not valid.");
        }
        return null;
      }
      const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? "production" : "development";
      let frontendApi = isomorphicAtob(key.split("_")[2]);
      frontendApi = frontendApi.slice(0, -1);
      if (options.proxyUrl) {
        frontendApi = options.proxyUrl;
      } else if (instanceType !== "development" && options.domain && options.isSatellite) {
        frontendApi = `clerk.${options.domain}`;
      }
      return {
        instanceType,
        frontendApi
      };
    }
    function isPublishableKey(key = "") {
      try {
        const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);
        const hasValidFrontendApiPostfix = isomorphicAtob(key.split("_")[2] || "").endsWith("$");
        return hasValidPrefix && hasValidFrontendApiPostfix;
      } catch {
        return false;
      }
    }
    function snakeToCamel(str) {
      return str ? str.replace(/([-_][a-z])/g, (match) => match.toUpperCase().replace(/-|_/, "")) : "";
    }
    function camelToSnake(str) {
      return str ? str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`) : "";
    }
    var createDeepObjectTransformer = (transform) => {
      const deepTransform = (obj) => {
        if (!obj) {
          return obj;
        }
        if (Array.isArray(obj)) {
          return obj.map((el) => {
            if (typeof el === "object" || Array.isArray(el)) {
              return deepTransform(el);
            }
            return el;
          });
        }
        const copy = { ...obj };
        const keys = Object.keys(copy);
        for (const oldName of keys) {
          const newName = transform(oldName.toString());
          if (newName !== oldName) {
            copy[newName] = copy[oldName];
            delete copy[oldName];
          }
          if (typeof copy[newName] === "object") {
            copy[newName] = deepTransform(copy[newName]);
          }
        }
        return copy;
      };
      return deepTransform;
    };
    var deepCamelToSnake = createDeepObjectTransformer(camelToSnake);
    var deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);
    function isTruthy(value) {
      if (typeof value === `boolean`) {
        return value;
      }
      if (value === void 0 || value === null) {
        return false;
      }
      if (typeof value === `string`) {
        if (value.toLowerCase() === `true`) {
          return true;
        }
        if (value.toLowerCase() === `false`) {
          return false;
        }
      }
      const number = parseInt(value, 10);
      if (isNaN(number)) {
        return false;
      }
      if (number > 0) {
        return true;
      }
      return false;
    }
    var DEFAULT_CACHE_TTL_MS = 864e5;
    var _storageKey;
    var _cacheTtl;
    var _TelemetryEventThrottler_instances;
    var generateKey_fn;
    var cache_get;
    var isValidBrowser_get;
    var TelemetryEventThrottler = class {
      constructor() {
        __privateAdd(this, _TelemetryEventThrottler_instances);
        __privateAdd(this, _storageKey, "clerk_telemetry_throttler");
        __privateAdd(this, _cacheTtl, DEFAULT_CACHE_TTL_MS);
      }
      isEventThrottled(payload) {
        var _a;
        if (!__privateGet(this, _TelemetryEventThrottler_instances, isValidBrowser_get)) {
          return false;
        }
        const now = Date.now();
        const key = __privateMethod(this, _TelemetryEventThrottler_instances, generateKey_fn).call(this, payload);
        const entry = (_a = __privateGet(this, _TelemetryEventThrottler_instances, cache_get)) == null ? void 0 : _a[key];
        if (!entry) {
          const updatedCache = {
            ...__privateGet(this, _TelemetryEventThrottler_instances, cache_get),
            [key]: now
          };
          localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));
        }
        const shouldInvalidate = entry && now - entry > __privateGet(this, _cacheTtl);
        if (shouldInvalidate) {
          const updatedCache = __privateGet(this, _TelemetryEventThrottler_instances, cache_get);
          delete updatedCache[key];
          localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));
        }
        return !!entry;
      }
    };
    _storageKey = /* @__PURE__ */ new WeakMap();
    _cacheTtl = /* @__PURE__ */ new WeakMap();
    _TelemetryEventThrottler_instances = /* @__PURE__ */ new WeakSet();
    generateKey_fn = function(event) {
      const { sk: _sk, pk: _pk, payload, ...rest } = event;
      const sanitizedEvent = {
        ...payload,
        ...rest
      };
      return JSON.stringify(
        Object.keys({
          ...payload,
          ...rest
        }).sort().map((key) => sanitizedEvent[key])
      );
    };
    cache_get = function() {
      const cacheString = localStorage.getItem(__privateGet(this, _storageKey));
      if (!cacheString) {
        return {};
      }
      return JSON.parse(cacheString);
    };
    isValidBrowser_get = function() {
      if (typeof window === "undefined") {
        return false;
      }
      const storage = window.localStorage;
      if (!storage) {
        return false;
      }
      try {
        const testKey = "test";
        storage.setItem(testKey, testKey);
        storage.removeItem(testKey);
        return true;
      } catch (err) {
        const isQuotaExceededError = err instanceof DOMException && // Check error names for different browsers
        (err.name === "QuotaExceededError" || err.name === "NS_ERROR_DOM_QUOTA_REACHED");
        if (isQuotaExceededError && storage.length > 0) {
          storage.removeItem(__privateGet(this, _storageKey));
        }
        return false;
      }
    };
    var DEFAULT_CONFIG = {
      samplingRate: 1,
      maxBufferSize: 5,
      // Production endpoint: https://clerk-telemetry.com
      // Staging endpoint: https://staging.clerk-telemetry.com
      // Local: http://localhost:8787
      endpoint: "https://clerk-telemetry.com"
    };
    var _config;
    var _eventThrottler;
    var _metadata;
    var _buffer;
    var _pendingFlush;
    var _TelemetryCollector_instances;
    var shouldRecord_fn;
    var shouldBeSampled_fn;
    var scheduleFlush_fn;
    var flush_fn;
    var logEvent_fn;
    var getSDKMetadata_fn;
    var preparePayload_fn;
    var TelemetryCollector = class {
      constructor(options) {
        __privateAdd(this, _TelemetryCollector_instances);
        __privateAdd(this, _config);
        __privateAdd(this, _eventThrottler);
        __privateAdd(this, _metadata, {});
        __privateAdd(this, _buffer, []);
        __privateAdd(this, _pendingFlush);
        __privateSet(this, _config, {
          maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,
          samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,
          disabled: options.disabled ?? false,
          debug: options.debug ?? false,
          endpoint: DEFAULT_CONFIG.endpoint
        });
        if (!options.clerkVersion && typeof window === "undefined") {
          __privateGet(this, _metadata).clerkVersion = "";
        } else {
          __privateGet(this, _metadata).clerkVersion = options.clerkVersion ?? "";
        }
        __privateGet(this, _metadata).sdk = options.sdk;
        __privateGet(this, _metadata).sdkVersion = options.sdkVersion;
        __privateGet(this, _metadata).publishableKey = options.publishableKey ?? "";
        const parsedKey = parsePublishableKey(options.publishableKey);
        if (parsedKey) {
          __privateGet(this, _metadata).instanceType = parsedKey.instanceType;
        }
        if (options.secretKey) {
          __privateGet(this, _metadata).secretKey = options.secretKey.substring(0, 16);
        }
        __privateSet(this, _eventThrottler, new TelemetryEventThrottler());
      }
      get isEnabled() {
        var _a;
        if (__privateGet(this, _metadata).instanceType !== "development") {
          return false;
        }
        if (__privateGet(this, _config).disabled || typeof process !== "undefined" && isTruthy(process.env.CLERK_TELEMETRY_DISABLED)) {
          return false;
        }
        if (typeof window !== "undefined" && !!((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.webdriver)) {
          return false;
        }
        return true;
      }
      get isDebug() {
        return __privateGet(this, _config).debug || typeof process !== "undefined" && isTruthy(process.env.CLERK_TELEMETRY_DEBUG);
      }
      record(event) {
        const preparedPayload = __privateMethod(this, _TelemetryCollector_instances, preparePayload_fn).call(this, event.event, event.payload);
        __privateMethod(this, _TelemetryCollector_instances, logEvent_fn).call(this, preparedPayload.event, preparedPayload);
        if (!__privateMethod(this, _TelemetryCollector_instances, shouldRecord_fn).call(this, preparedPayload, event.eventSamplingRate)) {
          return;
        }
        __privateGet(this, _buffer).push(preparedPayload);
        __privateMethod(this, _TelemetryCollector_instances, scheduleFlush_fn).call(this);
      }
    };
    _config = /* @__PURE__ */ new WeakMap();
    _eventThrottler = /* @__PURE__ */ new WeakMap();
    _metadata = /* @__PURE__ */ new WeakMap();
    _buffer = /* @__PURE__ */ new WeakMap();
    _pendingFlush = /* @__PURE__ */ new WeakMap();
    _TelemetryCollector_instances = /* @__PURE__ */ new WeakSet();
    shouldRecord_fn = function(preparedPayload, eventSamplingRate) {
      return this.isEnabled && !this.isDebug && __privateMethod(this, _TelemetryCollector_instances, shouldBeSampled_fn).call(this, preparedPayload, eventSamplingRate);
    };
    shouldBeSampled_fn = function(preparedPayload, eventSamplingRate) {
      const randomSeed = Math.random();
      const toBeSampled = randomSeed <= __privateGet(this, _config).samplingRate && (typeof eventSamplingRate === "undefined" || randomSeed <= eventSamplingRate);
      if (!toBeSampled) {
        return false;
      }
      return !__privateGet(this, _eventThrottler).isEventThrottled(preparedPayload);
    };
    scheduleFlush_fn = function() {
      if (typeof window === "undefined") {
        __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);
        return;
      }
      const isBufferFull = __privateGet(this, _buffer).length >= __privateGet(this, _config).maxBufferSize;
      if (isBufferFull) {
        if (__privateGet(this, _pendingFlush)) {
          const cancel = typeof cancelIdleCallback !== "undefined" ? cancelIdleCallback : clearTimeout;
          cancel(__privateGet(this, _pendingFlush));
        }
        __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);
        return;
      }
      if (__privateGet(this, _pendingFlush)) {
        return;
      }
      if ("requestIdleCallback" in window) {
        __privateSet(this, _pendingFlush, requestIdleCallback(() => {
          __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);
        }));
      } else {
        __privateSet(this, _pendingFlush, setTimeout(() => {
          __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);
        }, 0));
      }
    };
    flush_fn = function() {
      fetch(new URL("/v1/event", __privateGet(this, _config).endpoint), {
        method: "POST",
        // TODO: We send an array here with that idea that we can eventually send multiple events.
        body: JSON.stringify({
          events: __privateGet(this, _buffer)
        }),
        headers: {
          "Content-Type": "application/json"
        }
      }).catch(() => void 0).then(() => {
        __privateSet(this, _buffer, []);
      }).catch(() => void 0);
    };
    logEvent_fn = function(event, payload) {
      if (!this.isDebug) {
        return;
      }
      if (typeof console.groupCollapsed !== "undefined") {
        console.groupCollapsed("[clerk/telemetry]", event);
        console.log(payload);
        console.groupEnd();
      } else {
        console.log("[clerk/telemetry]", event, payload);
      }
    };
    getSDKMetadata_fn = function() {
      let sdkMetadata = {
        name: __privateGet(this, _metadata).sdk,
        version: __privateGet(this, _metadata).sdkVersion
      };
      if (typeof window !== "undefined" && window.Clerk) {
        sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };
      }
      return sdkMetadata;
    };
    preparePayload_fn = function(event, payload) {
      const sdkMetadata = __privateMethod(this, _TelemetryCollector_instances, getSDKMetadata_fn).call(this);
      return {
        event,
        cv: __privateGet(this, _metadata).clerkVersion ?? "",
        it: __privateGet(this, _metadata).instanceType ?? "",
        sdk: sdkMetadata.name,
        sdkv: sdkMetadata.version,
        ...__privateGet(this, _metadata).publishableKey ? { pk: __privateGet(this, _metadata).publishableKey } : {},
        ...__privateGet(this, _metadata).secretKey ? { sk: __privateGet(this, _metadata).secretKey } : {},
        payload
      };
    };
    var EVENT_COMPONENT_MOUNTED = "COMPONENT_MOUNTED";
    var EVENT_COMPONENT_OPENED = "COMPONENT_OPENED";
    var EVENT_SAMPLING_RATE = 0.1;
    function createPrebuiltComponentEvent(event) {
      return function(component, props, additionalPayload) {
        var _a, _b, _c;
        return {
          event,
          eventSamplingRate: EVENT_SAMPLING_RATE,
          payload: {
            component,
            appearanceProp: Boolean(props == null ? void 0 : props.appearance),
            baseTheme: Boolean((_a = props == null ? void 0 : props.appearance) == null ? void 0 : _a.baseTheme),
            elements: Boolean((_b = props == null ? void 0 : props.appearance) == null ? void 0 : _b.elements),
            variables: Boolean((_c = props == null ? void 0 : props.appearance) == null ? void 0 : _c.variables),
            ...additionalPayload
          }
        };
      };
    }
    function eventPrebuiltComponentMounted(component, props, additionalPayload) {
      return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);
    }
    function eventPrebuiltComponentOpened(component, props, additionalPayload) {
      return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);
    }
    function eventComponentMounted(component, props = {}) {
      return {
        event: EVENT_COMPONENT_MOUNTED,
        eventSamplingRate: EVENT_SAMPLING_RATE,
        payload: {
          component,
          ...props
        }
      };
    }
    var EVENT_METHOD_CALLED = "METHOD_CALLED";
    function eventMethodCalled(method, payload) {
      return {
        event: EVENT_METHOD_CALLED,
        payload: {
          method,
          ...payload
        }
      };
    }
    var EVENT_FRAMEWORK_METADATA = "FRAMEWORK_METADATA";
    var EVENT_SAMPLING_RATE2 = 0.1;
    function eventFrameworkMetadata(payload) {
      return {
        event: EVENT_FRAMEWORK_METADATA,
        eventSamplingRate: EVENT_SAMPLING_RATE2,
        payload
      };
    }
  }
});

// node_modules/@clerk/remix/dist/utils/errors.js
var require_errors = __commonJS({
  "node_modules/@clerk/remix/dist/utils/errors.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var errors_exports = {};
    __export(errors_exports, {
      invalidClerkStatePropError: () => invalidClerkStatePropError,
      invalidRootLoaderCallbackReturn: () => invalidRootLoaderCallbackReturn,
      noClerkStateError: () => noClerkStateError,
      noLoaderArgsPassedInGetAuth: () => noLoaderArgsPassedInGetAuth,
      noSecretKeyError: () => noSecretKeyError,
      publishableKeyMissingErrorInSpaMode: () => publishableKeyMissingErrorInSpaMode,
      satelliteAndMissingProxyUrlAndDomain: () => satelliteAndMissingProxyUrlAndDomain,
      satelliteAndMissingSignInUrl: () => satelliteAndMissingSignInUrl
    });
    module.exports = __toCommonJS(errors_exports);
    var createErrorMessage = (msg) => {
      return `🔒 Clerk: ${msg.trim()}

For more info, check out the docs: https://clerk.com/docs,
or come say hi in our discord server: https://clerk.com/discord

`;
    };
    var ssrExample = `Use 'rootAuthLoader' as your root loader. Then, simply wrap the App component with ClerkApp and make it the default export.
Example:

import { ClerkApp } from '@clerk/remix';
import { rootAuthLoader } from '@clerk/remix/ssr.server';

export const loader: LoaderFunction = args => rootAuthLoader(args)

function App() {
  return (
    <html lang='en'>
      ...
    </html>
  );
}

export default ClerkApp(App, { publishableKey: '...' });
`;
    var invalidClerkStatePropError = createErrorMessage(`
You're trying to pass an invalid object in "<ClerkProvider clerkState={...}>".

${ssrExample}
`);
    var noClerkStateError = createErrorMessage(`
Looks like you didn't pass 'clerkState' to "<ClerkProvider clerkState={...}>".

${ssrExample}
`);
    var noLoaderArgsPassedInGetAuth = createErrorMessage(`
You're calling 'getAuth()' from a loader, without providing the loader args object.
Example:

export const loader: LoaderFunction = async (args) => {
  const { sessionId } = await getAuth(args);
  ...
};
`);
    var invalidRootLoaderCallbackReturn = createErrorMessage(`
You're returning an invalid response from the 'rootAuthLoader' called from the loader in root.tsx.
You can only return plain objects, responses created using the Remix 'json()' and 'redirect()' helpers,
custom redirect 'Response' instances (status codes in the range of 300 to 400),
or custom json 'Response' instances (containing a body that is a valid json string).
If you want to return a primitive value or an array, you can always wrap the response with an object.

Example:

export const loader: LoaderFunction = args => rootAuthLoader(args, ({ auth }) => {
    const { userId } = auth;
    const posts: Post[] = database.getPostsByUserId(userId);

    return json({ data: posts })
    // or
    return new Response(JSON.stringify({ data: posts }), { headers: { 'Content-Type': 'application/json' } });
    // or
    return { data: posts };
})
`);
    var noSecretKeyError = createErrorMessage(`
A secretKey must be provided in order to use SSR and the exports from @clerk/remix/api.');
If your runtime supports environment variables, you can add a CLERK_SECRET_KEY variable to your config.
Otherwise, you can pass a secretKey parameter to rootAuthLoader or getAuth.
`);
    var satelliteAndMissingProxyUrlAndDomain = createErrorMessage(
      `Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`
    );
    var satelliteAndMissingSignInUrl = createErrorMessage(`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL.`);
    var publishableKeyMissingErrorInSpaMode = createErrorMessage(`
You're trying to use Clerk in Remix SPA Mode without providing a Publishable Key.
Please provide the publishableKey option on the ClerkApp component.

Example:

export default ClerkApp(App, {
  publishableKey: 'pk_test_XXX'
});
`);
  }
});

// node_modules/@clerk/shared/dist/utils/index.js
var require_utils = __commonJS({
  "node_modules/@clerk/shared/dist/utils/index.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var utils_exports = {};
    __export(utils_exports, {
      allSettled: () => allSettled,
      createDeferredPromise: () => createDeferredPromise,
      fastDeepMergeAndKeep: () => fastDeepMergeAndKeep,
      fastDeepMergeAndReplace: () => fastDeepMergeAndReplace,
      handleValueOrFn: () => handleValueOrFn,
      isDevelopmentEnvironment: () => isDevelopmentEnvironment,
      isProductionEnvironment: () => isProductionEnvironment,
      isStaging: () => isStaging,
      isTestEnvironment: () => isTestEnvironment,
      logErrorInDevMode: () => logErrorInDevMode,
      noop: () => noop
    });
    module.exports = __toCommonJS(utils_exports);
    var noop = (..._args) => {
    };
    var createDeferredPromise = () => {
      let resolve = noop;
      let reject = noop;
      const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
      });
      return { promise, resolve, reject };
    };
    function allSettled(iterable) {
      const promises = Array.from(iterable).map(
        (p) => p.then(
          (value) => ({ status: "fulfilled", value }),
          (reason) => ({ status: "rejected", reason })
        )
      );
      return Promise.all(promises);
    }
    function isStaging(frontendApi) {
      return frontendApi.endsWith(".lclstage.dev") || frontendApi.endsWith(".stgstage.dev") || frontendApi.endsWith(".clerkstage.dev") || frontendApi.endsWith(".accountsstage.dev");
    }
    var isDevelopmentEnvironment = () => {
      try {
        return true;
      } catch {
      }
      return false;
    };
    var isTestEnvironment = () => {
      try {
        return false;
      } catch {
      }
      return false;
    };
    var isProductionEnvironment = () => {
      try {
        return false;
      } catch {
      }
      return false;
    };
    var logErrorInDevMode = (message) => {
      if (isDevelopmentEnvironment()) {
        console.error(`Clerk: ${message}`);
      }
    };
    function handleValueOrFn(value, url, defaultValue) {
      if (typeof value === "function") {
        return value(url);
      }
      if (typeof value !== "undefined") {
        return value;
      }
      if (typeof defaultValue !== "undefined") {
        return defaultValue;
      }
      return void 0;
    }
    var fastDeepMergeAndReplace = (source, target) => {
      if (!source || !target) {
        return;
      }
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {
          if (target[key] === void 0) {
            target[key] = new (Object.getPrototypeOf(source[key])).constructor();
          }
          fastDeepMergeAndReplace(source[key], target[key]);
        } else if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    };
    var fastDeepMergeAndKeep = (source, target) => {
      if (!source || !target) {
        return;
      }
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {
          if (target[key] === void 0) {
            target[key] = new (Object.getPrototypeOf(source[key])).constructor();
          }
          fastDeepMergeAndKeep(source[key], target[key]);
        } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === void 0) {
          target[key] = source[key];
        }
      }
    };
  }
});

// node_modules/@clerk/shared/dist/keys.js
var require_keys = __commonJS({
  "node_modules/@clerk/shared/dist/keys.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var keys_exports = {};
    __export(keys_exports, {
      buildPublishableKey: () => buildPublishableKey,
      createDevOrStagingUrlCache: () => createDevOrStagingUrlCache,
      getCookieSuffix: () => getCookieSuffix,
      getSuffixedCookieName: () => getSuffixedCookieName,
      isDevelopmentFromPublishableKey: () => isDevelopmentFromPublishableKey,
      isDevelopmentFromSecretKey: () => isDevelopmentFromSecretKey,
      isProductionFromPublishableKey: () => isProductionFromPublishableKey,
      isProductionFromSecretKey: () => isProductionFromSecretKey,
      isPublishableKey: () => isPublishableKey,
      parsePublishableKey: () => parsePublishableKey
    });
    module.exports = __toCommonJS(keys_exports);
    var LEGACY_DEV_INSTANCE_SUFFIXES = [".lcl.dev", ".lclstage.dev", ".lclclerk.com"];
    var DEV_OR_STAGING_SUFFIXES = [
      ".lcl.dev",
      ".stg.dev",
      ".lclstage.dev",
      ".stgstage.dev",
      ".dev.lclclerk.com",
      ".stg.lclclerk.com",
      ".accounts.lclclerk.com",
      "accountsstage.dev",
      "accounts.dev"
    ];
    var isomorphicAtob = (data) => {
      if (typeof atob !== "undefined" && typeof atob === "function") {
        return atob(data);
      } else if (typeof global !== "undefined" && global.Buffer) {
        return new global.Buffer(data, "base64").toString();
      }
      return data;
    };
    var isomorphicBtoa = (data) => {
      if (typeof btoa !== "undefined" && typeof btoa === "function") {
        return btoa(data);
      } else if (typeof global !== "undefined" && global.Buffer) {
        return new global.Buffer(data).toString("base64");
      }
      return data;
    };
    var PUBLISHABLE_KEY_LIVE_PREFIX = "pk_live_";
    var PUBLISHABLE_KEY_TEST_PREFIX = "pk_test_";
    var PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\.clerk\.accounts([a-z.]*)(dev|com)$/i;
    function buildPublishableKey(frontendApi) {
      const isDevKey = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) || frontendApi.startsWith("clerk.") && LEGACY_DEV_INSTANCE_SUFFIXES.some((s) => frontendApi.endsWith(s));
      const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;
      return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;
    }
    function parsePublishableKey(key, options = {}) {
      key = key || "";
      if (!key || !isPublishableKey(key)) {
        if (options.fatal && !key) {
          throw new Error(
            "Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys"
          );
        }
        if (options.fatal && !isPublishableKey(key)) {
          throw new Error("Publishable key not valid.");
        }
        return null;
      }
      const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? "production" : "development";
      let frontendApi = isomorphicAtob(key.split("_")[2]);
      frontendApi = frontendApi.slice(0, -1);
      if (options.proxyUrl) {
        frontendApi = options.proxyUrl;
      } else if (instanceType !== "development" && options.domain && options.isSatellite) {
        frontendApi = `clerk.${options.domain}`;
      }
      return {
        instanceType,
        frontendApi
      };
    }
    function isPublishableKey(key = "") {
      try {
        const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);
        const hasValidFrontendApiPostfix = isomorphicAtob(key.split("_")[2] || "").endsWith("$");
        return hasValidPrefix && hasValidFrontendApiPostfix;
      } catch {
        return false;
      }
    }
    function createDevOrStagingUrlCache() {
      const devOrStagingUrlCache = /* @__PURE__ */ new Map();
      return {
        isDevOrStagingUrl: (url) => {
          if (!url) {
            return false;
          }
          const hostname = typeof url === "string" ? url : url.hostname;
          let res = devOrStagingUrlCache.get(hostname);
          if (res === void 0) {
            res = DEV_OR_STAGING_SUFFIXES.some((s) => hostname.endsWith(s));
            devOrStagingUrlCache.set(hostname, res);
          }
          return res;
        }
      };
    }
    function isDevelopmentFromPublishableKey(apiKey) {
      return apiKey.startsWith("test_") || apiKey.startsWith("pk_test_");
    }
    function isProductionFromPublishableKey(apiKey) {
      return apiKey.startsWith("live_") || apiKey.startsWith("pk_live_");
    }
    function isDevelopmentFromSecretKey(apiKey) {
      return apiKey.startsWith("test_") || apiKey.startsWith("sk_test_");
    }
    function isProductionFromSecretKey(apiKey) {
      return apiKey.startsWith("live_") || apiKey.startsWith("sk_live_");
    }
    async function getCookieSuffix(publishableKey, subtle = globalThis.crypto.subtle) {
      const data = new TextEncoder().encode(publishableKey);
      const digest = await subtle.digest("sha-1", data);
      const stringDigest = String.fromCharCode(...new Uint8Array(digest));
      return isomorphicBtoa(stringDigest).replace(/\+/gi, "-").replace(/\//gi, "_").substring(0, 8);
    }
    var getSuffixedCookieName = (cookieName, cookieSuffix) => {
      return `${cookieName}_${cookieSuffix}`;
    };
  }
});

// node_modules/@clerk/remix/dist/utils/utils.js
var require_utils2 = __commonJS({
  "node_modules/@clerk/remix/dist/utils/utils.js"(exports, module) {
    "use strict";
    var __defProp = Object.defineProperty;
    var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    var __getOwnPropNames = Object.getOwnPropertyNames;
    var __hasOwnProp = Object.prototype.hasOwnProperty;
    var __export = (target, all) => {
      for (var name in all)
        __defProp(target, name, { get: all[name], enumerable: true });
    };
    var __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
    var utils_exports = {};
    __export(utils_exports, {
      assertEnvVar: () => assertEnvVar,
      assertPublishableKeyInSpaMode: () => assertPublishableKeyInSpaMode,
      assertValidClerkState: () => assertValidClerkState,
      getEnvVariable: () => getEnvVariable,
      inSpaMode: () => inSpaMode,
      warnForSsr: () => warnForSsr
    });
    module.exports = __toCommonJS(utils_exports);
    var import_errors = require_errors();
    function warnForSsr(val) {
      if (!val || !val.__internal_clerk_state) {
        console.warn(import_errors.noClerkStateError);
      }
    }
    function assertEnvVar(name, errorMessage) {
      if (!name || typeof name !== "string") {
        throw new Error(errorMessage);
      }
    }
    function assertValidClerkState(val) {
      if (!val) {
        throw new Error(import_errors.noClerkStateError);
      }
      if (!!val && !val.__internal_clerk_state) {
        throw new Error(import_errors.invalidClerkStatePropError);
      }
    }
    function assertPublishableKeyInSpaMode(key) {
      if (!key || typeof key !== "string") {
        throw new Error(import_errors.publishableKeyMissingErrorInSpaMode);
      }
    }
    var hasCloudflareProxyContext = (context) => {
      var _a;
      return !!((_a = context == null ? void 0 : context.cloudflare) == null ? void 0 : _a.env);
    };
    var hasCloudflareContext = (context) => {
      return !!(context == null ? void 0 : context.env);
    };
    var getEnvVariable = (name, context) => {
      if (typeof process !== "undefined" && process.env && typeof process.env[name] === "string") {
        return process.env[name];
      }
      if (hasCloudflareProxyContext(context)) {
        return context.cloudflare.env[name] || "";
      }
      if (hasCloudflareContext(context)) {
        return context.env[name] || "";
      }
      if (context && typeof context[name] === "string") {
        return context[name];
      }
      try {
        return globalThis[name];
      } catch {
      }
      return "";
    };
    var inSpaMode = () => {
      var _a;
      if (typeof window !== "undefined" && typeof ((_a = window.__remixContext) == null ? void 0 : _a.isSpaMode) !== "undefined") {
        return window.__remixContext.isSpaMode;
      }
      return false;
    };
  }
});

export {
  require_error,
  require_utils,
  require_deprecated,
  require_authorization,
  require_telemetry,
  require_keys,
  require_errors,
  require_utils2
};
//# sourceMappingURL=chunk-XACFPJBO.js.map
