{"version": 3, "sources": ["../../@clerk/shared/src/error.ts", "../../@clerk/shared/src/deprecated.ts", "../../@clerk/shared/src/utils/runtimeEnvironment.ts", "../../@clerk/shared/src/authorization.ts", "../../@clerk/shared/src/telemetry.ts", "../../@clerk/shared/src/isomorphicAtob.ts", "../../@clerk/shared/src/keys.ts", "../../@clerk/shared/src/underscore.ts", "../../@clerk/shared/src/telemetry/throttler.ts", "../../@clerk/shared/src/telemetry/collector.ts", "../../@clerk/shared/src/telemetry/events/component-mounted.ts", "../../@clerk/shared/src/telemetry/events/method-called.ts", "../../@clerk/shared/src/telemetry/events/framework-metadata.ts", "../../@clerk/remix/src/utils/errors.ts", "../../@clerk/shared/src/utils/index.ts", "../../@clerk/shared/src/utils/noop.ts", "../../@clerk/shared/src/utils/createDeferredPromise.ts", "../../@clerk/shared/src/utils/allSettled.ts", "../../@clerk/shared/src/utils/instance.ts", "../../@clerk/shared/src/utils/runtimeEnvironment.ts", "../../@clerk/shared/src/utils/logErrorInDevMode.ts", "../../@clerk/shared/src/utils/handleValueOrFn.ts", "../../@clerk/shared/src/utils/fastDeepMerge.ts", "../../@clerk/shared/src/keys.ts", "../../@clerk/shared/src/constants.ts", "../../@clerk/shared/src/isomorphicAtob.ts", "../../@clerk/shared/src/isomorphicBtoa.ts", "../../@clerk/remix/src/utils/utils.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n", "import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => value.replace(/^(org:)*/, 'org:');\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return prefixWithOrg(orgRole) === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages.\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n", "export { TelemetryCollector } from './telemetry/collector';\nexport type { TelemetryCollectorOptions } from './telemetry/types';\n\nexport * from './telemetry/events';\n", "/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n", "import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n", "/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n", "import type { TelemetryEvent } from '@clerk/types';\n\ntype TtlInMilliseconds = number;\n\nconst DEFAULT_CACHE_TTL_MS = 86400000; // 24 hours\n\n/**\n * Manages throttling for telemetry events using the browser's localStorage to\n * mitigate event flooding in frequently executed code paths.\n */\nexport class TelemetryEventThrottler {\n  #storageKey = 'clerk_telemetry_throttler';\n  #cacheTtl = DEFAULT_CACHE_TTL_MS;\n\n  isEventThrottled(payload: TelemetryEvent): boolean {\n    if (!this.#isValidBrowser) {\n      return false;\n    }\n\n    const now = Date.now();\n    const key = this.#generateKey(payload);\n    const entry = this.#cache?.[key];\n\n    if (!entry) {\n      const updatedCache = {\n        ...this.#cache,\n        [key]: now,\n      };\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    const shouldInvalidate = entry && now - entry > this.#cacheTtl;\n    if (shouldInvalidate) {\n      const updatedCache = this.#cache;\n      delete updatedCache[key];\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    return !!entry;\n  }\n\n  /**\n   * Generates a consistent unique key for telemetry events by sorting payload properties.\n   * This ensures that payloads with identical content in different orders produce the same key.\n   */\n  #generateKey(event: TelemetryEvent): string {\n    const { sk: _sk, pk: _pk, payload, ...rest } = event;\n\n    const sanitizedEvent: Omit<TelemetryEvent, 'sk' | 'pk' | 'payload'> & TelemetryEvent['payload'] = {\n      ...payload,\n      ...rest,\n    };\n\n    return JSON.stringify(\n      Object.keys({\n        ...payload,\n        ...rest,\n      })\n        .sort()\n        .map(key => sanitizedEvent[key]),\n    );\n  }\n\n  get #cache(): Record<string, TtlInMilliseconds> | undefined {\n    const cacheString = localStorage.getItem(this.#storageKey);\n\n    if (!cacheString) {\n      return {};\n    }\n\n    return JSON.parse(cacheString);\n  }\n\n  /**\n   * Checks if the browser's localStorage is supported and writable.\n   *\n   * If any of these operations fail, it indicates that localStorage is either\n   * not supported or not writable (e.g., in cases where the storage is full or\n   * the browser is in a privacy mode that restricts localStorage usage).\n   */\n  get #isValidBrowser(): boolean {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n\n    const storage = window.localStorage;\n    if (!storage) {\n      return false;\n    }\n\n    try {\n      const testKey = 'test';\n      storage.setItem(testKey, testKey);\n      storage.removeItem(testKey);\n\n      return true;\n    } catch (err: unknown) {\n      const isQuotaExceededError =\n        err instanceof DOMException &&\n        // Check error names for different browsers\n        (err.name === 'QuotaExceededError' || err.name === 'NS_ERROR_DOM_QUOTA_REACHED');\n\n      if (isQuotaExceededError && storage.length > 0) {\n        storage.removeItem(this.#storageKey);\n      }\n\n      return false;\n    }\n  }\n}\n", "/**\n * The `TelemetryCollector` class handles collection of telemetry events from Clerk <PERSON>. Telemetry is opt-out and can be disabled by setting a CLERK_TELEMETRY_DISABLED environment variable.\n * The `Clerk<PERSON>rovider` also accepts a `telemetry` prop that will be passed to the collector during initialization:\n *\n * ```jsx\n * <ClerkProvider telemetry={false}>\n *    ...\n * </ClerkProvider>\n * ```\n *\n * For more information, please see the telemetry documentation page: https://clerk.com/docs/telemetry\n */\nimport type {\n  InstanceType,\n  TelemetryCollector as TelemetryCollectorInterface,\n  TelemetryEvent,\n  TelemetryEventRaw,\n} from '@clerk/types';\n\nimport { parsePublishableKey } from '../keys';\nimport { isTruthy } from '../underscore';\nimport { TelemetryEventThrottler } from './throttler';\nimport type { TelemetryCollectorOptions } from './types';\n\ntype TelemetryCollectorConfig = Pick<\n  TelemetryCollectorOptions,\n  'samplingRate' | 'disabled' | 'debug' | 'maxBufferSize'\n> & {\n  endpoint: string;\n};\n\ntype TelemetryMetadata = Required<\n  Pick<TelemetryCollectorOptions, 'clerkVersion' | 'sdk' | 'sdkVersion' | 'publishableKey' | 'secretKey'>\n> & {\n  /**\n   * The instance type, derived from the provided publishableKey.\n   */\n  instanceType: InstanceType;\n};\n\nconst DEFAULT_CONFIG: Partial<TelemetryCollectorConfig> = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: 'https://clerk-telemetry.com',\n};\n\nexport class TelemetryCollector implements TelemetryCollectorInterface {\n  #config: Required<TelemetryCollectorConfig>;\n  #eventThrottler: TelemetryEventThrottler;\n  #metadata: TelemetryMetadata = {} as TelemetryMetadata;\n  #buffer: TelemetryEvent[] = [];\n  #pendingFlush: any;\n\n  constructor(options: TelemetryCollectorOptions) {\n    this.#config = {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint,\n    } as Required<TelemetryCollectorConfig>;\n\n    if (!options.clerkVersion && typeof window === 'undefined') {\n      // N/A in a server environment\n      this.#metadata.clerkVersion = '';\n    } else {\n      this.#metadata.clerkVersion = options.clerkVersion ?? '';\n    }\n\n    // We will try to grab the SDK data lazily when an event is triggered, so it should always be defined once the event is sent.\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdk = options.sdk!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdkVersion = options.sdkVersion!;\n\n    this.#metadata.publishableKey = options.publishableKey ?? '';\n\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      this.#metadata.instanceType = parsedKey.instanceType;\n    }\n\n    if (options.secretKey) {\n      // Only send the first 16 characters of the secret key to to avoid sending the full key. We can still query against the partial key.\n      this.#metadata.secretKey = options.secretKey.substring(0, 16);\n    }\n\n    this.#eventThrottler = new TelemetryEventThrottler();\n  }\n\n  get isEnabled(): boolean {\n    if (this.#metadata.instanceType !== 'development') {\n      return false;\n    }\n\n    // In browser or client environments, we most likely pass the disabled option to the collector, but in environments\n    // where environment variables are available we also check for `CLERK_TELEMETRY_DISABLED`.\n    if (this.#config.disabled || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DISABLED))) {\n      return false;\n    }\n\n    // navigator.webdriver is a property generally set by headless browsers that are running in an automated testing environment.\n    // Data from these environments is not meaningful for us and has the potential to produce a large volume of events, so we disable\n    // collection in this case. (ref: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/webdriver)\n    if (typeof window !== 'undefined' && !!window?.navigator?.webdriver) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get isDebug(): boolean {\n    return this.#config.debug || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DEBUG));\n  }\n\n  record(event: TelemetryEventRaw): void {\n    const preparedPayload = this.#preparePayload(event.event, event.payload);\n\n    this.#logEvent(preparedPayload.event, preparedPayload);\n\n    if (!this.#shouldRecord(preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n\n    this.#buffer.push(preparedPayload);\n\n    this.#scheduleFlush();\n  }\n\n  #shouldRecord(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    return this.isEnabled && !this.isDebug && this.#shouldBeSampled(preparedPayload, eventSamplingRate);\n  }\n\n  #shouldBeSampled(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    const randomSeed = Math.random();\n\n    const toBeSampled =\n      randomSeed <= this.#config.samplingRate &&\n      (typeof eventSamplingRate === 'undefined' || randomSeed <= eventSamplingRate);\n\n    if (!toBeSampled) {\n      return false;\n    }\n\n    return !this.#eventThrottler.isEventThrottled(preparedPayload);\n  }\n\n  #scheduleFlush(): void {\n    // On the server, we want to flush immediately as we have less guarantees about the lifecycle of the process\n    if (typeof window === 'undefined') {\n      this.#flush();\n      return;\n    }\n\n    const isBufferFull = this.#buffer.length >= this.#config.maxBufferSize;\n    if (isBufferFull) {\n      // If the buffer is full, flush immediately to make sure we minimize the chance of event loss.\n      // Cancel any pending flushes as we're going to flush immediately\n      if (this.#pendingFlush) {\n        const cancel = typeof cancelIdleCallback !== 'undefined' ? cancelIdleCallback : clearTimeout;\n        cancel(this.#pendingFlush);\n      }\n      this.#flush();\n      return;\n    }\n\n    // If we have a pending flush, do nothing\n    if (this.#pendingFlush) {\n      return;\n    }\n\n    if ('requestIdleCallback' in window) {\n      this.#pendingFlush = requestIdleCallback(() => {\n        this.#flush();\n      });\n    } else {\n      // This is not an ideal solution, but it at least waits until the next tick\n      this.#pendingFlush = setTimeout(() => {\n        this.#flush();\n      }, 0);\n    }\n  }\n\n  #flush(): void {\n    fetch(new URL('/v1/event', this.#config.endpoint), {\n      method: 'POST',\n      // TODO: We send an array here with that idea that we can eventually send multiple events.\n      body: JSON.stringify({\n        events: this.#buffer,\n      }),\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n      .catch(() => void 0)\n      .then(() => {\n        this.#buffer = [];\n      })\n      .catch(() => void 0);\n  }\n\n  /**\n   * If running in debug mode, log the event and its payload to the console.\n   */\n  #logEvent(event: TelemetryEvent['event'], payload: Record<string, any>) {\n    if (!this.isDebug) {\n      return;\n    }\n\n    if (typeof console.groupCollapsed !== 'undefined') {\n      console.groupCollapsed('[clerk/telemetry]', event);\n      console.log(payload);\n      console.groupEnd();\n    } else {\n      console.log('[clerk/telemetry]', event, payload);\n    }\n  }\n\n  /**\n   * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n   *\n   * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n   */\n  #getSDKMetadata() {\n    let sdkMetadata = {\n      name: this.#metadata.sdk,\n      version: this.#metadata.sdkVersion,\n    };\n\n    // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n    if (typeof window !== 'undefined' && window.Clerk) {\n      // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n      sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n    }\n\n    return sdkMetadata;\n  }\n\n  /**\n   * Append relevant metadata from the Clerk singleton to the event payload.\n   */\n  #preparePayload(event: TelemetryEvent['event'], payload: TelemetryEvent['payload']): TelemetryEvent {\n    const sdkMetadata = this.#getSDKMetadata();\n\n    return {\n      event,\n      cv: this.#metadata.clerkVersion ?? '',\n      it: this.#metadata.instanceType ?? '',\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      ...(this.#metadata.publishableKey ? { pk: this.#metadata.publishableKey } : {}),\n      ...(this.#metadata.secretKey ? { sk: this.#metadata.secretKey } : {}),\n      payload,\n    };\n  }\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_COMPONENT_MOUNTED = 'COMPONENT_MOUNTED';\nconst EVENT_COMPONENT_OPENED = 'COMPONENT_OPENED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype ComponentMountedBase = {\n  component: string;\n};\n\ntype EventPrebuiltComponent = ComponentMountedBase & {\n  appearanceProp: boolean;\n  elements: boolean;\n  variables: boolean;\n  baseTheme: boolean;\n};\n\ntype EventComponentMounted = ComponentMountedBase & TelemetryEventRaw['payload'];\n\nfunction createPrebuiltComponentEvent(event: typeof EVENT_COMPONENT_MOUNTED | typeof EVENT_COMPONENT_OPENED) {\n  return function (\n    component: string,\n    props?: Record<string, any>,\n    additionalPayload?: TelemetryEventRaw['payload'],\n  ): TelemetryEventRaw<EventPrebuiltComponent> {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Bo<PERSON>an(props?.appearance),\n        baseTheme: <PERSON><PERSON><PERSON>(props?.appearance?.baseTheme),\n        elements: <PERSON><PERSON><PERSON>(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload,\n      },\n    };\n  };\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is mounted.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentMounted('SignUp', props));\n */\nexport function eventPrebuiltComponentMounted(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is opened as a modal.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentOpened('GoogleOneTap', props));\n */\nexport function eventPrebuiltComponentOpened(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a component is mounted. Use `eventPrebuiltComponentMounted` for prebuilt components.\n *\n * **Caution:** Filter the `props` you pass to this function to avoid sending too much data.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Ideally you only pass a handful of props here.\n *\n * @example\n * telemetry.record(eventComponentMounted('SignUp', props));\n */\nexport function eventComponentMounted(\n  component: string,\n  props: TelemetryEventRaw['payload'] = {},\n): TelemetryEventRaw<EventComponentMounted> {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_METHOD_CALLED = 'METHOD_CALLED';\n\ntype EventMethodCalled = {\n  method: string;\n} & Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventMethodCalled(\n  method: string,\n  payload?: Record<string, unknown>,\n): TelemetryEventRaw<EventMethodCalled> {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_FRAMEWORK_METADATA = 'FRAMEWORK_METADATA';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventFrameworkMetadata = Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventFrameworkMetadata(payload: EventFrameworkMetadata): TelemetryEventRaw<EventFrameworkMetadata> {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload,\n  };\n}\n", "const createErrorMessage = (msg: string) => {\n  return `🔒 Clerk: ${msg.trim()}\n\nFor more info, check out the docs: https://clerk.com/docs,\nor come say hi in our discord server: https://clerk.com/discord\n\n`;\n};\n\nconst ssrExample = `Use 'rootAuthLoader' as your root loader. Then, simply wrap the App component with ClerkApp and make it the default export.\nExample:\n\nimport { ClerkApp } from '@clerk/remix';\nimport { rootAuthLoader } from '@clerk/remix/ssr.server';\n\nexport const loader: LoaderFunction = args => rootAuthLoader(args)\n\nfunction App() {\n  return (\n    <html lang='en'>\n      ...\n    </html>\n  );\n}\n\nexport default ClerkApp(App, { publishableKey: '...' });\n`;\n\nexport const invalidClerkStatePropError = createErrorMessage(`\nYou're trying to pass an invalid object in \"<ClerkProvider clerkState={...}>\".\n\n${ssrExample}\n`);\n\nexport const noClerkStateError = createErrorMessage(`\nLooks like you didn't pass 'clerkState' to \"<ClerkProvider clerkState={...}>\".\n\n${ssrExample}\n`);\n\nexport const noLoaderArgsPassedInGetAuth = createErrorMessage(`\nYou're calling 'getAuth()' from a loader, without providing the loader args object.\nExample:\n\nexport const loader: LoaderFunction = async (args) => {\n  const { sessionId } = await getAuth(args);\n  ...\n};\n`);\n\nexport const invalidRootLoaderCallbackReturn = createErrorMessage(`\nYou're returning an invalid response from the 'rootAuthLoader' called from the loader in root.tsx.\nYou can only return plain objects, responses created using the Remix 'json()' and 'redirect()' helpers,\ncustom redirect 'Response' instances (status codes in the range of 300 to 400),\nor custom json 'Response' instances (containing a body that is a valid json string).\nIf you want to return a primitive value or an array, you can always wrap the response with an object.\n\nExample:\n\nexport const loader: LoaderFunction = args => rootAuthLoader(args, ({ auth }) => {\n    const { userId } = auth;\n    const posts: Post[] = database.getPostsByUserId(userId);\n\n    return json({ data: posts })\n    // or\n    return new Response(JSON.stringify({ data: posts }), { headers: { 'Content-Type': 'application/json' } });\n    // or\n    return { data: posts };\n})\n`);\n\nexport const noSecretKeyError = createErrorMessage(`\nA secretKey must be provided in order to use SSR and the exports from @clerk/remix/api.');\nIf your runtime supports environment variables, you can add a CLERK_SECRET_KEY variable to your config.\nOtherwise, you can pass a secretKey parameter to rootAuthLoader or getAuth.\n`);\n\nexport const satelliteAndMissingProxyUrlAndDomain = createErrorMessage(\n  `Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`,\n);\n\nexport const satelliteAndMissingSignInUrl = createErrorMessage(`\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL.`);\n\nexport const publishableKeyMissingErrorInSpaMode = createErrorMessage(`\nYou're trying to use Clerk in Remix SPA Mode without providing a Publishable Key.\nPlease provide the publishableKey option on the ClerkApp component.\n\nExample:\n\nexport default ClerkApp(App, {\n  publishableKey: 'pk_test_XXX'\n});\n`);\n", "export * from './createDeferredPromise';\nexport * from './allSettled';\nexport { isStaging } from './instance';\nexport { logErrorInDevMode } from './logErrorInDevMode';\nexport { noop } from './noop';\nexport * from './runtimeEnvironment';\nexport { handleValueOrFn } from './handleValueOrFn';\nexport { fastDeepMergeAndReplace, fastDeepMergeAndKeep } from './fastDeepMerge';\n", "export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n", "import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n", "/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n", "import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n", "export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n", "/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n", "export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n", "import type { AppLoadContext } from '@remix-run/server-runtime';\n\nimport type { ClerkState } from '../client/types';\nimport { invalidClerkStatePropError, noClerkStateError, publishableKeyMissingErrorInSpaMode } from './errors';\n\nexport function warnForSsr(val: ClerkState | undefined) {\n  if (!val || !val.__internal_clerk_state) {\n    console.warn(noClerkStateError);\n  }\n}\n\nexport function assertEnvVar(name: any, errorMessage: string): asserts name is string {\n  if (!name || typeof name !== 'string') {\n    throw new Error(errorMessage);\n  }\n}\n\nexport function assertValidClerkState(val: any): asserts val is ClerkState | undefined {\n  if (!val) {\n    throw new Error(noClerkStateError);\n  }\n  if (!!val && !val.__internal_clerk_state) {\n    throw new Error(invalidClerkStatePropError);\n  }\n}\n\nexport function assertPublishableKeyInSpaMode(key: any): asserts key is string {\n  if (!key || typeof key !== 'string') {\n    throw new Error(publishableKeyMissingErrorInSpaMode);\n  }\n}\n\ntype CloudflareEnv = { env: Record<string, string> };\n\n// https://remix.run/blog/remix-vite-stable#cloudflare-pages-support\nconst hasCloudflareProxyContext = (context: any): context is { cloudflare: CloudflareEnv } => {\n  return !!context?.cloudflare?.env;\n};\n\nconst hasCloudflareContext = (context: any): context is CloudflareEnv => {\n  return !!context?.env;\n};\n\n/**\n *\n * Utility function to get env variables across Node and Edge runtimes.\n *\n * @param name\n * @returns string\n */\nexport const getEnvVariable = (name: string, context: AppLoadContext | undefined): string => {\n  // Node envs\n  if (typeof process !== 'undefined' && process.env && typeof process.env[name] === 'string') {\n    return process.env[name];\n  }\n\n  // Remix + Cloudflare pages\n  // if (typeof (context?.cloudflare as CloudflareEnv)?.env !== 'undefined') {\n  if (hasCloudflareProxyContext(context)) {\n    return context.cloudflare.env[name] || '';\n  }\n\n  // Cloudflare\n  if (hasCloudflareContext(context)) {\n    return context.env[name] || '';\n  }\n\n  // Check whether the value exists in the context object directly\n  if (context && typeof context[name] === 'string') {\n    return context[name];\n  }\n\n  // Cloudflare workers\n  try {\n    return globalThis[name as keyof typeof globalThis];\n  } catch {\n    // This will raise an error in Cloudflare Pages\n  }\n\n  return '';\n};\n\nexport const inSpaMode = (): boolean => {\n  if (typeof window !== 'undefined' && typeof window.__remixContext?.isSpaMode !== 'undefined') {\n    return window.__remixContext.isSpaMode;\n  }\n  return false;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,QAAA,gBAAA,CAAA;AAAA,aAAA,eAAA;MAAA,uBAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,oBAAA,MAAA;MAAA,gBAAA,MAAA;MAAA,oBAAA,MAAA;MAAA,0BAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,aAAA,MAAA;MAAA,YAAA,MAAA;MAAA,gBAAA,MAAA;MAAA,yBAAA,MAAA;MAAA,qBAAA,MAAA;MAAA,kBAAA,MAAA;MAAA,cAAA,MAAA;MAAA,iBAAA,MAAA;MAAA,gBAAA,MAAA;MAAA,sBAAA,MAAA;MAAA,gCAAA,MAAA;MAAA,qBAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,YAAA,MAAA;MAAA,aAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,aAAA;AAEO,aAAS,oBAAoB,GAAiB;;AACnD,YAAM,SAAS,uBAAG;AAClB,YAAM,QAAO,kCAAG,WAAH,mBAAY,OAAZ,mBAAgB;AAC7B,aAAO,SAAS,4BAA4B,WAAW;IACzD;AAEO,aAAS,eAAe,GAAmC;AAChE,aAAO,CAAC,mBAAmB,uBAAuB,uBAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI;IACtG;AAEO,aAAS,WAAW,GAAiB;AAC1C,YAAM,SAAS,uBAAG;AAClB,aAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;IAC/C;AAEO,aAAS,eAAe,GAAiB;AAE9C,YAAM,WAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM,IAAI,YAAY,EAAE,QAAQ,QAAQ,EAAE;AAChF,aAAO,QAAQ,SAAS,cAAc;IACxC;AAiBO,aAAS,aAAa,OAAgF;AAC3G,aAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;IAC9F;AAEO,aAAS,wBAAwB,KAAwC;AAC9E,aAAO,gBAAgB;IACzB;AAkBO,aAAS,oBAAoB,KAAoC;AACtE,aAAO,uBAAuB;IAChC;AAEO,aAAS,+BAA+B,KAAU;AACvD,aAAO,oBAAoB,GAAG,KAAK,IAAI,SAAS;IAClD;AAEO,aAAS,gBAAgB,KAAgC;AAC9D,aAAO,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,aAAa;IAClF;AAEO,aAAS,kBAAkB,KAAU;;AAC1C,aAAO,wBAAwB,GAAG,OAAK,eAAI,WAAJ,mBAAa,OAAb,mBAAiB,UAAS;IACnE;AAEO,aAAS,qBAAqB,KAAU;;AAC7C,aAAO,wBAAwB,GAAG,OAAK,eAAI,WAAJ,mBAAa,OAAb,mBAAiB,UAAS;IACnE;AAEO,aAAS,YAAY,OAA4B,CAAC,GAAoB;AAC3E,aAAO,KAAK,SAAS,IAAI,KAAK,IAAI,UAAU,IAAI,CAAC;IACnD;AAEO,aAAS,WAAW,OAAyC;;AAClE,aAAO;QACL,MAAM,MAAM;QACZ,SAAS,MAAM;QACf,aAAa,MAAM;QACnB,MAAM;UACJ,YAAW,oCAAO,SAAP,mBAAa;UACxB,YAAW,oCAAO,SAAP,mBAAa;UACxB,iBAAgB,oCAAO,SAAP,mBAAa;UAC7B,cAAa,oCAAO,SAAP,mBAAa;UAC1B,SAAQ,oCAAO,SAAP,mBAAa;UACrB,OAAM,oCAAO,SAAP,mBAAa;QACrB;MACF;IACF;AAEO,aAAS,YAAY,OAAgD;;AAC1E,aAAO;QACL,OAAM,+BAAO,SAAQ;QACrB,UAAS,+BAAO,YAAW;QAC3B,cAAc,+BAAO;QACrB,MAAM;UACJ,aAAY,oCAAO,SAAP,mBAAa;UACzB,aAAY,oCAAO,SAAP,mBAAa;UACzB,kBAAiB,oCAAO,SAAP,mBAAa;UAC9B,cAAa,oCAAO,SAAP,mBAAa;UAC1B,SAAQ,oCAAO,SAAP,mBAAa;UACrB,OAAM,oCAAO,SAAP,mBAAa;QACrB;MACF;IACF;AAEO,QAAM,wBAAN,MAAM,+BAA8B,MAAM;MAU/C,YAAY,SAAiB,EAAE,MAAM,QAAQ,cAAc,WAAW,GAA4B;AAChG,cAAM,OAAO;AAYf,aAAO,WAAW,MAAM;AACtB,cAAIA,WAAU,IAAI,KAAK,IAAI;UAAc,KAAK,OAAO;SAAY,KAAK,MAAM;qBAAwB,KAAK,OAAO;YAC9G,CAAA,MAAK,KAAK,UAAU,CAAC;UACvB,CAAC;AAED,cAAI,KAAK,cAAc;AACrB,YAAAA,YAAW;kBAAqB,KAAK,YAAY;UACnD;AAEA,iBAAOA;QACT;AApBE,eAAO,eAAe,MAAM,uBAAsB,SAAS;AAE3D,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,eAAe;AACpB,aAAK,aAAa;AAClB,aAAK,aAAa;AAClB,aAAK,SAAS,YAAY,IAAI;MAChC;IAaF;AASO,QAAM,oBAAN,MAAM,2BAA0B,MAAM;MAiB3C,YAAY,SAAiB,EAAE,KAAK,GAAqB;AACvD,cAAM,SAAS;AACf,cAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,KAAK,MAAM,GAAG,GAAG;AACzD,cAAM,YAAY,QAAQ,QAAQ,OAAO,EAAE;AAC3C,cAAM,WAAW,GAAG,MAAM,IAAI,UAAU,KAAK,CAAC;;SAAc,IAAI;;AAChE,cAAM,QAAQ;AAehB,aAAO,WAAW,MAAM;AACtB,iBAAO,IAAI,KAAK,IAAI;UAAc,KAAK,OAAO;QAChD;AAfE,eAAO,eAAe,MAAM,mBAAkB,SAAS;AAEvD,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,oBAAoB;AACzB,aAAK,OAAO;MACd;IAUF;AAEO,QAAM,iBAAN,MAAM,wBAAuB,MAAM;MAGxC,YAAY,MAAc;AACxB,cAAM,IAAI;AACV,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,eAAO,eAAe,MAAM,gBAAe,SAAS;MACtD;IACF;AAEO,aAAS,iBAAiB,KAAmC;AAClE,aAAO,IAAI,SAAS;IACtB;AAOO,QAAM,qBAAqB;MAChC,SAAS;MACT,QAAQ;MACR,gBAAgB;IAClB;AAEO,QAAM,2BAA2B;MACtC,SAAS;MACT,QAAQ;MACR,gBAAgB;IAClB;AAEA,QAAM,kBAAkB,OAAO,OAAO;MACpC,6BAA6B;MAC7B,mCAAmC;MACnC,mCAAmC;MACnC,8BAA8B;MAC9B,sBAAsB;IACxB,CAAC;AA+BM,aAAS,kBAAkB,EAAE,aAAa,eAAe,GAAsC;AACpG,UAAI,MAAM;AAEV,YAAM,WAAW;QACf,GAAG;QACH,GAAG;MACL;AAEA,eAAS,aAAa,YAAoB,cAAgD;AACxF,YAAI,CAAC,cAAc;AACjB,iBAAO,GAAG,GAAG,KAAK,UAAU;QAC9B;AAEA,YAAI,MAAM;AACV,cAAM,UAAU,WAAW,SAAS,uBAAuB;AAE3D,mBAAW,SAAS,SAAS;AAC3B,gBAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS;AAC5D,gBAAM,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,WAAW;QAClD;AAEA,eAAO,GAAG,GAAG,KAAK,GAAG;MACvB;AAEA,aAAO;QACL,eAAe,EAAE,aAAAC,aAAY,GAAsC;AACjE,cAAI,OAAOA,iBAAgB,UAAU;AACnC,kBAAMA;UACR;AACA,iBAAO;QACT;QAEA,YAAY,EAAE,gBAAAC,gBAAe,GAAsC;AACjE,iBAAO,OAAO,UAAUA,mBAAkB,CAAC,CAAC;AAC5C,iBAAO;QACT;QAEA,gCAAgC,QAAiC;AAC/D,gBAAM,IAAI,MAAM,aAAa,SAAS,mCAAmC,MAAM,CAAC;QAClF;QAEA,qBAAqB,QAAiC;AACpD,gBAAM,IAAI,MAAM,aAAa,SAAS,6BAA6B,MAAM,CAAC;QAC5E;QAEA,kCAAyC;AACvC,gBAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;QAC1E;QAEA,6BAAoC;AAClC,gBAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;QACrE;QAEA,+BAA+B,QAAoC;AACjE,gBAAM,IAAI,MAAM,aAAa,SAAS,sBAAsB,MAAM,CAAC;QACrE;QAEA,MAAM,SAAwB;AAC5B,gBAAM,IAAI,MAAM,aAAa,OAAO,CAAC;QACvC;MACF;IACF;AAgBO,QAAM,qBAAN,cAAiC,kBAAkB;MAMxD,YAAY,SAAiB,EAAE,KAAK,GAAqC;AACvE,cAAM,SAAS,EAAE,KAAK,CAAC;AACvB,aAAK,OAAO;MACd;IACF;;;;;;;;;;;;;;;;;;;;;;;;;ACvWA,QAAA,qBAAA,CAAA;AAAA,aAAA,oBAAA;MAAA,YAAA,MAAA;MAAA,0BAAA,MAAA;MAAA,oBAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,kBAAA;ACWO,QAAM,oBAAoB,MAAe;AAC9C,UAAI;AACF,eAAO;MAET,QAAQ;MAAC;AAGT,aAAO;IACT;AAEO,QAAM,0BAA0B,MAAe;AACpD,UAAI;AACF,eAAO;MAET,QAAQ;MAAC;AAGT,aAAO;IACT;ADRA,QAAM,oBAAoB,oBAAI,IAAY;AACnC,QAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;AACjF,YAAM,cAAc,kBAAkB,KAAK,wBAAwB;AACnE,YAAM,YAAY,OAAO;AACzB,UAAI,kBAAkB,IAAI,SAAS,KAAK,aAAa;AACnD;MACF;AACA,wBAAkB,IAAI,SAAS;AAE/B,cAAQ;QACN,iCAAiC,MAAM;EAAmE,OAAO;MACnH;IACF;AAyBO,QAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,UAAgB;AAC9G,YAAM,SAAS,WAAW,MAAM,IAAI;AAEpC,UAAI,QAAQ,OAAO,QAAQ;AAC3B,aAAO,eAAe,QAAQ,UAAU;QACtC,MAAM;AACJ,qBAAW,UAAU,SAAS,GAAG,IAAI,IAAI,IAAI,QAAQ,EAAE;AACvD,iBAAO;QACT;QACA,IAAI,GAAY;AACd,kBAAQ;QACV;MACF,CAAC;IACH;AAYO,QAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;AACT,UAAI,QAAQ,IAAI,QAAQ;AACxB,aAAO,eAAe,KAAK,UAAU;QACnC,MAAM;AACJ,qBAAW,UAAU,SAAS,GAAG;AACjC,iBAAO;QACT;QACA,IAAI,GAAY;AACd,kBAAQ;QACV;MACF,CAAC;IACH;;;;;;;;;;;;;;;;;;;;;;;;;AEnGA,QAAA,wBAAA,CAAA;AAAA,aAAA,uBAAA;MAAA,0BAAA,MAAA;MAAA,kBAAA,MAAA;MAAA,cAAA,MAAA;MAAA,8BAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,qBAAA;AA4CA,QAAM,mBAAkC;MACtC,YAAY;QACV,cAAc;QACd,OAAO;MACT;MACA,QAAQ;QACN,cAAc;QACd,OAAO;MACT;MACA,UAAU;QACR,cAAc;QACd,OAAO;MACT;MACA,KAAK;QACH,cAAc;QACd,OAAO;MACT;IACF;AAEA,QAAM,iBAAiB,oBAAI,IAA8B,CAAC,gBAAgB,iBAAiB,cAAc,CAAC;AAE1G,QAAM,gBAAgB,oBAAI,IAA8B,CAAC,cAAc,UAAU,YAAY,KAAK,CAAC;AAGnG,QAAM,gBAAgB,CAAC,WAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,QAAM,eAAe,CAAC,UAAe,eAAe,IAAI,KAAK;AAC7D,QAAM,0BAA0B,CAAC,SAAc,cAAc,IAAI,IAAI;AAErE,QAAM,gBAAgB,CAAC,UAAkB,MAAM,QAAQ,YAAY,MAAM;AAOzE,QAAM,wBAA+C,CAAC,QAAQ,YAAY;AACxE,YAAM,EAAE,OAAO,SAAS,eAAe,IAAI;AAC3C,UAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,YAAY;AACtC,eAAO;MACT;AAEA,UAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;AACzC,eAAO;MACT;AAEA,UAAI,OAAO,YAAY;AACrB,eAAO,eAAe,SAAS,cAAc,OAAO,UAAU,CAAC;MACjE;AAEA,UAAI,OAAO,MAAM;AACf,eAAO,cAAc,OAAO,MAAM,cAAc,OAAO,IAAI;MAC7D;AACA,aAAO;IACT;AAEA,QAAM,wBAAwB,CAAC,OAAe,kBAA0B;AACtE,YAAM,EAAE,KAAK,aAAa,MAAM,aAAa,IAAI,aAAa,KAAK;AACnE,YAAM,CAAC,OAAO,GAAG,IAAI,cAAc,MAAM,GAAG;AAC5C,YAAM,KAAK,OAAO;AAElB,UAAI,UAAU,OAAO;AACnB,eAAO,YAAY,SAAS,EAAE;MAChC,WAAW,UAAU,QAAQ;AAC3B,eAAO,aAAa,SAAS,EAAE;MACjC,OAAO;AAEL,eAAO,CAAC,GAAG,aAAa,GAAG,YAAY,EAAE,SAAS,EAAE;MACtD;IACF;AAEA,QAAM,4BAAuD,CAAC,QAAQ,YAAY;AAChF,YAAM,EAAE,UAAU,MAAM,IAAI;AAE5B,UAAI,OAAO,WAAW,UAAU;AAC9B,eAAO,sBAAsB,UAAU,OAAO,OAAO;MACvD;AAEA,UAAI,OAAO,QAAQ,OAAO;AACxB,eAAO,sBAAsB,OAAO,OAAO,IAAI;MACjD;AACA,aAAO;IACT;AAEA,QAAM,eAAe,CAAC,QAAmC;AACvD,YAAM,WAAW,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI,CAAA,MAAK,EAAE,KAAK,CAAC,IAAI,CAAC;AAG5D,aAAO;QACL,KAAK,SAAS,OAAO,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;QACjF,MAAM,SAAS,OAAO,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;MACpF;IACF;AAEA,QAAM,+BAA+B,CAAC,WAAoD;AACxF,UAAI,CAAC,QAAQ;AACX,eAAO;MACT;AAEA,YAAM,wBAAwB,CAACC,YAAiC;AAC9D,YAAI,OAAOA,YAAW,UAAU;AAC9B,iBAAO,iBAAiBA,OAAM;QAChC;AACA,eAAOA;MACT;AAEA,YAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;AACvF,YAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;AAE/F,UAAI,sBAAsB,oBAAoB;AAC5C,eAAO,sBAAsB,KAAK,MAAM,MAAM;MAChD;AAEA,aAAO;IACT;AAQA,QAAM,mCAAqE,CAAC,QAAQ,EAAE,sBAAsB,MAAM;AAChH,UAAI,CAAC,OAAO,kBAAkB,CAAC,uBAAuB;AACpD,eAAO;MACT;AAEA,YAAM,wBAAwB,6BAA6B,OAAO,cAAc;AAChF,UAAI,CAAC,uBAAuB;AAC1B,eAAO;MACT;AAEA,YAAM,EAAE,OAAO,aAAa,IAAI,sBAAsB;AACtD,YAAM,CAAC,YAAY,UAAU,IAAI;AAIjC,YAAM,iBAAiB,eAAe,KAAK,eAAe,aAAa;AACvE,YAAM,iBAAiB,eAAe,KAAK,eAAe,aAAa;AAEvE,cAAQ,OAAO;QACb,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO,eAAe,KAAK,iBAAiB;QAC9C,KAAK;AACH,iBAAO,eAAe,KAAK,iBAAiB,kBAAkB;MAClE;IACF;AAQA,QAAM,2BAA2B,CAAC,YAA2E;AAC3G,aAAO,CAAC,WAAoB;AAC1B,YAAI,CAAC,QAAQ,QAAQ;AACnB,iBAAO;QACT;AAEA,cAAM,uBAAuB,0BAA0B,QAAQ,OAAO;AACtE,cAAM,mBAAmB,sBAAsB,QAAQ,OAAO;AAC9D,cAAM,8BAA8B,iCAAiC,QAAQ,OAAO;AAEpF,YAAI,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,KAAK,CAAA,MAAK,MAAM,IAAI,GAAG;AACjG,iBAAO,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,KAAK,CAAA,MAAK,MAAM,IAAI;QACrG;AAEA,eAAO,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,MAAM,CAAA,MAAK,MAAM,IAAI;MACtG;IACF;AAyBA,QAAM,mBAAmB,CAAC;MACxB,YAAY;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF;MACA,SAAS,EAAE,0BAA0B,KAAK;IAC5C,MAAmD;AACjD,UAAI,cAAc,UAAa,WAAW,QAAW;AACnD,eAAO;UACL,UAAU;UACV,YAAY;UACZ;UACA,eAAe;UACf;UACA,OAAO;UACP,OAAO;UACP,SAAS;UACT,SAAS;UACT,KAAK;UACL;UACA;QACF;MACF;AAEA,UAAI,cAAc,QAAQ,WAAW,MAAM;AACzC,eAAO;UACL,UAAU;UACV,YAAY;UACZ;UACA;UACA,eAAe;UACf,OAAO;UACP,OAAO;UACP,SAAS;UACT,SAAS;UACT,KAAK,MAAM;UACX;UACA;QACF;MACF;AAEA,UAAI,2BAA2B,kBAAkB,WAAW;AAC1D,eAAO;UACL,UAAU;UACV,YAAY;UACZ,WAAW;UACX,QAAQ;UACR,eAAe;UACf,OAAO;UACP,OAAO;UACP,SAAS;UACT,SAAS;UACT,KAAK,MAAM;UACX;UACA;QACF;MACF;AAEA,UAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;AACtE,eAAO;UACL,UAAU;UACV,YAAY;UACZ;UACA;UACA;UACA,OAAO,SAAS;UAChB;UACA;UACA,SAAS,WAAW;UACpB;UACA;UACA;QACF;MACF;AAEA,UAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;AACxD,eAAO;UACL,UAAU;UACV,YAAY;UACZ;UACA;UACA;UACA,OAAO,SAAS;UAChB,OAAO;UACP,SAAS;UACT,SAAS;UACT;UACA;UACA;QACF;MACF;IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrVA,QAAA,oBAAA,CAAA;AAAA,aAAA,mBAAA;MAAA,oBAAA,MAAA;MAAA,uBAAA,MAAA;MAAA,wBAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,+BAAA,MAAA;MAAA,8BAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,iBAAA;ACIO,QAAM,iBAAiB,CAAC,SAAiB;AAC9C,UAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,eAAO,KAAK,IAAI;MAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,eAAO,IAAI,OAAO,OAAO,MAAM,QAAQ,EAAE,SAAS;MACpD;AACA,aAAO;IACT;ACEA,QAAM,8BAA8B;AACpC,QAAM,8BAA8B;AAqB7B,aAAS,oBACd,KACA,UAA0F,CAAC,GACpE;AACvB,YAAM,OAAO;AAEb,UAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;AAClC,YAAI,QAAQ,SAAS,CAAC,KAAK;AACzB,gBAAM,IAAI;YACR;UACF;QACF;AACA,YAAI,QAAQ,SAAS,CAAC,iBAAiB,GAAG,GAAG;AAC3C,gBAAM,IAAI,MAAM,4BAA4B;QAC9C;AACA,eAAO;MACT;AAEA,YAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,UAAI,cAAc,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAGlD,oBAAc,YAAY,MAAM,GAAG,EAAE;AAErC,UAAI,QAAQ,UAAU;AACpB,sBAAc,QAAQ;MACxB,WAAW,iBAAiB,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAClF,sBAAc,SAAS,QAAQ,MAAM;MACvC;AAEA,aAAO;QACL;QACA;MACF;IACF;AAQO,aAAS,iBAAiB,MAAc,IAAI;AACjD,UAAI;AACF,cAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,cAAM,6BAA6B,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG;AAEvF,eAAO,kBAAkB;MAC3B,QAAQ;AACN,eAAO;MACT;IACF;ACtCO,aAAS,aAAa,KAAiC;AAC5D,aAAO,MAAM,IAAI,QAAQ,gBAAgB,CAAA,UAAS,MAAM,YAAY,EAAE,QAAQ,OAAO,EAAE,CAAC,IAAI;IAC9F;AAKO,aAAS,aAAa,KAAiC;AAC5D,aAAO,MAAM,IAAI,QAAQ,UAAU,CAAA,WAAU,IAAI,OAAO,YAAY,CAAC,EAAE,IAAI;IAC7E;AAEA,QAAM,8BAA8B,CAAC,cAAmB;AACtD,YAAM,gBAAgB,CAAC,QAAkB;AACvC,YAAI,CAAC,KAAK;AACR,iBAAO;QACT;AAEA,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,iBAAO,IAAI,IAAI,CAAA,OAAM;AACnB,gBAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,GAAG;AAC/C,qBAAO,cAAc,EAAE;YACzB;AACA,mBAAO;UACT,CAAC;QACH;AAEA,cAAM,OAAO,EAAE,GAAG,IAAI;AACtB,cAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,mBAAW,WAAW,MAAM;AAC1B,gBAAM,UAAU,UAAU,QAAQ,SAAS,CAAC;AAC5C,cAAI,YAAY,SAAS;AACvB,iBAAK,OAAO,IAAI,KAAK,OAAO;AAC5B,mBAAO,KAAK,OAAO;UACrB;AACA,cAAI,OAAO,KAAK,OAAO,MAAM,UAAU;AACrC,iBAAK,OAAO,IAAI,cAAc,KAAK,OAAO,CAAC;UAC7C;QACF;AACA,eAAO;MACT;AAEA,aAAO;IACT;AASO,QAAM,mBAAmB,4BAA4B,YAAY;AASjE,QAAM,mBAAmB,4BAA4B,YAAY;AAOjE,aAAS,SAAS,OAAyB;AAEhD,UAAI,OAAO,UAAU,WAAW;AAC9B,eAAO;MACT;AAGA,UAAI,UAAU,UAAa,UAAU,MAAM;AACzC,eAAO;MACT;AAGA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,YAAY,MAAM,QAAQ;AAClC,iBAAO;QACT;AAEA,YAAI,MAAM,YAAY,MAAM,SAAS;AACnC,iBAAO;QACT;MACF;AAGA,YAAM,SAAS,SAAS,OAAiB,EAAE;AAC3C,UAAI,MAAM,MAAM,GAAG;AACjB,eAAO;MACT;AAEA,UAAI,SAAS,GAAG;AACd,eAAO;MACT;AAGA,aAAO;IACT;ACnJA,QAAM,uBAAuB;AAJ7B,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAUO,QAAM,0BAAN,MAA8B;MAA9B,cAAA;AAAA,qBAAA,MAAA,kCAAA;AACL,qBAAA,MAAA,aAAc,2BAAA;AACd,qBAAA,MAAA,WAAY,oBAAA;MAAA;MAEZ,iBAAiB,SAAkC;;AACjD,YAAI,CAAC,aAAA,MAAK,oCAAA,kBAAA,GAAiB;AACzB,iBAAO;QACT;AAEA,cAAM,MAAM,KAAK,IAAI;AACrB,cAAM,MAAM,gBAAA,MAAK,oCAAA,cAAA,EAAL,KAAA,MAAkB,OAAA;AAC9B,cAAM,SAAQ,kBAAA,MAAK,oCAAA,SAAA,MAAL,mBAAc;AAE5B,YAAI,CAAC,OAAO;AACV,gBAAM,eAAe;YACnB,GAAG,aAAA,MAAK,oCAAA,SAAA;YACR,CAAC,GAAG,GAAG;UACT;AAEA,uBAAa,QAAQ,aAAA,MAAK,WAAA,GAAa,KAAK,UAAU,YAAY,CAAC;QACrE;AAEA,cAAM,mBAAmB,SAAS,MAAM,QAAQ,aAAA,MAAK,SAAA;AACrD,YAAI,kBAAkB;AACpB,gBAAM,eAAe,aAAA,MAAK,oCAAA,SAAA;AAC1B,iBAAO,aAAa,GAAG;AAEvB,uBAAa,QAAQ,aAAA,MAAK,WAAA,GAAa,KAAK,UAAU,YAAY,CAAC;QACrE;AAEA,eAAO,CAAC,CAAC;MACX;IAsEF;AApGE,kBAAA,oBAAA,QAAA;AACA,gBAAA,oBAAA,QAAA;AAFK,yCAAA,oBAAA,QAAA;AAqCL,qBAAY,SAAC,OAA+B;AAC1C,YAAM,EAAE,IAAI,KAAK,IAAI,KAAK,SAAS,GAAG,KAAK,IAAI;AAE/C,YAAM,iBAA4F;QAChG,GAAG;QACH,GAAG;MACL;AAEA,aAAO,KAAK;QACV,OAAO,KAAK;UACV,GAAG;UACH,GAAG;QACL,CAAC,EACE,KAAK,EACL,IAAI,CAAA,QAAO,eAAe,GAAG,CAAC;MACnC;IACF;AAEI,gBAAM,WAAkD;AAC1D,YAAM,cAAc,aAAa,QAAQ,aAAA,MAAK,WAAA,CAAW;AAEzD,UAAI,CAAC,aAAa;AAChB,eAAO,CAAC;MACV;AAEA,aAAO,KAAK,MAAM,WAAW;IAC/B;AASI,yBAAe,WAAY;AAC7B,UAAI,OAAO,WAAW,aAAa;AACjC,eAAO;MACT;AAEA,YAAM,UAAU,OAAO;AACvB,UAAI,CAAC,SAAS;AACZ,eAAO;MACT;AAEA,UAAI;AACF,cAAM,UAAU;AAChB,gBAAQ,QAAQ,SAAS,OAAO;AAChC,gBAAQ,WAAW,OAAO;AAE1B,eAAO;MACT,SAAS,KAAc;AACrB,cAAM,uBACJ,eAAe;SAEd,IAAI,SAAS,wBAAwB,IAAI,SAAS;AAErD,YAAI,wBAAwB,QAAQ,SAAS,GAAG;AAC9C,kBAAQ,WAAW,aAAA,MAAK,WAAA,CAAW;QACrC;AAEA,eAAO;MACT;IACF;ACtEF,QAAM,iBAAoD;MACxD,cAAc;MACd,eAAe;;;;MAIf,UAAU;IACZ;AA/CA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAAA,QAAA;AAiDO,QAAM,qBAAN,MAAgE;MAOrE,YAAY,SAAoC;AAP3C,qBAAA,MAAA,6BAAA;AACL,qBAAA,MAAA,OAAA;AACA,qBAAA,MAAA,eAAA;AACA,qBAAA,MAAA,WAA+B,CAAC,CAAA;AAChC,qBAAA,MAAA,SAA4B,CAAC,CAAA;AAC7B,qBAAA,MAAA,aAAA;AAGE,qBAAA,MAAK,SAAU;UACb,eAAe,QAAQ,iBAAiB,eAAe;UACvD,cAAc,QAAQ,gBAAgB,eAAe;UACrD,UAAU,QAAQ,YAAY;UAC9B,OAAO,QAAQ,SAAS;UACxB,UAAU,eAAe;QAC3B,CAAA;AAEA,YAAI,CAAC,QAAQ,gBAAgB,OAAO,WAAW,aAAa;AAE1D,uBAAA,MAAK,SAAA,EAAU,eAAe;QAChC,OAAO;AACL,uBAAA,MAAK,SAAA,EAAU,eAAe,QAAQ,gBAAgB;QACxD;AAIA,qBAAA,MAAK,SAAA,EAAU,MAAM,QAAQ;AAE7B,qBAAA,MAAK,SAAA,EAAU,aAAa,QAAQ;AAEpC,qBAAA,MAAK,SAAA,EAAU,iBAAiB,QAAQ,kBAAkB;AAE1D,cAAM,YAAY,oBAAoB,QAAQ,cAAc;AAC5D,YAAI,WAAW;AACb,uBAAA,MAAK,SAAA,EAAU,eAAe,UAAU;QAC1C;AAEA,YAAI,QAAQ,WAAW;AAErB,uBAAA,MAAK,SAAA,EAAU,YAAY,QAAQ,UAAU,UAAU,GAAG,EAAE;QAC9D;AAEA,qBAAA,MAAK,iBAAkB,IAAI,wBAAwB,CAAA;MACrD;MAEA,IAAI,YAAqB;;AACvB,YAAI,aAAA,MAAK,SAAA,EAAU,iBAAiB,eAAe;AACjD,iBAAO;QACT;AAIA,YAAI,aAAA,MAAK,OAAA,EAAQ,YAAa,OAAO,YAAY,eAAe,SAAS,QAAQ,IAAI,wBAAwB,GAAI;AAC/G,iBAAO;QACT;AAKA,YAAI,OAAO,WAAW,eAAe,CAAC,GAAC,sCAAQ,cAAR,mBAAmB,YAAW;AACnE,iBAAO;QACT;AAEA,eAAO;MACT;MAEA,IAAI,UAAmB;AACrB,eAAO,aAAA,MAAK,OAAA,EAAQ,SAAU,OAAO,YAAY,eAAe,SAAS,QAAQ,IAAI,qBAAqB;MAC5G;MAEA,OAAO,OAAgC;AACrC,cAAM,kBAAkB,gBAAA,MAAK,+BAAA,iBAAA,EAAL,KAAA,MAAqB,MAAM,OAAO,MAAM,OAAA;AAEhE,wBAAA,MAAK,+BAAA,WAAA,EAAL,KAAA,MAAe,gBAAgB,OAAO,eAAA;AAEtC,YAAI,CAAC,gBAAA,MAAK,+BAAA,eAAA,EAAL,KAAA,MAAmB,iBAAiB,MAAM,iBAAA,GAAoB;AACjE;QACF;AAEA,qBAAA,MAAK,OAAA,EAAQ,KAAK,eAAe;AAEjC,wBAAA,MAAK,+BAAA,gBAAA,EAAL,KAAA,IAAA;MACF;IAgIF;AAhNE,cAAA,oBAAA,QAAA;AACA,sBAAA,oBAAA,QAAA;AACA,gBAAA,oBAAA,QAAA;AACA,cAAA,oBAAA,QAAA;AACA,oBAAA,oBAAA,QAAA;AALK,oCAAA,oBAAA,QAAA;AAmFL,sBAAa,SAAC,iBAAiC,mBAA4B;AACzE,aAAO,KAAK,aAAa,CAAC,KAAK,WAAW,gBAAA,MAAK,+BAAA,kBAAA,EAAL,KAAA,MAAsB,iBAAiB,iBAAA;IACnF;AAEA,yBAAgB,SAAC,iBAAiC,mBAA4B;AAC5E,YAAM,aAAa,KAAK,OAAO;AAE/B,YAAM,cACJ,cAAc,aAAA,MAAK,OAAA,EAAQ,iBAC1B,OAAO,sBAAsB,eAAe,cAAc;AAE7D,UAAI,CAAC,aAAa;AAChB,eAAO;MACT;AAEA,aAAO,CAAC,aAAA,MAAK,eAAA,EAAgB,iBAAiB,eAAe;IAC/D;AAEA,uBAAc,WAAS;AAErB,UAAI,OAAO,WAAW,aAAa;AACjC,wBAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;AACA;MACF;AAEA,YAAM,eAAe,aAAA,MAAK,OAAA,EAAQ,UAAU,aAAA,MAAK,OAAA,EAAQ;AACzD,UAAI,cAAc;AAGhB,YAAI,aAAA,MAAK,aAAA,GAAe;AACtB,gBAAM,SAAS,OAAO,uBAAuB,cAAc,qBAAqB;AAChF,iBAAO,aAAA,MAAK,aAAA,CAAa;QAC3B;AACA,wBAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;AACA;MACF;AAGA,UAAI,aAAA,MAAK,aAAA,GAAe;AACtB;MACF;AAEA,UAAI,yBAAyB,QAAQ;AACnC,qBAAA,MAAK,eAAgB,oBAAoB,MAAM;AAC7C,0BAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;QACF,CAAC,CAAA;MACH,OAAO;AAEL,qBAAA,MAAK,eAAgB,WAAW,MAAM;AACpC,0BAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;QACF,GAAG,CAAC,CAAA;MACN;IACF;AAEA,eAAM,WAAS;AACb,YAAM,IAAI,IAAI,aAAa,aAAA,MAAK,OAAA,EAAQ,QAAQ,GAAG;QACjD,QAAQ;;QAER,MAAM,KAAK,UAAU;UACnB,QAAQ,aAAA,MAAK,OAAA;QACf,CAAC;QACD,SAAS;UACP,gBAAgB;QAClB;MACF,CAAC,EACE,MAAM,MAAM,MAAM,EAClB,KAAK,MAAM;AACV,qBAAA,MAAK,SAAU,CAAC,CAAA;MAClB,CAAC,EACA,MAAM,MAAM,MAAM;IACvB;AAKA,kBAAS,SAAC,OAAgC,SAA8B;AACtE,UAAI,CAAC,KAAK,SAAS;AACjB;MACF;AAEA,UAAI,OAAO,QAAQ,mBAAmB,aAAa;AACjD,gBAAQ,eAAe,qBAAqB,KAAK;AACjD,gBAAQ,IAAI,OAAO;AACnB,gBAAQ,SAAS;MACnB,OAAO;AACL,gBAAQ,IAAI,qBAAqB,OAAO,OAAO;MACjD;IACF;AAOA,wBAAe,WAAG;AAChB,UAAI,cAAc;QAChB,MAAM,aAAA,MAAK,SAAA,EAAU;QACrB,SAAS,aAAA,MAAK,SAAA,EAAU;MAC1B;AAGA,UAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AAEjD,sBAAc,EAAE,GAAG,aAAa,GAAG,OAAO,MAAM,YAAY,YAAY;MAC1E;AAEA,aAAO;IACT;AAKA,wBAAe,SAAC,OAAgC,SAAoD;AAClG,YAAM,cAAc,gBAAA,MAAK,+BAAA,iBAAA,EAAL,KAAA,IAAA;AAEpB,aAAO;QACL;QACA,IAAI,aAAA,MAAK,SAAA,EAAU,gBAAgB;QACnC,IAAI,aAAA,MAAK,SAAA,EAAU,gBAAgB;QACnC,KAAK,YAAY;QACjB,MAAM,YAAY;QAClB,GAAI,aAAA,MAAK,SAAA,EAAU,iBAAiB,EAAE,IAAI,aAAA,MAAK,SAAA,EAAU,eAAe,IAAI,CAAC;QAC7E,GAAI,aAAA,MAAK,SAAA,EAAU,YAAY,EAAE,IAAI,aAAA,MAAK,SAAA,EAAU,UAAU,IAAI,CAAC;QACnE;MACF;IACF;AC/PF,QAAM,0BAA0B;AAChC,QAAM,yBAAyB;AAC/B,QAAM,sBAAsB;AAe5B,aAAS,6BAA6B,OAAuE;AAC3G,aAAO,SACL,WACA,OACA,mBAC2C;;AAC3C,eAAO;UACL;UACA,mBAAmB;UACnB,SAAS;YACP;YACA,gBAAgB,QAAQ,+BAAO,UAAU;YACzC,WAAW,SAAQ,oCAAO,eAAP,mBAAmB,SAAS;YAC/C,UAAU,SAAQ,oCAAO,eAAP,mBAAmB,QAAQ;YAC7C,WAAW,SAAQ,oCAAO,eAAP,mBAAmB,SAAS;YAC/C,GAAG;UACL;QACF;MACF;IACF;AAYO,aAAS,8BACd,WACA,OACA,mBAC2C;AAC3C,aAAO,6BAA6B,uBAAuB,EAAE,WAAW,OAAO,iBAAiB;IAClG;AAYO,aAAS,6BACd,WACA,OACA,mBAC2C;AAC3C,aAAO,6BAA6B,sBAAsB,EAAE,WAAW,OAAO,iBAAiB;IACjG;AAaO,aAAS,sBACd,WACA,QAAsC,CAAC,GACG;AAC1C,aAAO;QACL,OAAO;QACP,mBAAmB;QACnB,SAAS;UACP;UACA,GAAG;QACL;MACF;IACF;ACjGA,QAAM,sBAAsB;AASrB,aAAS,kBACd,QACA,SACsC;AACtC,aAAO;QACL,OAAO;QACP,SAAS;UACP;UACA,GAAG;QACL;MACF;IACF;ACpBA,QAAM,2BAA2B;AACjC,QAAMC,uBAAsB;AAOrB,aAAS,uBAAuB,SAA4E;AACjH,aAAO;QACL,OAAO;QACP,mBAAmBA;QACnB;MACF;IACF;;;;;;;;;;;;;;;;;;;;;;;;;AChBA,QAAA,iBAAA,CAAA;AAAA,aAAA,gBAAA;MAAA,4BAAA,MAAA;MAAA,iCAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,6BAAA,MAAA;MAAA,kBAAA,MAAA;MAAA,qCAAA,MAAA;MAAA,sCAAA,MAAA;MAAA,8BAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,cAAA;AAAA,QAAM,qBAAqB,CAAC,QAAgB;AAC1C,aAAO,aAAa,IAAI,KAAK,CAAC;;;;;;IAMhC;AAEA,QAAM,aAAa;;;;;;;;;;;;;;;;;;AAmBZ,QAAM,6BAA6B,mBAAmB;;;EAG3D,UAAU;CACX;AAEM,QAAM,oBAAoB,mBAAmB;;;EAGlD,UAAU;CACX;AAEM,QAAM,8BAA8B,mBAAmB;;;;;;;;CAQ7D;AAEM,QAAM,kCAAkC,mBAAmB;;;;;;;;;;;;;;;;;;;CAmBjE;AAEM,QAAM,mBAAmB,mBAAmB;;;;CAIlD;AAEM,QAAM,uCAAuC;MAClD;IACF;AAEO,QAAM,+BAA+B,mBAAmB;;uFAEwB;AAEhF,QAAM,sCAAsC,mBAAmB;;;;;;;;;CASrE;;;;;;;;;;;;;;;;;;;;;;;;;AC9FD,QAAA,gBAAA,CAAA;AAAA,aAAA,eAAA;MAAA,YAAA,MAAA;MAAA,uBAAA,MAAA;MAAA,sBAAA,MAAA;MAAA,yBAAA,MAAA;MAAA,iBAAA,MAAA;MAAA,0BAAA,MAAA;MAAA,yBAAA,MAAA;MAAA,WAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,mBAAA,MAAA;MAAA,MAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,aAAA;ACAO,QAAM,OAAO,IAAI,UAAuB;IAE/C;ACQO,QAAM,wBAAwB,MAAM;AACzC,UAAI,UAAoB;AACxB,UAAI,SAAmB;AACvB,YAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxC,kBAAU;AACV,iBAAS;MACX,CAAC;AACD,aAAO,EAAE,SAAS,SAAS,OAAO;IACpC;ACdO,aAAS,WACd,UACsF;AACtF,YAAM,WAAW,MAAM,KAAK,QAAQ,EAAE;QAAI,CAAA,MACxC,EAAE;UACA,CAAA,WAAU,EAAE,QAAQ,aAAa,MAAM;UACvC,CAAA,YAAW,EAAE,QAAQ,YAAY,OAAO;QAC1C;MACF;AACA,aAAO,QAAQ,IAAI,QAAQ;IAC7B;ACXO,aAAS,UAAU,aAA8B;AACtD,aACE,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,iBAAiB,KACtC,YAAY,SAAS,oBAAoB;IAE7C;ACVO,QAAM,2BAA2B,MAAe;AACrD,UAAI;AACF,eAAO;MAET,QAAQ;MAAC;AAIT,aAAO;IACT;AAEO,QAAM,oBAAoB,MAAe;AAC9C,UAAI;AACF,eAAO;MAET,QAAQ;MAAC;AAGT,aAAO;IACT;AAEO,QAAM,0BAA0B,MAAe;AACpD,UAAI;AACF,eAAO;MAET,QAAQ;MAAC;AAGT,aAAO;IACT;AC3BO,QAAM,oBAAoB,CAAC,YAAoB;AACpD,UAAI,yBAAyB,GAAG;AAC9B,gBAAQ,MAAM,UAAU,OAAO,EAAE;MACnC;IACF;ACHO,aAAS,gBAAmB,OAAyB,KAAU,cAAiC;AACrG,UAAI,OAAO,UAAU,YAAY;AAC/B,eAAQ,MAAwB,GAAG;MACrC;AAEA,UAAI,OAAO,UAAU,aAAa;AAChC,eAAO;MACT;AAEA,UAAI,OAAO,iBAAiB,aAAa;AACvC,eAAO;MACT;AAEA,aAAO;IACT;ACZO,QAAM,0BAA0B,CACrC,QACA,WACG;AACH,UAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;MACF;AAEA,iBAAW,OAAO,QAAQ;AACxB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,cAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,mBAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;UACrE;AACA,kCAAwB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;QAClD,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC5D,iBAAO,GAAG,IAAI,OAAO,GAAG;QAC1B;MACF;IACF;AAEO,QAAM,uBAAuB,CAClC,QACA,WACG;AACH,UAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;MACF;AAEA,iBAAW,OAAO,QAAQ;AACxB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,cAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,mBAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;UACrE;AACA,+BAAqB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;QAC/C,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAW;AACzF,iBAAO,GAAG,IAAI,OAAO,GAAG;QAC1B;MACF;IACF;;;;;;;;;;;;;;;;;;;;;;;;;AC3CA,QAAA,eAAA,CAAA;AAAA,aAAA,cAAA;MAAA,qBAAA,MAAA;MAAA,4BAAA,MAAA;MAAA,iBAAA,MAAA;MAAA,uBAAA,MAAA;MAAA,iCAAA,MAAA;MAAA,4BAAA,MAAA;MAAA,gCAAA,MAAA;MAAA,2BAAA,MAAA;MAAA,kBAAA,MAAA;MAAA,qBAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,YAAA;ACAO,QAAM,+BAA+B,CAAC,YAAY,iBAAiB,eAAe;AAElF,QAAM,0BAA0B;MACrC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;ACRO,QAAM,iBAAiB,CAAC,SAAiB;AAC9C,UAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,eAAO,KAAK,IAAI;MAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,eAAO,IAAI,OAAO,OAAO,MAAM,QAAQ,EAAE,SAAS;MACpD;AACA,aAAO;IACT;ACXO,QAAM,iBAAiB,CAAC,SAAiB;AAC9C,UAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,eAAO,KAAK,IAAI;MAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,eAAO,IAAI,OAAO,OAAO,IAAI,EAAE,SAAS,QAAQ;MAClD;AACA,aAAO;IACT;AHMA,QAAM,8BAA8B;AACpC,QAAM,8BAA8B;AAGpC,QAAM,qCAAqC;AAEpC,aAAS,oBAAoB,aAA6B;AAC/D,YAAM,WACJ,mCAAmC,KAAK,WAAW,KAClD,YAAY,WAAW,QAAQ,KAAK,6BAA6B,KAAK,CAAA,MAAK,YAAY,SAAS,CAAC,CAAC;AACrG,YAAM,YAAY,WAAW,8BAA8B;AAC3D,aAAO,GAAG,SAAS,GAAG,eAAe,GAAG,WAAW,GAAG,CAAC;IACzD;AAUO,aAAS,oBACd,KACA,UAA0F,CAAC,GACpE;AACvB,YAAM,OAAO;AAEb,UAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;AAClC,YAAI,QAAQ,SAAS,CAAC,KAAK;AACzB,gBAAM,IAAI;YACR;UACF;QACF;AACA,YAAI,QAAQ,SAAS,CAAC,iBAAiB,GAAG,GAAG;AAC3C,gBAAM,IAAI,MAAM,4BAA4B;QAC9C;AACA,eAAO;MACT;AAEA,YAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,UAAI,cAAc,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAGlD,oBAAc,YAAY,MAAM,GAAG,EAAE;AAErC,UAAI,QAAQ,UAAU;AACpB,sBAAc,QAAQ;MACxB,WAAW,iBAAiB,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAClF,sBAAc,SAAS,QAAQ,MAAM;MACvC;AAEA,aAAO;QACL;QACA;MACF;IACF;AAQO,aAAS,iBAAiB,MAAc,IAAI;AACjD,UAAI;AACF,cAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,cAAM,6BAA6B,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG;AAEvF,eAAO,kBAAkB;MAC3B,QAAQ;AACN,eAAO;MACT;IACF;AAEO,aAAS,6BAA6B;AAC3C,YAAM,uBAAuB,oBAAI,IAAqB;AAEtD,aAAO;QACL,mBAAmB,CAAC,QAA+B;AACjD,cAAI,CAAC,KAAK;AACR,mBAAO;UACT;AAEA,gBAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI;AACrD,cAAI,MAAM,qBAAqB,IAAI,QAAQ;AAC3C,cAAI,QAAQ,QAAW;AACrB,kBAAM,wBAAwB,KAAK,CAAA,MAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,iCAAqB,IAAI,UAAU,GAAG;UACxC;AACA,iBAAO;QACT;MACF;IACF;AAEO,aAAS,gCAAgC,QAAyB;AACvE,aAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;IACnE;AAEO,aAAS,+BAA+B,QAAyB;AACtE,aAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;IACnE;AAEO,aAAS,2BAA2B,QAAyB;AAClE,aAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;IACnE;AAEO,aAAS,0BAA0B,QAAyB;AACjE,aAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;IACnE;AAEA,mBAAsB,gBACpB,gBACA,SAAuB,WAAW,OAAO,QACxB;AACjB,YAAM,OAAO,IAAI,YAAY,EAAE,OAAO,cAAc;AACpD,YAAM,SAAS,MAAM,OAAO,OAAO,SAAS,IAAI;AAChD,YAAM,eAAe,OAAO,aAAa,GAAG,IAAI,WAAW,MAAM,CAAC;AAElE,aAAO,eAAe,YAAY,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,UAAU,GAAG,CAAC;IAC9F;AAEO,QAAM,wBAAwB,CAAC,YAAoB,iBAAiC;AACzF,aAAO,GAAG,UAAU,IAAI,YAAY;IACtC;;;;;;;;;;;;;;;;;;;;;;;;;AI3IA,QAAA,gBAAA,CAAA;AAAA,aAAA,eAAA;MAAA,cAAA,MAAA;MAAA,+BAAA,MAAA;MAAA,uBAAA,MAAA;MAAA,gBAAA,MAAA;MAAA,WAAA,MAAA;MAAA,YAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,aAAA;AAGA,QAAA,gBAAmG;AAE5F,aAAS,WAAW,KAA6B;AACtD,UAAI,CAAC,OAAO,CAAC,IAAI,wBAAwB;AACvC,gBAAQ,KAAK,cAAA,iBAAiB;MAChC;IACF;AAEO,aAAS,aAAa,MAAW,cAA8C;AACpF,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,cAAM,IAAI,MAAM,YAAY;MAC9B;IACF;AAEO,aAAS,sBAAsB,KAAiD;AACrF,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,cAAA,iBAAiB;MACnC;AACA,UAAI,CAAC,CAAC,OAAO,CAAC,IAAI,wBAAwB;AACxC,cAAM,IAAI,MAAM,cAAA,0BAA0B;MAC5C;IACF;AAEO,aAAS,8BAA8B,KAAiC;AAC7E,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,cAAM,IAAI,MAAM,cAAA,mCAAmC;MACrD;IACF;AAKA,QAAM,4BAA4B,CAAC,YAA2D;AAnC9F,UAAA;AAoCE,aAAO,CAAC,GAAC,KAAA,WAAA,OAAA,SAAA,QAAS,eAAT,OAAA,SAAA,GAAqB;IAChC;AAEA,QAAM,uBAAuB,CAAC,YAA2C;AACvE,aAAO,CAAC,EAAC,WAAA,OAAA,SAAA,QAAS;IACpB;AASO,QAAM,iBAAiB,CAAC,MAAc,YAAgD;AAE3F,UAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI,MAAM,UAAU;AAC1F,eAAO,QAAQ,IAAI,IAAI;MACzB;AAIA,UAAI,0BAA0B,OAAO,GAAG;AACtC,eAAO,QAAQ,WAAW,IAAI,IAAI,KAAK;MACzC;AAGA,UAAI,qBAAqB,OAAO,GAAG;AACjC,eAAO,QAAQ,IAAI,IAAI,KAAK;MAC9B;AAGA,UAAI,WAAW,OAAO,QAAQ,IAAI,MAAM,UAAU;AAChD,eAAO,QAAQ,IAAI;MACrB;AAGA,UAAI;AACF,eAAO,WAAW,IAA+B;MACnD,QAAQ;MAER;AAEA,aAAO;IACT;AAEO,QAAM,YAAY,MAAe;AAlFxC,UAAA;AAmFE,UAAI,OAAO,WAAW,eAAe,SAAO,KAAA,OAAO,mBAAP,OAAA,SAAA,GAAuB,eAAc,aAAa;AAC5F,eAAO,OAAO,eAAe;MAC/B;AACA,aAAO;IACT;;;", "names": ["message", "packageName", "customMessages", "config", "EVENT_SAMPLING_RATE"]}