import {
  require_react
} from "./chunk-M4GN2IAG.js";
import {
  __toESM
} from "./chunk-QGSYD46Z.js";

// node_modules/remix-utils/build/react/use-hydrated.js
var import_react = __toESM(require_react());
function subscribe() {
  return () => {
  };
}
function useHydrated() {
  return (0, import_react.useSyncExternalStore)(subscribe, () => true, () => false);
}
export {
  useHydrated
};
//# sourceMappingURL=remix-utils_use-hydrated.js.map
