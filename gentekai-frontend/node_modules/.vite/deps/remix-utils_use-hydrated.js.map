{"version": 3, "sources": ["../../remix-utils/build/react/use-hydrated.js"], "sourcesContent": ["import { useSyncExternalStore } from \"react\";\nfunction subscribe() {\n    return () => { };\n}\n/**\n * Return a boolean indicating if the JS has been hydrated already.\n * When doing Server-Side Rendering, the result will always be false.\n * When doing Client-Side Rendering, the result will always be false on the\n * first render and true from then on. Even if a new component renders it will\n * always start with true.\n *\n * Example: Disable a button that needs JS to work.\n * ```tsx\n * let hydrated = useHydrated();\n * return (\n *   <button type=\"button\" disabled={!hydrated} onClick={doSomethingCustom}>\n *     Click me\n *   </button>\n * );\n * ```\n */\nexport function useHydrated() {\n    return useSyncExternalStore(subscribe, () => true, () => false);\n}\n"], "mappings": ";;;;;;;;AAAA,mBAAqC;AACrC,SAAS,YAAY;AACjB,SAAO,MAAM;AAAA,EAAE;AACnB;AAkBO,SAAS,cAAc;AAC1B,aAAO,mCAAqB,WAAW,MAAM,MAAM,MAAM,KAAK;AAClE;", "names": []}