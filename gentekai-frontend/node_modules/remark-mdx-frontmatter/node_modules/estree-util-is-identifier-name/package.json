{"name": "estree-util-is-identifier-name", "version": "1.1.0", "description": "Check if something can be an ecmascript (javascript) identifier name", "license": "MIT", "keywords": ["estree", "ast", "ecmascript", "javascript", "tree", "identifier", "character"], "repository": "wooorm/estree-util-is-identifier-name", "bugs": "https://github.com/wooorm/estree-util-is-identifier-name/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js", "regex.js", "*.d.ts"], "devDependencies": {"@types/tape": "^4.0.0", "@unicode/unicode-13.0.0": "^1.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "regenerate": "^1.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "rimraf": "^3.0.0", "tape": "^5.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.38.0"}, "scripts": {"generate": "node build", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "build": "rimraf \"*.d.ts\" && tsc && type-coverage", "test": "npm run generate && npm run format && npm run test-coverage && npm run build", "prepack": "npm run build && npm run format"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"no-misleading-character-class": "off", "no-useless-escape": "off", "unicorn/no-fn-reference-in-iterator": "off", "unicorn/no-hex-escape": "off", "unicorn/better-regex": "off"}}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true}}