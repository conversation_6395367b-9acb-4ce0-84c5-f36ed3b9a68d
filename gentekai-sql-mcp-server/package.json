{"name": "express-typescript-boilerplate", "version": "1.0.14", "description": "An Express boilerplate backend", "author": "Tekai Oy", "main": "index.ts", "private": true, "scripts": {"start:dev": "node --import=tsx --watch src/index.ts", "start:prod": "node dist/index.js", "lint": "biome lint --fix", "format": "biome format --write", "test": "vitest run", "test:cov": "vitest run --coverage", "check": "pnpm lint && pnpm format && pnpm build && pnpm test", "prisma:generate": "prisma generate", "build": "prisma generate && tsc && tsup", "migrate": "prisma migrate deploy"}, "dependencies": {"@asteasolutions/zod-to-openapi": "7.3.0", "@modelcontextprotocol/sdk": "^1.12.0", "@prisma/client": "6.8.2", "@sequelize/core": "7.0.0-alpha.46", "cors": "2.8.5", "dotenv": "16.5.0", "express": "5.1.0", "express-rate-limit": "7.5.0", "helmet": "8.1.0", "http-status-codes": "2.3.0", "pg": "^8.16.0", "pino": "9.7.0", "pino-http": "10.4.0", "prisma": "^6.8.2", "sequelize": "^6.37.7", "swagger-ui-express": "5.0.1", "zod": "3.25.23"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/cors": "2.8.18", "@types/express": "5.0.2", "@types/sequelize": "^4.28.20", "@types/supertest": "6.0.3", "@types/swagger-ui-express": "4.1.8", "@vitest/coverage-v8": "3.1.4", "pino-pretty": "13.0.0", "supertest": "7.1.1", "tsup": "8.5.0", "tsx": "4.19.4", "typescript": "5.8.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.1.4"}, "tsup": {"entry": ["src/index.ts"], "outDir": "dist", "format": ["esm", "cjs"], "target": "es2020", "sourcemap": true, "clean": true, "dts": true, "splitting": false, "skipNodeModulesBundle": true}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@prisma/client", "@prisma/engines", "@scarf/scarf", "esbuild", "prisma"]}, "packageManager": "pnpm@10.11.0"}